import React, {useMemo, useState} from 'react';
import {BaseEntityTable} from '../components/table/BaseEntityTable';
import {useUserCrud} from '../hooks/useUserCrud';
import type {AdminChangePasswordDto, CreateUserDto, UpdateUserDto, User} from '../types/user';
import type {Role} from '../types/role';
import {useRoleCrud} from '../hooks/useRoleCrud';
import {usePermissions} from '../hooks/usePermissions';
import {ChangePasswordModal} from '../components/form/ChangePasswordModal';
import {type ColumnDef} from '@tanstack/react-table';
import {Lock} from 'lucide-react';

const Users: React.FC = () => {
    // Estados para paginación y búsqueda
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    // Estados para el modal de cambio de contraseña
    const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] = useState(false);
    const [selectedUserForPassword, setSelectedUserForPassword] = useState<User | null>(null);

    // Hook para operaciones CRUD de usuarios
    const {
        useGetAll,
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,
        useAdminChangePassword,
    } = useUserCrud();

    // Hook para obtener roles (para el selector)
    const {useGetActive} = useRoleCrud();

    // Hook para permisos
    const {isAdmin} = usePermissions();

    // Queries
    const {data: usersData, isLoading} = useGetAll(pageNumber, pageSize, false);
    const {data: rolesData} = useGetActive(1, 100);

    // Mutations
    const createUserMutation = useCreate();
    const updateUserMutation = useUpdate();
    const deleteUserMutation = useDelete();
    const activateUserMutation = useActivate();
    const deactivateUserMutation = useDeactivate();
    const changePasswordMutation = useAdminChangePassword();

    // Handlers
    const handleCreate = async (data: CreateUserDto) => {
        await createUserMutation.mutateAsync(data);
    };

    const handleUpdate = async (id: number, data: UpdateUserDto) => {
        await updateUserMutation.mutateAsync({id, data});
    };

    const handleDelete = async (id: number) => {
        await deleteUserMutation.mutateAsync(id);
    };

    const handleActivate = async (id: number) => {
        await activateUserMutation.mutateAsync(id);
    };

    const handleDeactivate = async (id: number) => {
        await deactivateUserMutation.mutateAsync(id);
    };

    const handleChangePassword = async (data: AdminChangePasswordDto) => {
        await changePasswordMutation.mutateAsync(data);
    };

    const handleOpenChangePasswordModal = (user: User) => {
        setSelectedUserForPassword(user);
        setIsChangePasswordModalOpen(true);
    };

    const handleCloseChangePasswordModal = () => {
        setIsChangePasswordModalOpen(false);
        setSelectedUserForPassword(null);
    };

    const handleSearch = () => {
        setPageNumber(1); // Reset to first page when searching
    };

    const handlePageChange = (page: number) => {
        setPageNumber(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setPageNumber(1); // Reset to first page when changing page size
    };

    // Columnas personalizadas para User
    const customColumns: ColumnDef<User>[] = useMemo(() => [
        {
            accessorKey: 'employeeNumber',
            header: 'Número de Empleado',
            cell: ({row}) => (
                <span style={{color: 'var(--text-primary)'}}>
          {row.getValue('employeeNumber')}
        </span>
            ),
            size: 150,
        },
        {
            accessorKey: 'email',
            header: 'Email',
            cell: ({row}) => (
                <span style={{color: 'var(--text-primary)'}}>
          {row.getValue('email') || '-'}
        </span>
            ),
            size: 200,
        },
        {
            accessorKey: 'roleName',
            header: 'Rol',
            cell: ({row}) => (
                <span style={{color: 'var(--text-primary)'}}>
          {row.getValue('roleName') || 'Sin rol asignado'}
        </span>
            ),
            size: 150,
        },
    ], []);

    // Renderizar acciones adicionales (solo el botón de cambio de contraseña)
    const renderAdditionalActions = (user: User) => (
        <>
            {/* Botón de cambio de contraseña solo para administradores */}
            {isAdmin() && (
                <button
                    onClick={() => handleOpenChangePasswordModal(user)}
                    className="p-1 text-purple-600 hover:text-purple-800 transition-colors duration-200"
                    title="Cambiar contraseña"
                >
                    <Lock className="h-4 w-4"/>
                </button>
            )}
        </>
    );

    // Renderizar campos del formulario
    const renderFormFields = (data?: User) => (
        <div className="space-y-4">
            {/* Campo oculto para el ID en modo de edición */}
            {data?.id && (
                <input
                    type="hidden"
                    name="id"
                    value={data.id}
                />
            )}

            <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                    Nombre *
                </label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    defaultValue={data?.name || ''}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ingrese el nombre completo"
                />
            </div>

            <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                    Email *
                </label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    defaultValue={data?.email || ''}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                />
            </div>

            <div>
                <label htmlFor="employeeNumber" className="block text-sm font-medium mb-1">
                    Número de Empleado *
                </label>
                <input
                    type="text"
                    id="employeeNumber"
                    name="employeeNumber"
                    defaultValue={data?.employeeNumber || ''}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="EMP001"
                />
            </div>

            <div>
                <label htmlFor="roleId" className="block text-sm font-medium mb-1">
                    Rol *
                </label>
                <select
                    id="roleId"
                    name="roleId"
                    defaultValue={data?.roleId?.toString() || ''}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    <option style={{background: 'var(--bg-color)'}} value="">Seleccione un rol</option>
                    {rolesData?.data?.map((role: Role) => (
                        <option style={{background: 'var(--bg-color)'}} key={role.id} value={role.id}>
                            {role.name}
                        </option>
                    ))}
                </select>
            </div>

            {/* Campo de contraseña solo para crear nuevos usuarios */}
            {!data && (
                <div>
                    <label htmlFor="password" className="block text-sm font-medium mb-1">
                        Contraseña *
                    </label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Ingrese la contraseña"
                    />
                </div>
            )}
        </div>
    );

    // Renderizar detalles del usuario
    const renderDetails = (user: User) => (
        <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">ID</label>
                    <p className="text-sm">{user.id}</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Estado</label>
                    <p className="text-sm">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                user.active
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
            }`}>
              {user.active ? 'Activo' : 'Inactivo'}
            </span>
                    </p>
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Nombre</label>
                <p className="text-sm">{user.name}</p>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="text-sm">{user.email}</p>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Número de Empleado</label>
                <p className="text-sm">{user.employeeNumber}</p>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Rol</label>
                <p className="text-sm">{user.roleName || 'Sin rol asignado'}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">Creado por</label>
                    <p className="text-sm">{user.createdBy}</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Fecha de creación</label>
                    <p className="text-sm">{user.createdLocalString}</p>
                </div>
            </div>

            {user.updatedBy && (
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Actualizado por</label>
                        <p className="text-sm">{user.updatedBy}</p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Fecha de actualización</label>
                        <p className="text-sm">{user.updatedLocalString}</p>
                    </div>
                </div>
            )}

            {user.isDeleted && (
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Eliminado por</label>
                        <p className="text-sm">{user.deletedBy}</p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Fecha de eliminación</label>
                        <p className="text-sm">{user.deletedLocalString}</p>
                    </div>
                </div>
            )}
        </div>
    );

    return (
        <div className="p-6">
            <BaseEntityTable
                data={usersData?.data || []}
                isLoading={isLoading}
                totalRecords={usersData?.totalRecords || 0}
                pageNumber={pageNumber}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                onSearch={handleSearch}
                onCreate={handleCreate}
                onUpdate={handleUpdate}
                onDelete={handleDelete}
                onActivate={handleActivate}
                onDeactivate={handleDeactivate}
                entityName="Usuario"
                entityNamePlural="Usuarios"
                searchPlaceholder="Buscar usuarios..."
                showActiveToggle={true}
                customColumns={customColumns}
                renderFormFields={renderFormFields}
                renderFields={renderDetails}
                additionalActions={renderAdditionalActions}
            />

            {/* Modal de cambio de contraseña */}
            {selectedUserForPassword && (
                <ChangePasswordModal
                    isOpen={isChangePasswordModalOpen}
                    onClose={handleCloseChangePasswordModal}
                    onSubmit={handleChangePassword}
                    userId={selectedUserForPassword.id}
                    userName={selectedUserForPassword.name}
                    isLoading={changePasswordMutation.isPending}
                />
            )}
        </div>
    );
};

export default Users;
