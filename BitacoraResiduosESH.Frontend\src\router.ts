import {createHashHistory, createRouter} from "@tanstack/react-router";
import {routeTree} from "./routes";

// Crear el router con hash routing
export const router = createRouter({
    routeTree,
    defaultPreload: 'intent',
    history: createHashHistory(),
});

// Declarar tipos para TypeScript
declare module "@tanstack/react-router" {
    interface Register {
        router: typeof router;
    }
} 