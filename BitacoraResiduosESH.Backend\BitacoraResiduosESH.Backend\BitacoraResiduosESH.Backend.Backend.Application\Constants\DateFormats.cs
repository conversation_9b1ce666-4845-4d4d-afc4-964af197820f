﻿namespace BitacoraResiduosESH.Backend.Backend.Application.Constants;

public static class DateFormats
{
    public const string INPUT_DATE = "yyyy-MM-dd";
    public const string INPUT_DAT_TIME = "yyyy-MM-dd-HH-mm-ss";

    // ISO 8601 formats
    public const string ISO8601 = "yyyy-MM-ddTHH:mm:ss.fffZ";
    public const string ISO8601_WITHOUT_MILLISECONDS = "yyyy-MM-ddTHH:mm:ssZ";
    public const string ISO_DATE = "yyyy-MM-dd";

    // Common date formats
    public const string SHORT_DATE = "MM/dd/yyyy";
    public const string LONG_DATE = "MMMM dd, yyyy";
    public const string SHORT_DATE_TIME = "MM/dd/yyyy HH:mm";
    public const string LONG_DATE_TIME = "MMMM dd, yyyy HH:mm:ss";

    // Time formats
    public const string TIME_24_HOUR = "HH:mm:ss";
    public const string TIME_12_HOUR = "hh:mm:ss tt";

    // Custom formats
    public const string SORTABLE_DATE = "yyyy_MM_dd";
    public const string FILE_NAME_SAFE_TIMESTAMP = "yyyyMMdd_HHmmss";
}