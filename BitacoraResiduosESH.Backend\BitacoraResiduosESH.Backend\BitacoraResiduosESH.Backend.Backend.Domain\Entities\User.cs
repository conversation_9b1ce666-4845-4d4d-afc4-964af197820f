﻿using BitacoraResiduosESH.Backend.Backend.Domain.Entities.Shared;

namespace BitacoraResiduosESH.Backend.Backend.Domain.Entities;

public class User : BaseEntity
{
    public string Name { get; set; } = string.Empty;
    public string EmployeeNumber { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string Password { get; set; } = string.Empty;

    // Relación obligatoria con Role
    public int RoleId { get; set; }
    public Role Role { get; set; } = null!;
}