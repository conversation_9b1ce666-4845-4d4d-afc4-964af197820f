namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.User;

public class AdminChangePasswordDto
{
    [Required(ErrorMessage = "El ID del usuario es requerido")]
    [Range(1, int.MaxValue, ErrorMessage = "El ID del usuario debe ser válido")]
    public int UserId { get; set; }

    [Required(ErrorMessage = "La nueva contraseña es requerida")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "La nueva contraseña debe tener entre 6 y 100 caracteres")]
    public string NewPassword { get; set; } = string.Empty;

    [Required(ErrorMessage = "La confirmación de la nueva contraseña es requerida")]
    [Compare("NewPassword", ErrorMessage = "La confirmación de contraseña no coincide")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}