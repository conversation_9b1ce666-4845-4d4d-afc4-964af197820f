import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import {roleService} from '../services/roleService';
import type {CreateRoleDto, UpdateRoleDto} from '../types/role';

// Query keys para TanStack Query
export const roleQueryKeys = {
    all: ['roles'] as const,
    lists: () => [...roleQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...roleQueryKeys.lists(), filters] as const,
    details: () => [...roleQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...roleQueryKeys.details(), id] as const,
};

export const useRoleCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todos los roles
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: roleQueryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => roleService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener roles activos
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: roleQueryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => roleService.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener un rol por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: roleQueryKeys.detail(id),
            queryFn: () => roleService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar roles por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: roleQueryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => roleService.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar roles por descripción
    const useSearchByDescription = (
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: roleQueryKeys.list({search: 'description', description, pageNumber, pageSize, includeDeleted}),
            queryFn: () => roleService.searchByDescription(description, pageNumber, pageSize, includeDeleted),
            enabled: !!description.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear un rol
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateRoleDto) => roleService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: roleQueryKeys.lists()});
                toast.success('Rol creado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el rol: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar un rol
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: UpdateRoleDto }) =>
                roleService.update(id, data),
            onSuccess: (updatedRole) => {
                queryClient.invalidateQueries({queryKey: roleQueryKeys.lists()});
                queryClient.setQueryData(roleQueryKeys.detail(updatedRole.id), updatedRole);
                toast.success('Rol actualizado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el rol: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar un rol
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => roleService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: roleQueryKeys.lists()});
                queryClient.removeQueries({queryKey: roleQueryKeys.detail(deletedId)});
                toast.success('Rol eliminado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el rol: ${error.message}`);
            },
        });
    };

    // Mutation para activar un rol
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => roleService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: roleQueryKeys.lists()});
                toast.success('Rol activado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el rol: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar un rol
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => roleService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: roleQueryKeys.lists()});
                toast.success('Rol desactivado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el rol: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: roleQueryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByDescription,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
}; 