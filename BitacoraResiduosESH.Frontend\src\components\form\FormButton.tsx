import React from 'react';

export interface FormButtonProps {
    type?: 'button' | 'submit' | 'reset';
    variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
    disabled?: boolean;
    loading?: boolean;
    children: React.ReactNode;
    onClick?: () => void;
    className?: string;
    fullWidth?: boolean;
    icon?: React.ReactNode;
    iconPosition?: 'left' | 'right';
}

const FormButton: React.FC<FormButtonProps> = ({
                                                   type = 'button',
                                                   variant = 'primary',
                                                   size = 'md',
                                                   disabled = false,
                                                   loading = false,
                                                   children,
                                                   onClick,
                                                   className = '',
                                                   fullWidth = false,
                                                   icon,
                                                   iconPosition = 'left'
                                               }) => {
    const isDisabled = disabled || loading;

    const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-lg
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${fullWidth ? 'w-full' : ''}
  `;

    const sizeClasses = {
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };

    const variantClasses = {
        primary: `
      bg-blue-600 hover:bg-blue-700 focus:ring-blue-500
      text-white shadow-sm hover:shadow-md
      dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-500
    `,
        secondary: `
      bg-gray-200 hover:bg-gray-300 focus:ring-gray-500
      text-gray-900 shadow-sm hover:shadow-md
      dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100 dark:focus:ring-gray-500
    `,
        danger: `
      bg-red-600 hover:bg-red-700 focus:ring-red-500
      text-white shadow-sm hover:shadow-md
      dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-500
    `,
        ghost: `
      bg-transparent hover:bg-gray-100 focus:ring-gray-500
      text-gray-700 border border-gray-300
      dark:hover:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:focus:ring-gray-500
    `
    };

    const getButtonStyles = () => {
        const baseStyle = {
            backgroundColor: 'var(--card-bg)',
            borderColor: 'var(--border-color)',
            color: 'var(--text-primary)'
        };

        switch (variant) {
            case 'primary':
                return {
                    ...baseStyle,
                    backgroundColor: '#2563eb',
                    color: '#ffffff'
                };
            case 'secondary':
                return {
                    ...baseStyle,
                    backgroundColor: 'var(--bg-secondary)',
                    color: 'var(--text-primary)'
                };
            case 'danger':
                return {
                    ...baseStyle,
                    backgroundColor: '#dc2626',
                    color: '#ffffff'
                };
            case 'ghost':
                return {
                    ...baseStyle,
                    backgroundColor: 'transparent',
                    borderColor: 'var(--border-color)',
                    color: 'var(--text-primary)'
                };
            default:
                return baseStyle;
        }
    };

    return (
        <button
            type={type}
            disabled={isDisabled}
            onClick={onClick}
            className={`
        ${baseClasses}
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
            style={getButtonStyles()}
        >
            {loading && (
                <svg
                    className={`animate-spin ${iconPosition === 'left' ? 'mr-2' : 'ml-2'} w-4 h-4`}
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                    />
                    <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                </svg>
            )}

            {!loading && icon && iconPosition === 'left' && (
                <span className="mr-2">{icon}</span>
            )}

            <span>{children}</span>

            {!loading && icon && iconPosition === 'right' && (
                <span className="ml-2">{icon}</span>
            )}
        </button>
    );
};

export default FormButton; 