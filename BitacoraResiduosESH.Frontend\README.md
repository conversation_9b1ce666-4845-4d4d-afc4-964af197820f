﻿    # toolsms.reactui
    
    Este proyecto fue creado usando el template de Molex Nogales Dashboard.
    
    ## DescripciÃ³n
    
    Dashboard empresarial desarrollado con React, TypeScript y Tailwind CSS.
    
    ## CaracterÃ­sticas
    
    - âœ… Dashboard moderno y responsive
    - âœ… Sidebar colapsible
    - âœ… Header con bÃºsqueda y notificaciones
    - âœ… Componentes reutilizables
    - âœ… TypeScript para mayor seguridad
    - âœ… Tailwind CSS para estilos
    
    ## InstalaciÃ³n
    
    1. Instalar dependencias:
    ```bash
    npm install
    ```
    
    2. Ejecutar en modo desarrollo:
    ```bash
    npm run dev
    ```
    
    3. Construir para producciÃ³n:
    ```bash
    npm run build
    ```
    
    ## Estructura del Proyecto
    
    ```
    src/
    â”œâ”€â”€ components/     # Componentes reutilizables
    â”œâ”€â”€ layouts/        # Layouts de la aplicaciÃ³n
    â”œâ”€â”€ pages/          # PÃ¡ginas de la aplicaciÃ³n
    â”œâ”€â”€ types/          # Definiciones de tipos TypeScript
    â”œâ”€â”€ services/       # Servicios y APIs
    â””â”€â”€ lib/           # Utilidades y helpers
    ```
    
    ## TecnologÃ­as Utilizadas
    
    - React 19
    - TypeScript
    - Tailwind CSS
    - Vite
    
    ## Creado con
    
    Template de Molex Nogales Dashboard Generator v1.0
