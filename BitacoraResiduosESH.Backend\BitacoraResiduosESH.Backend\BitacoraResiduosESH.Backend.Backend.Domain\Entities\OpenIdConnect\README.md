# Entidades de OpenID Connect

Este directorio contiene las entidades relacionadas con la autenticación OpenID Connect que forman parte del dominio de
la aplicación.

## Entidades

### TokenResponse

Representa la respuesta de token de OpenID Connect que incluye:

- `AccessToken`: Token de acceso para autenticar requests
- `TokenType`: Tipo de token (generalmente "Bearer")
- `ExpiresIn`: Tiempo de expiración en segundos
- `RefreshToken`: Token para refrescar el access token
- `Scope`: Alcance de permisos otorgados
- `IdToken`: Token de identidad (JWT)

### UserInfo

Representa la información del usuario obtenida del proveedor de identidad:

- `Sub`: Identificador único del usuario
- `Name`: Nombre completo del usuario
- `GivenName`: Nombre(s) del usuario
- `FamilyName`: Apellido(s) del usuario
- `PreferedUserName`: Nombre de usuario preferido (usado como email)
- `NetworkId`: ID de red del usuario
- `Region`: Región del usuario
- `UpdatedAt`: Timestamp de última actualización (formato epoch)
- `UpdatedAtDateTime`: Propiedad calculada que convierte el timestamp a DateTime

## Características

- **Separación de responsabilidades**: Las entidades están en el proyecto de Dominio siguiendo la arquitectura limpia
- **Serialización JSON**: Usan `JsonPropertyName` para mapear correctamente con las respuestas de la API
- **Manejo de timestamps**: Incluyen conversión automática de timestamps epoch a DateTime
- **Documentación**: Cada entidad y propiedad está documentada con comentarios XML

## Uso

Estas entidades son utilizadas por:

- `IOpenIdConnectService` en el proyecto de Dominio (interfaz)
- `OpenIdConnectService` en el proyecto de Infraestructura (implementación)
- Controladores en el proyecto WebAPI
- Middleware de autenticación 