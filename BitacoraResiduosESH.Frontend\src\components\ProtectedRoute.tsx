import React from 'react';
import {useAuthStore} from '../stores/authStore';
import {Link} from '@tanstack/react-router';
import {AlertTriangle, Home, Lock, LogIn, Shield} from 'lucide-react';

interface ProtectedRouteProps {
    children: React.ReactNode;
    requiredRoles?: string | string[];
    fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
                                                           children,
                                                           requiredRoles,
                                                           fallback
                                                       }) => {
    const {isAuthenticated, user, hasRole, isLoading} = useAuthStore();

    // Mostrar loading mientras se verifica la autenticación
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
                <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
                    <div className="flex items-center justify-center mb-6">
                        <img
                            src="/molex-logo-1.png"
                            alt="Molex Logo"
                            className="h-12 w-auto"
                        />
                    </div>
                    <div className="flex items-center justify-center space-x-3">
                        <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg"
                             fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span className="text-gray-700 font-medium">Verificando autenticación...</span>
                    </div>
                </div>
            </div>
        );
    }

    // Si no está autenticado, mostrar fallback o redirigir
    if (!isAuthenticated) {
        if (fallback) {
            return <>{fallback}</>;
        }

        return (
            <div
                className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
                <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 border border-red-100">
                    {/* Logo */}
                    <div className="flex items-center justify-center mb-6">
                        <img
                            src="/molex-logo-1.png"
                            alt="Molex Logo"
                            className="h-16 w-auto"
                        />
                    </div>

                    {/* Icono de acceso denegado */}
                    <div className="flex justify-center mb-6">
                        <div className="bg-red-100 p-4 rounded-full">
                            <Lock className="h-12 w-12 text-red-600"/>
                        </div>
                    </div>

                    {/* Contenido */}
                    <div className="text-center">
                        <h2 className="text-3xl font-bold text-gray-900 mb-3">
                            Acceso Denegado
                        </h2>
                        <p className="text-gray-600 mb-8 text-lg">
                            Debe iniciar sesión para acceder a esta página
                        </p>

                        {/* Botón de acción */}
                        <Link
                            to="/login"
                            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg hover:shadow-xl"
                        >
                            <LogIn className="h-5 w-5 mr-2"/>
                            Ir al Login
                        </Link>
                    </div>

                    {/* Información adicional */}
                    <div className="mt-8 pt-6 border-t border-gray-100">
                        <p className="text-sm text-gray-500 text-center">
                            Sistema de Gestión de Aplicadores - Molex
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    // Si se requieren roles específicos, verificar permisos
    if (requiredRoles && !hasRole(requiredRoles)) {
        if (fallback) {
            return <>{fallback}</>;
        }

        return (
            <div
                className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 via-yellow-50 to-red-50">
                <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-lg w-full mx-4 border border-orange-100">
                    {/* Logo */}
                    <div className="flex items-center justify-center mb-6">
                        <img
                            src="/molex-logo-1.png"
                            alt="Molex Logo"
                            className="h-16 w-auto"
                        />
                    </div>

                    {/* Icono de permisos insuficientes */}
                    <div className="flex justify-center mb-6">
                        <div className="bg-orange-100 p-4 rounded-full">
                            <Shield className="h-12 w-12 text-orange-600"/>
                        </div>
                    </div>

                    {/* Contenido */}
                    <div className="text-center">
                        <h2 className="text-3xl font-bold text-gray-900 mb-3">
                            Permisos Insuficientes
                        </h2>
                        <div className="bg-orange-50 rounded-lg p-4 mb-6 border border-orange-200">
                            <div className="flex items-center justify-center mb-2">
                                <AlertTriangle className="h-5 w-5 text-orange-600 mr-2"/>
                                <span className="text-orange-800 font-medium">Rol Actual: {user?.role}</span>
                            </div>
                            <p className="text-orange-700 text-sm">
                                Su rol actual no tiene permisos para acceder a esta página
                            </p>
                        </div>

                        <div className="bg-gray-50 rounded-lg p-4 mb-6">
                            <p className="text-gray-600 text-sm mb-2">Roles requeridos:</p>
                            <p className="text-gray-800 font-medium">
                                {Array.isArray(requiredRoles) ? requiredRoles.join(', ') : requiredRoles}
                            </p>
                        </div>

                        {/* Botones de acción */}
                        <div className="flex flex-col sm:flex-row gap-3 justify-center">
                            <a
                                href="/"
                                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                            >
                                <Home className="h-5 w-5 mr-2"/>
                                Volver al Inicio
                            </a>
                            <Link
                                to="/login"
                                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                            >
                                <LogIn className="h-5 w-5 mr-2"/>
                                Cambiar Usuario
                            </Link>
                        </div>
                    </div>

                    {/* Información adicional */}
                    <div className="mt-8 pt-6 border-t border-gray-100">
                        <p className="text-sm text-gray-500 text-center">
                            Sistema de Gestión de Herramientas - Molex
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    // Si todo está bien, mostrar el contenido
    return <>{children}</>;
};

export default ProtectedRoute; 