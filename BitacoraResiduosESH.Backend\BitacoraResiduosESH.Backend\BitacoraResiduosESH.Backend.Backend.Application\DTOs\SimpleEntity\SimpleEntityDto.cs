namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.SimpleEntity;

public class SimpleEntityDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime Created { get; set; }
    public string CreatedString { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime? Updated { get; set; }
    public string? UpdatedString { get; set; }
    public string? UpdatedBy { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? Deleted { get; set; }
    public string? DeletedString { get; set; }
    public string? DeletedBy { get; set; }
    public bool Active { get; set; }
}