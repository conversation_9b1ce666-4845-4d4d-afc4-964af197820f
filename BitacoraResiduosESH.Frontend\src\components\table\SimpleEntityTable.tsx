import React, {useMemo, useState} from 'react';
import {
    type ColumnDef,
    type ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    type PaginationState,
    type SortingState,
    useReactTable,
} from '@tanstack/react-table';
import {ArrowDown, ArrowUp, ArrowUpDown, CheckCircle, Edit, Eye, Plus, Trash2, XCircle} from 'lucide-react';
import FuzzySearch from './FuzzySearch';
import TablePagination from './TablePagination';
import ConfirmModal from './ConfirmModal';
import DetailsModal from './DetailsModal';
import FormModal from '../form/FormModal';
import type {CreateSimpleEntityDto, SimpleEntity, UpdateSimpleEntityDto} from '../../types/simpleEntity';

// Props para el componente de tabla genérico
interface SimpleEntityTableProps<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto
> {
    // Datos y estado
    data: T[];
    isLoading: boolean;
    totalRecords: number;
    pageNumber: number;
    pageSize: number;

    // Callbacks
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
    onSearch: (searchTerm: string) => void;
    onCreate: (data: TCreate) => Promise<void>;
    onUpdate: (id: number, data: TUpdate) => Promise<void>;
    onDelete: (id: number) => Promise<void>;
    onActivate?: (id: number) => Promise<void>;
    onDeactivate?: (id: number) => Promise<void>;

    // Configuración
    entityName: string;
    entityNamePlural: string;
    searchPlaceholder?: string;
    showActiveToggle?: boolean;

    // Componentes personalizados
    renderFormFields?: (data?: T) => React.ReactNode;
    renderDetails?: (data: T) => React.ReactNode;
    renderActions?: (data: T) => React.ReactNode;
}

export function SimpleEntityTable<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto
>({
      data,
      isLoading,
      totalRecords,
      pageNumber,
      pageSize,
      onPageChange,
      onPageSizeChange,
      onSearch,
      onCreate,
      onUpdate,
      onDelete,
      onActivate,
      onDeactivate,
      entityName,
      entityNamePlural,
      searchPlaceholder = `Buscar ${entityNamePlural.toLowerCase()}...`,
      showActiveToggle = true,
      renderFormFields,
      renderDetails,
      renderActions,
  }: SimpleEntityTableProps<T, TCreate, TUpdate>) {
    // Estados locales
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [globalFilter, setGlobalFilter] = useState('');
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: pageNumber - 1,
        pageSize,
    });

    // Estados de modales
    const [isFormModalOpen, setIsFormModalOpen] = useState(false);
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<T | null>(null);
    const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
    const [loading, setLoading] = useState(false);

    // Función de búsqueda fuzzy
    const fuzzyFilter = (row: any, columnId: string, value: string) => {
        const itemValue = row.getValue(columnId);
        if (!value) return true;

        const searchValue = value.toLowerCase();
        const itemString = String(itemValue).toLowerCase();

        return itemString.includes(searchValue);
    };

    // Definición de columnas
    const columns = useMemo<ColumnDef<T>[]>(
        () => [
            {
                accessorKey: 'id',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>ID</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => (
                    <span style={{color: 'var(--text-primary)'}}>
            {row.getValue('id')}
          </span>
                ),
                size: 80,
            },
            {
                accessorKey: 'name',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>Nombre</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => (
                    <span style={{color: 'var(--text-primary)'}}>
            {row.getValue('name')}
          </span>
                ),
            },
            {
                accessorKey: 'description',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>Descripción</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => {
                    const description = row.getValue('description') as string;
                    return (
                        <span
                            style={{color: 'var(--text-primary)'}}
                            className="max-w-xs truncate block"
                            title={description}
                        >
              {description || '-'}
            </span>
                    );
                },
            },
            {
                accessorKey: 'active',
                header: 'Estado',
                cell: ({row}) => {
                    const isActive = row.getValue('active') as boolean;
                    return (
                        <div className="flex items-center space-x-2">
                            {isActive ? (
                                <CheckCircle className="h-4 w-4 text-green-600"/>
                            ) : (
                                <XCircle className="h-4 w-4 text-red-600"/>
                            )}
                            <span
                                style={{color: 'var(--text-primary)'}}
                                className={isActive ? 'text-green-600' : 'text-red-600'}
                            >
                {isActive ? 'Activo' : 'Inactivo'}
              </span>
                        </div>
                    );
                },
                size: 100,
            },
            {
                accessorKey: 'updatedLocalString',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>Última Actualización</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => {
                    const updatedString = row.getValue('updatedLocalString') as string;
                    return (
                        <span style={{color: 'var(--text-primary)'}}>
              {updatedString || '-'}
            </span>
                    );
                },
            },
            {
                id: 'actions',
                header: 'Acciones',
                cell: ({row}) => {
                    const item = row.original;

                    if (renderActions) {
                        return renderActions(item);
                    }

                    return (
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => handleViewDetails(item)}
                                className="p-1 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/20 transition-colors duration-200"
                                style={{color: '#3b82f6'}}
                                title="Ver detalles"
                            >
                                <Eye className="h-4 w-4"/>
                            </button>
                            <button
                                onClick={() => handleEdit(item)}
                                className="p-1 rounded-md hover:bg-yellow-100 dark:hover:bg-yellow-900/20 transition-colors duration-200"
                                style={{color: '#d97706'}}
                                title="Editar"
                            >
                                <Edit className="h-4 w-4"/>
                            </button>
                            {showActiveToggle && onActivate && onDeactivate && (
                                <>
                                    {item.active ? (
                                        <button
                                            onClick={() => handleDeactivate(item)}
                                            className="p-1 rounded-md hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors duration-200"
                                            style={{color: '#dc2626'}}
                                            title="Desactivar"
                                        >
                                            <XCircle className="h-4 w-4"/>
                                        </button>
                                    ) : (
                                        <button
                                            onClick={() => handleActivate(item)}
                                            className="p-1 rounded-md hover:bg-green-100 dark:hover:bg-green-900/20 transition-colors duration-200"
                                            style={{color: '#16a34a'}}
                                            title="Activar"
                                        >
                                            <CheckCircle className="h-4 w-4"/>
                                        </button>
                                    )}
                                </>
                            )}
                            <button
                                onClick={() => handleDelete(item)}
                                className="p-1 rounded-md hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors duration-200"
                                style={{color: '#dc2626'}}
                                title="Eliminar"
                            >
                                <Trash2 className="h-4 w-4"/>
                            </button>
                        </div>
                    );
                },
                size: 150,
            },
        ],
        [showActiveToggle, onActivate, onDeactivate, renderActions]
    );

    // Configuración de la tabla
    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnFilters,
            globalFilter,
            pagination,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        globalFilterFn: fuzzyFilter,
        enableSorting: true,
        enableColumnFilters: true,
        enableGlobalFilter: true,
    });

    // Handlers
    const handleCreate = () => {
        setFormMode('create');
        setSelectedItem(null);
        setIsFormModalOpen(true);
    };

    const handleEdit = (item: T) => {
        setFormMode('edit');
        setSelectedItem(item);
        setIsFormModalOpen(true);
    };

    const handleViewDetails = (item: T) => {
        setSelectedItem(item);
        setIsDetailsModalOpen(true);
    };

    const handleDelete = (item: T) => {
        setSelectedItem(item);
        setIsDeleteModalOpen(true);
    };

    const handleActivate = async (item: T) => {
        if (!onActivate) return;
        setLoading(true);
        try {
            await onActivate(item.id);
        } finally {
            setLoading(false);
        }
    };

    const handleDeactivate = async (item: T) => {
        if (!onDeactivate) return;
        setLoading(true);
        try {
            await onDeactivate(item.id);
        } finally {
            setLoading(false);
        }
    };

    const handleFormSubmit = async (formData: any) => {
        setLoading(true);
        try {
            if (formMode === 'create') {
                await onCreate(formData as TCreate);
            } else if (selectedItem) {
                await onUpdate(selectedItem.id, {...formData, id: selectedItem.id} as TUpdate);
            }
            setIsFormModalOpen(false);
        } finally {
            setLoading(false);
        }
    };

    const handleConfirmDelete = async () => {
        if (!selectedItem) return;

        setLoading(true);
        try {
            await onDelete(selectedItem.id);
            setIsDeleteModalOpen(false);
            setSelectedItem(null);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (value: string) => {
        setGlobalFilter(value);
        onSearch(value);
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1
                        className="text-2xl font-bold"
                        style={{color: 'var(--text-primary)'}}
                    >
                        Gestión de {entityNamePlural}
                    </h1>
                    <p
                        className="text-sm mt-1"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Administra los {entityNamePlural.toLowerCase()} del sistema
                    </p>
                </div>
                <button
                    onClick={handleCreate}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                    <Plus className="h-4 w-4"/>
                    <span>Crear Nuevo</span>
                </button>
            </div>

            {/* Filtros y búsqueda */}
            <div className="flex items-center justify-between">
                <FuzzySearch
                    value={globalFilter}
                    onChange={handleSearch}
                    placeholder={searchPlaceholder}
                    className="w-80"
                />
            </div>

            {/* Tabla */}
            <div
                className="border rounded-lg overflow-hidden"
                style={{borderColor: 'var(--border-color)'}}
            >
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead
                            className="border-b"
                            style={{borderColor: 'var(--border-color)', backgroundColor: 'var(--bg-secondary)'}}
                        >
                        {table.getHeaderGroups().map(headerGroup => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <th
                                        key={header.id}
                                        className="px-4 py-3 text-left text-sm font-medium"
                                        style={{color: 'var(--text-primary)'}}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </th>
                                ))}
                            </tr>
                        ))}
                        </thead>
                        <tbody>
                        {isLoading ? (
                            <tr>
                                <td
                                    colSpan={columns.length}
                                    className="px-4 py-8 text-center"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    Cargando...
                                </td>
                            </tr>
                        ) : table.getRowModel().rows.length === 0 ? (
                            <tr>
                                <td
                                    colSpan={columns.length}
                                    className="px-4 py-8 text-center"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    No se encontraron {entityNamePlural.toLowerCase()}
                                </td>
                            </tr>
                        ) : (
                            table.getRowModel().rows.map(row => (
                                <tr
                                    key={row.id}
                                    className="border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                                    style={{borderColor: 'var(--border-color)'}}
                                >
                                    {row.getVisibleCells().map(cell => (
                                        <td
                                            key={cell.id}
                                            className="px-4 py-3 text-sm"
                                        >
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </td>
                                    ))}
                                </tr>
                            ))
                        )}
                        </tbody>
                    </table>
                </div>

                {/* Paginación */}
                <TablePagination
                    currentPage={pageNumber}
                    totalPages={Math.ceil(totalRecords / pageSize)}
                    totalItems={totalRecords}
                    pageSize={pageSize}
                    onPageChange={onPageChange}
                    onPageSizeChange={onPageSizeChange}
                />
            </div>

            {/* Modales */}
            <FormModal
                isOpen={isFormModalOpen}
                onClose={() => setIsFormModalOpen(false)}
                onSubmit={handleFormSubmit}
                data={selectedItem || undefined}
                mode={formMode}
                loading={loading}
                title={formMode === 'create' ? `Crear ${entityName}` : `Editar ${entityName}`}
                renderFields={renderFormFields}
            />

            <DetailsModal
                isOpen={isDetailsModalOpen && !!selectedItem}
                onClose={() => setIsDetailsModalOpen(false)}
                data={selectedItem || undefined}
                title={`Detalles de ${entityName}`}
                renderContent={renderDetails}
            />

            <ConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                title="Confirmar Eliminación"
                message={`¿Estás seguro de que deseas eliminar "${selectedItem?.name}"? Esta acción no se puede deshacer.`}
                confirmText="Eliminar"
                variant="danger"
                loading={loading}
            />
        </div>
    );
} 