﻿namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry;

public class CreateBitacoraEntryDto : CreateBaseEntityDto
{

    [StringLength(1000, ErrorMessage = "El comentario no puede exceder 1000 caracteres")]
    public string? Comments { get; set; }
    // Waste Type relationship
    [Required(ErrorMessage = "El tipo de residuo es requerido")]
    public int WasteTypeId { get; set; }

    // Weight properties
    [Required(ErrorMessage = "El peso bruto es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El peso bruto debe ser mayor o igual a 0")]
    public decimal GrossWeight { get; set; }

    [Required(ErrorMessage = "La tara es requerida")]
    [Range(0, double.MaxValue, ErrorMessage = "La tara debe ser mayor o igual a 0")]
    public decimal Tare { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "El peso neto en libras debe ser mayor o igual a 0")]
    public decimal NetWeightLB { get; set; }

    [Range(0, double.MaxValue, ErrorMessage = "El peso neto en kilogramos debe ser mayor o igual a 0")]
    public decimal NetWeightKG { get; set; }

    // Price
    [Required(ErrorMessage = "El precio unitario es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El precio unitario debe ser mayor o igual a 0")]
    public decimal UnitPrice { get; set; }

    // Container Type relationship
    [Required(ErrorMessage = "El tipo de contenedor es requerido")]
    public int ContainerTypeId { get; set; }

    // Area relationship
    [Required(ErrorMessage = "El área es requerida")]
    public int AreaId { get; set; }
}
