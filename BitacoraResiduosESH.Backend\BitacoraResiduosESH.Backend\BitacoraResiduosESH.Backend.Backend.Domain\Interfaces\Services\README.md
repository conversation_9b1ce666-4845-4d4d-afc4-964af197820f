# Interfaces de Servicios

Este directorio contiene las interfaces de servicios que definen los contratos para la lógica de negocio y servicios
externos.

## Interfaces

### IOpenIdConnectService

Define el contrato para el servicio de autenticación OpenID Connect que incluye:

#### Métodos:

- `GetAuthorizationUrl(string state, string scope)`: Genera la URL de autorización para iniciar el flujo OAuth2
- `ExchangeCodeForTokenAsync(string authorizationCode)`: Intercambia el código de autorización por un token de acceso
- `GetUserInfoAsync(string accessToken)`: Obtiene información del usuario usando el token de acceso
- `RefreshTokenAsync(string refreshToken)`: Refresca el token de acceso usando el refresh token
- `RevokeTokenAsync(string token, string tokenTypeHint)`: Revoca un token de acceso

#### Características:

- **Cumple con RFC6749**: La implementación sigue la especificación OAuth 2.0
- **Autenticación Basic**: Usa autenticación Basic para cliente confidencial
- **Manejo de errores**: Incluye logging detallado y manejo de excepciones
- **Limpieza de headers**: Limpia headers de autorización después de cada request

## Arquitectura

Siguiendo los principios de la arquitectura limpia:

- **Dominio**: Contiene las interfaces y entidades
- **Infraestructura**: Contiene las implementaciones concretas
- **WebAPI**: Usa las interfaces a través de inyección de dependencias

## Implementación

La implementación concreta `OpenIdConnectService` se encuentra en:
`ToolsMS.Backend.Infrastructure/Services/OpenIdConnectService.cs`

## Registro de Dependencias

El servicio se registra en el contenedor de dependencias en:
`ToolsMS.Backend.Infrastructure/Extensions/ServiceCollectionExtensions.cs`

```csharp
services.AddScoped<IOpenIdConnectService, OpenIdConnectService>();
``` 