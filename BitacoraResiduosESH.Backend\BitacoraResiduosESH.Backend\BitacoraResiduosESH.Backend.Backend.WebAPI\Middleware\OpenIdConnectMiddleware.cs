namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Middleware;

public class OpenIdConnectMiddleware
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    private readonly RequestDelegate _next;

    public OpenIdConnectMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context, OpenIdConnectService openIdService)
    {
        // Solo procesar si hay un token de autorización
        var authorizationHeader = context.Request.Headers["Authorization"].FirstOrDefault();

        if (!string.IsNullOrEmpty(authorizationHeader) && authorizationHeader.StartsWith("Bearer "))
            try
            {
                var token = authorizationHeader.Substring("Bearer ".Length);

                // Validar el token obteniendo información del usuario
                var userInfo = await openIdService.GetUserInfoAsync(token);

                if (userInfo != null)
                {
                    // Agregar información del usuario al contexto
                    context.Items["UserInfo"] = userInfo;
                    context.Items["AccessToken"] = token;

                    Logger.Debug("Token válido para usuario: {Email}", userInfo.PreferedUserName);
                }
                else
                {
                    Logger.Warn("Token inválido o expirado");
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsJsonAsync(new { error = "Token inválido o expirado" });
                    return;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error al validar token");
                context.Response.StatusCode = 401;
                await context.Response.WriteAsJsonAsync(new { error = "Error al validar token" });
                return;
            }

        await _next(context);
    }
}

// Extensión para facilitar el uso del middleware
public static class OpenIdConnectMiddlewareExtensions
{
    public static IApplicationBuilder UseOpenIdConnectValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<OpenIdConnectMiddleware>();
    }
}