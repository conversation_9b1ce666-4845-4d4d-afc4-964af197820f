import React, {useEffect, useRef, useState} from 'react';

interface TooltipProps {
    children: React.ReactNode;
    content: React.ReactNode;
    position?: 'top' | 'bottom' | 'left' | 'right';
    delay?: number;
    className?: string;
    trigger?: 'hover' | 'click';
}

const Tooltip: React.FC<TooltipProps> = ({
                                             children,
                                             content,
                                             position = 'top',
                                             delay = 200,
                                             className = '',
                                             trigger = 'hover'
                                         }) => {
    const [isVisible, setIsVisible] = useState(false);
    const [tooltipPosition, setTooltipPosition] = useState({top: 0, left: 0});
    const triggerRef = useRef<HTMLDivElement>(null);
    const tooltipRef = useRef<HTMLDivElement>(null);
    const timeoutRef = useRef<number | undefined>(undefined);

    const showTooltip = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
            setIsVisible(true);
            calculatePosition();
        }, delay);
    };

    const hideTooltip = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        setIsVisible(false);
    };

    const calculatePosition = () => {
        if (!triggerRef.current || !tooltipRef.current) return;

        const triggerRect = triggerRef.current.getBoundingClientRect();
        const tooltipRect = tooltipRef.current.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        let top = 0;
        let left = 0;

        switch (position) {
            case 'top':
                top = triggerRect.top + scrollTop - tooltipRect.height - 8;
                left = triggerRect.left + scrollLeft + (triggerRect.width / 2) - (tooltipRect.width / 2);
                break;
            case 'bottom':
                top = triggerRect.bottom + scrollTop + 8;
                left = triggerRect.left + scrollLeft + (triggerRect.width / 2) - (tooltipRect.width / 2);
                break;
            case 'left':
                top = triggerRect.top + scrollTop + (triggerRect.height / 2) - (tooltipRect.height / 2);
                left = triggerRect.left + scrollLeft - tooltipRect.width - 8;
                break;
            case 'right':
                top = triggerRect.top + scrollTop + (triggerRect.height / 2) - (tooltipRect.height / 2);
                left = triggerRect.right + scrollLeft + 8;
                break;
        }

        // Asegurar que el tooltip no se salga de la pantalla
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        if (left < 8) left = 8;
        if (left + tooltipRect.width > viewportWidth - 8) {
            left = viewportWidth - tooltipRect.width - 8;
        }
        if (top < 8) top = 8;
        if (top + tooltipRect.height > viewportHeight - 8) {
            top = viewportHeight - tooltipRect.height - 8;
        }

        setTooltipPosition({top, left});
    };

    useEffect(() => {
        if (isVisible) {
            calculatePosition();
            window.addEventListener('scroll', calculatePosition);
            window.addEventListener('resize', calculatePosition);

            return () => {
                window.removeEventListener('scroll', calculatePosition);
                window.removeEventListener('resize', calculatePosition);
            };
        }
    }, [isVisible]);

    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    const handleMouseEvents = trigger === 'hover' ? {
        onMouseEnter: showTooltip,
        onMouseLeave: hideTooltip,
    } : {};

    const handleClickEvents = trigger === 'click' ? {
        onClick: () => {
            if (isVisible) {
                hideTooltip();
            } else {
                showTooltip();
            }
        },
    } : {};

    return (
        <div className="relative inline-block">
            <div
                ref={triggerRef}
                {...handleMouseEvents}
                {...handleClickEvents}
                className={className}
            >
                {children}
            </div>

            {isVisible && (
                <div
                    ref={tooltipRef}
                    className="fixed z-50 px-3 py-2 text-sm rounded-lg shadow-lg border transition-all duration-200 animate-fade-in"
                    style={{
                        top: tooltipPosition.top,
                        left: tooltipPosition.left,
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)',
                        color: 'var(--text-primary)',
                        maxWidth: '300px',
                        wordWrap: 'break-word'
                    }}
                >
                    {content}
                    {/* Flecha del tooltip */}
                    <div
                        className="absolute w-2 h-2 transform rotate-45"
                        style={{
                            backgroundColor: 'var(--card-bg)',
                            borderColor: 'var(--border-color)',
                            ...(position === 'top' && {
                                bottom: '-4px',
                                left: '50%',
                                marginLeft: '-4px',
                                borderRight: '1px solid var(--border-color)',
                                borderBottom: '1px solid var(--border-color)',
                                borderTop: 'none',
                                borderLeft: 'none'
                            }),
                            ...(position === 'bottom' && {
                                top: '-4px',
                                left: '50%',
                                marginLeft: '-4px',
                                borderLeft: '1px solid var(--border-color)',
                                borderTop: '1px solid var(--border-color)',
                                borderRight: 'none',
                                borderBottom: 'none'
                            }),
                            ...(position === 'left' && {
                                right: '-4px',
                                top: '50%',
                                marginTop: '-4px',
                                borderTop: '1px solid var(--border-color)',
                                borderRight: '1px solid var(--border-color)',
                                borderBottom: 'none',
                                borderLeft: 'none'
                            }),
                            ...(position === 'right' && {
                                left: '-4px',
                                top: '50%',
                                marginTop: '-4px',
                                borderBottom: '1px solid var(--border-color)',
                                borderLeft: '1px solid var(--border-color)',
                                borderTop: 'none',
                                borderRight: 'none'
                            })
                        }}
                    />
                </div>
            )}
        </div>
    );
};

export default Tooltip; 