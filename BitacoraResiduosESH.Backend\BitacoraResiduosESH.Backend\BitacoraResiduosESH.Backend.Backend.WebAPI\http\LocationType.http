### Variables de entorno
@baseUrl = https://localhost:7001
@token = {{login.response.body.accessToken}}

### Login para obtener token
# @name login
POST {{baseUrl}}/api/Auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "123456"
}

### Obtener todos los tipos de ubicación
GET {{baseUrl}}/api/LocationType
Authorization: Bearer {{token}}

### Obtener tipos de ubicación con filtros
GET {{baseUrl}}/api/LocationType?name=Almacén&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### Obtener un tipo de ubicación por ID
GET {{baseUrl}}/api/LocationType/1
Authorization: Bearer {{token}}

### Crear un nuevo tipo de ubicación
POST {{baseUrl}}/api/LocationType
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Laboratorio de Calidad",
    "description": "Ubicación en el laboratorio para herramientas de control de calidad"
}

### Actualizar un tipo de ubicación
PUT {{baseUrl}}/api/LocationType/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
    "name": "Almacén Principal Actualizado",
    "description": "Ubicación principal de almacenamiento de herramientas (actualizado)"
}

### Eliminar un tipo de ubicación (soft delete)
DELETE {{baseUrl}}/api/LocationType/1
Authorization: Bearer {{token}}

### Obtener tipos de ubicación incluyendo eliminados
GET {{baseUrl}}/api/LocationType?includeDeleted=true
Authorization: Bearer {{token}}

### Restaurar un tipo de ubicación eliminado
PUT {{baseUrl}}/api/LocationType/restore/1
Authorization: Bearer {{token}}

### Obtener tipos de ubicación inactivos
GET {{baseUrl}}/api/LocationType?active=false
Authorization: Bearer {{token}} 