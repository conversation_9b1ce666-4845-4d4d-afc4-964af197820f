// Configuración centralizada de la API
export const API_CONFIG = {
    BASE_URL: 'http://localhost:5001/api',
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3,
} as const;

// Tipos comunes para respuestas de la API
export interface ApiResponse<T> {
    data: T;
    message?: string;
    success: boolean;
}

export interface PagedResponse<T> {
    data: T[];
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
    totalPages: number;
}

// Headers comunes para las peticiones
export const getDefaultHeaders = (token?: string): HeadersInit => {
    const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    };

    // Agregar token JWT si está disponible
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
};

// Función helper para obtener headers con token automáticamente
export const getAuthHeaders = (token?: string): HeadersInit => {
    const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    };

    // Agregar token JWT si está disponible
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
};

// Función helper para obtener el token del store de autenticación
export const getAuthToken = (): string | undefined => {
    try {
        // Usar localStorage con la clave correcta del store de autenticación
        const token = localStorage.getItem('auth-token');
        return token || undefined;
    } catch (error) {
        console.warn('No se pudo obtener el token JWT:', error);
        return undefined;
    }
};

// Función helper para obtener headers con token automáticamente del store
export const getAuthHeadersFromStore = (): HeadersInit => {
    const token = getAuthToken();
    return getAuthHeaders(token);
};

// Función helper para construir URLs de la API
export const buildApiUrl = (endpoint: string): string => {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${API_CONFIG.BASE_URL}/${cleanEndpoint}`;
};

// Función helper para manejar errores de la API
export const handleApiError = (error: any): string => {
    if (error.response?.data?.message) {
        return error.response.data.message;
    }
    if (error.message) {
        return error.message;
    }
    return 'Error desconocido';
}; 