# Script PowerShell para generar una nueva entidad tipo SimpleEntity
# Uso: Ejecuta este script y sigue las instrucciones

param(
    [Parameter(Mandatory = $true)]
    [string]$EntityName
)

$root = "../BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend/BitacoraResiduosESH.Backend.Backend."
$domainPath = $root + "Domain/Entities/"
$dtoPath = $root + "Application/DTOs/$EntityName/"
$repoInterfacePath = $root + "Application/Interfaces/Repositories/"
$serviceInterfacePath = $root + "Application/Interfaces/Services/"
$serviceImplPath = $root + "Application/Services/"
$repoImplPath = $root + "Infrastructure/Repositories/"
$controllerPath = $root + "WebAPI/Controllers/"
$configurationPath = $root + "Infrastructure/Data/ModelConfigurations/"

# Crear carpetas necesarias
New-Item -ItemType Directory -Force -Path $domainPath | Out-Null
New-Item -ItemType Directory -Force -Path $dtoPath | Out-Null
New-Item -ItemType Directory -Force -Path $repoInterfacePath | Out-Null
New-Item -ItemType Directory -Force -Path $serviceInterfacePath | Out-Null
New-Item -ItemType Directory -Force -Path $serviceImplPath | Out-Null
New-Item -ItemType Directory -Force -Path $repoImplPath | Out-Null
New-Item -ItemType Directory -Force -Path $controllerPath | Out-Null
New-Item -ItemType Directory -Force -Path $configurationPath | Out-Null

# 1. Crear clase de entidad
$entityFile = $domainPath + "$EntityName.cs"
@"
namespace BitacoraResiduosESH.Backend.Backend.Domain.Entities;

public class $EntityName : SimpleEntity
{
    // Puedes agregar propiedades adicionales si es necesario
}
"@ | Out-File -Encoding utf8 $entityFile

# 2. Crear DTOs
@"
namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.$EntityName;

public class Create${EntityName}Dto : CreateSimpleEntityDto { }
"@ | Out-File -Encoding utf8 ($dtoPath + "Create${EntityName}Dto.cs")

@"
namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.$EntityName;

public class Update${EntityName}Dto : UpdateSimpleEntityDto { }
"@ | Out-File -Encoding utf8 ($dtoPath + "Update${EntityName}Dto.cs")

@"
namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.$EntityName;

public class ${EntityName}Dto : SimpleEntityDto { }
"@ | Out-File -Encoding utf8 ($dtoPath + "${EntityName}Dto.cs")

@"
namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.$EntityName;

public class ${EntityName}FilterDto : SimpleEntityFilterDto { }
"@ | Out-File -Encoding utf8 ($dtoPath + "${EntityName}FilterDto.cs")

# 3. Interfaces
@"
namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;

public interface I${EntityName}Repository : ISimpleEntityRepository<$EntityName> { }
"@ | Out-File -Encoding utf8 ($repoInterfacePath + "I${EntityName}Repository.cs")

@"
namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;

public interface I${EntityName}Service : ISimpleEntityService<$EntityName, ${EntityName}Dto, Create${EntityName}Dto, Update${EntityName}Dto, ${EntityName}FilterDto> { }
"@ | Out-File -Encoding utf8 ($serviceInterfacePath + "I${EntityName}Service.cs")

# 4. Implementaciones
@"
namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class ${EntityName}Service(I${EntityName}Repository repository)
    : SimpleEntityService<$EntityName, ${EntityName}Dto, Create${EntityName}Dto, Update${EntityName}Dto, ${EntityName}FilterDto>(repository), I${EntityName}Service
{
    
}
"@ | Out-File -Encoding utf8 ($serviceImplPath + "${EntityName}Service.cs")

@"
namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories;

public class ${EntityName}Repository(AppDbContext context)
    : SimpleEntityRepository<$EntityName>(context), I${EntityName}Repository
{
    
}
"@ | Out-File -Encoding utf8 ($repoImplPath + "${EntityName}Repository.cs")

# 5. Controlador
@"
namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ${EntityName}Controller(I${EntityName}Service service)
    : SimpleEntityController<$EntityName, ${EntityName}Dto, Create${EntityName}Dto, Update${EntityName}Dto, ${EntityName}FilterDto>(service)
{
    
}
"@ | Out-File -Encoding utf8 ($controllerPath + "${EntityName}Controller.cs")

# 6. Configuración de la entidad (ModelConfiguration)
$configurationFile = $configurationPath + "${EntityName}Configuration.cs"
@"
namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

public class ${EntityName}Configuration: SimpleEntityConfiguration<${EntityName}>
{
    protected override string TableName => "${EntityName}s";
}
"@ | Out-File -Encoding utf8 $configurationFile

Write-Host "\nArchivos base generados para la entidad $EntityName.\n"
Write-Host "Recuerda agregar los usings y registros en ServiceCollectionExtensions y GlobalUsings." 