# ToolsMS.Backend - Template de Arquitectura Limpia con .NET 9

## 📋 Descripción

Este template proporciona una base sólida para desarrollar aplicaciones .NET 9 con arquitectura limpia, incluyendo:

- **Arquitectura Limpia** (Clean Architecture) con separación de capas
- **Entity Framework Core** con SQLite
- **NLog** para logging completo
- **Sistema de Repositorios Genéricos** con CRUD completo
- **Sistema SimpleEntity** para reutilización de código
- **Validaciones** automáticas
- **Auditoría** completa
- **Soft/Hard Delete**
- **Paginación** y filtros
- **Logging** detallado de todas las operaciones

## 🏗️ Arquitectura

```
ToolsMS.Backend/
├── ToolsMS.Backend.Domain/          # Capa de Dominio
│   ├── Entities/                           # Entidades del dominio
│   ├── Enums/                              # Enumeraciones
│   ├── Interfaces/                         # Interfaces del dominio
│   └── ValueObjects/                       # Objetos de valor
├── ToolsMS.Backend.Application/     # Capa de Aplicación
│   ├── Common/                             # Clases comunes (PagedResponse, etc.)
│   ├── DTOs/                               # Objetos de transferencia de datos
│   ├── Features/                           # Casos de uso
│   ├── Interfaces/                         # Interfaces de servicios y repositorios
│   └── Services/                           # Servicios de aplicación
├── ToolsMS.Backend.Infrastructure/  # Capa de Infraestructura
│   ├── Data/                               # Contexto de EF Core y configuraciones
│   ├── Extensions/                         # Extensiones de configuración
│   └── Repositories/                       # Implementaciones de repositorios
└── ToolsMS.Backend.WebAPI/          # Capa de Presentación
    ├── Controllers/                        # Controladores REST
    ├── Constants/                          # Constantes
    └── Properties/                         # Configuraciones de lanzamiento
```

## 🚀 Características Principales

### ✅ Sistema de Repositorios Genéricos
- **IGenericRepository<T>** - Interfaz genérica con operaciones CRUD completas
- **GenericRepository<T>** - Implementación base con Entity Framework Core
- **Soft/Hard Delete** - Eliminación lógica y física
- **Activación/Desactivación** - Control de estado de entidades
- **Búsqueda y filtros** - Consultas optimizadas
- **Paginación** - Soporte completo para paginación

### ✅ Sistema SimpleEntity
- **Reutilización de código** - Evita duplicación en entidades similares
- **Funcionalidad automática** - CRUD completo sin escribir código
- **Herencia genérica** - Interfaces y servicios genéricos
- **Configuración flexible** - Tablas personalizables

### ✅ Logging Completo con NLog
- **Logging de consultas SQL** - En modo DEBUG
- **Logging de operaciones** - Todas las operaciones CRUD
- **Logging de errores** - Manejo detallado de excepciones
- **Múltiples targets** - Archivo, consola, JSON
- **Configuración por ambiente** - Desarrollo y producción

### ✅ Auditoría Automática
- **Fechas de creación/actualización** - Automáticas
- **Usuario que realiza operaciones** - Tracking completo
- **Soft delete** - Eliminación lógica con auditoría
- **Estado activo/inactivo** - Control de estado

### ✅ Validaciones
- **Validaciones de modelo** - Data Annotations
- **Validaciones de negocio** - En servicios
- **Validaciones únicas** - Prevención de duplicados
- **Mensajes de error** - Personalizables

## 📦 Entidades Incluidas

### 1. BaseEntity
Entidad base con auditoría completa:
```csharp
public abstract class BaseEntity
{
    public int Id { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? Updated { get; set; }
    public string? UpdatedBy { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? Deleted { get; set; }
    public string? DeletedBy { get; set; }
    public bool Active { get; set; }
}
```

### 2. SimpleEntity
Entidad base para reutilización:
```csharp
public class SimpleEntity : BaseEntity
{
    public string Name { get; set; }
    public string? Description { get; set; }
}
```

### 3. ExampleEntity
Ejemplo de entidad con propiedades específicas:
```csharp
public class ExampleEntity : BaseEntity
{
    public string Name { get; set; }
    public string? Description { get; set; }
    public int CategoryId { get; set; }
    public Category Category { get; set; }
}
```

### 4. Category
Entidad de categorías con relaciones:
```csharp
public class Category : BaseEntity
{
    public string Name { get; set; }
    public string? Description { get; set; }
    public int? ParentId { get; set; }
    public Category? Parent { get; set; }
    public ICollection<Category> Children { get; set; }
    public ICollection<ExampleEntity> Examples { get; set; }
}
```

### 5. Product
Ejemplo de entidad que hereda de SimpleEntity:
```csharp
public class Product : SimpleEntity
{
    public decimal Price { get; set; }
    public string? SKU { get; set; }
    public int Stock { get; set; }
}
```

### 6. Item
Ejemplo de entidad sin propiedades adicionales:
```csharp
public class Item : SimpleEntity
{
    // Solo hereda Name, Description y propiedades de BaseEntity
}
```

## 🔧 Configuración

### 1. Base de Datos
El template usa **SQLite** por defecto:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=app.db"
  }
}
```

### 2. Logging
Configuración de NLog con múltiples targets:
```xml
<!-- nlog.config -->
<targets>
  <target name="file" xsi:type="File" fileName="logs/app-${shortdate}.log" />
  <target name="console" xsi:type="Console" />
  <target name="json" xsi:type="File" fileName="logs/app-${shortdate}.json" />
</targets>
```

### 3. Entity Framework
Configuración automática con auditoría:
```csharp
public async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, string username = "", CancellationToken cancellationToken = default)
{
    // Auditoría automática de todas las entidades
}
```

## 📚 Cómo Usar el Template

### 1. Crear una Nueva Entidad Simple

Si tu entidad solo necesita `Name` y `Description`:

```csharp
// 1. Crear la entidad
public class MiEntidad : SimpleEntity
{
    // No se agregan propiedades adicionales
}

// 2. Crear los DTOs
public class MiEntidadDto : SimpleEntityDto { }
public class CreateMiEntidadDto : CreateSimpleEntityDto { }
public class UpdateMiEntidadDto : UpdateSimpleEntityDto { }
public class MiEntidadFilterDto : SimpleEntityFilterDto { }

// 3. Crear las interfaces
public interface IMiEntidadRepository : ISimpleEntityRepository<MiEntidad> { }
public interface IMiEntidadService : ISimpleEntityService<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto> { }

// 4. Crear las implementaciones
public class MiEntidadRepository : SimpleEntityRepository<MiEntidad>, IMiEntidadRepository { }
public class MiEntidadService : SimpleEntityService<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto>, IMiEntidadService { }

// 5. Crear el controlador
public class MiEntidadController : SimpleEntityController<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto> { }

// 6. Crear la configuración
public class MiEntidadConfiguration : SimpleEntityConfiguration<MiEntidad>
{
    public MiEntidadConfiguration() : base("MiEntidades") { }
}
```

### 2. Crear una Entidad con Propiedades Adicionales

Si tu entidad necesita propiedades específicas:

```csharp
// 1. Crear la entidad
public class MiEntidad : BaseEntity
{
    public string Name { get; set; }
    public string? Description { get; set; }
    public decimal Precio { get; set; }
    public int Stock { get; set; }
}

// 2. Crear los DTOs
public class MiEntidadDto
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string? Description { get; set; }
    public decimal Precio { get; set; }
    public int Stock { get; set; }
    // ... propiedades de auditoría
}

// 3. Crear las interfaces
public interface IMiEntidadRepository : IGenericRepository<MiEntidad>
{
    Task<IEnumerable<MiEntidad>> GetByPrecioRangeAsync(decimal min, decimal max);
}

// 4. Implementar repositorio y servicio
public class MiEntidadRepository : GenericRepository<MiEntidad>, IMiEntidadRepository
{
    public async Task<IEnumerable<MiEntidad>> GetByPrecioRangeAsync(decimal min, decimal max)
    {
        return await DbSet.Where(e => e.Precio >= min && e.Precio <= max).ToListAsync();
    }
}
```

## 🌐 Endpoints Automáticos

Al usar el sistema SimpleEntity, obtienes automáticamente estos endpoints:

| Método | Endpoint | Descripción |
|--------|----------|-------------|
| GET | `/api/{entidad}` | Obtener todos |
| GET | `/api/{entidad}/{id}` | Obtener por ID |
| GET | `/api/{entidad}/paged` | Obtener paginados |
| GET | `/api/{entidad}/active` | Obtener activos |
| GET | `/api/{entidad}/search/name` | Buscar por nombre |
| GET | `/api/{entidad}/search/description` | Buscar por descripción |
| POST | `/api/{entidad}` | Crear |
| PUT | `/api/{entidad}/{id}` | Actualizar |
| DELETE | `/api/{entidad}/{id}` | Eliminar (soft) |
| DELETE | `/api/{entidad}/{id}/permanent` | Eliminar (hard) |
| PATCH | `/api/{entidad}/{id}/activate` | Activar |
| PATCH | `/api/{entidad}/{id}/deactivate` | Desactivar |
| GET | `/api/{entidad}/{id}/exists` | Verificar existencia |

## 🔍 Ejemplos de Uso

### Crear una Entidad
```http
POST /api/product
Content-Type: application/json

{
  "name": "Laptop HP Pavilion",
  "description": "Laptop HP Pavilion 15.6 pulgadas",
  "price": 899.99,
  "sku": "HP-PAV-001",
  "stock": 25
}
```

### Obtener Paginados con Filtros
```http
GET /api/product/paged?pageNumber=1&pageSize=10&name=laptop&active=true&minPrice=500&maxPrice=1000
```

### Buscar por Nombre
```http
GET /api/product/search/name?name=hp
```

### Activar/Desactivar
```http
PATCH /api/product/1/activate
PATCH /api/product/1/deactivate
```

## 🛠️ Configuración del Proyecto

### 1. Instalar Dependencias
```bash
dotnet restore
```

### 2. Configurar Base de Datos
```bash
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### 3. Ejecutar la Aplicación
```bash
dotnet run --project ToolsMS.Backend.WebAPI
```

### 4. Probar Endpoints
Usa los archivos `.http` incluidos para probar los endpoints:
- `ExampleEntity.http`
- `Category.http`
- `Product.http`
- `Item.http`

## 📊 Logging

El template incluye logging completo:

### Logs de Operaciones
```
2024-01-15 10:30:15.1234|DEBUG|ProductService|Creando nueva Product: {"Name":"Laptop","Price":899.99}, CreatedBy: admin
2024-01-15 10:30:15.2345|INFO|ProductService|Product creada exitosamente con ID: 1 por usuario: admin
```

### Logs de Consultas SQL (DEBUG)
```
2024-01-15 10:30:15.1234|DEBUG|EF Query|Executed DbCommand (1ms) [Parameters=[@p0='Laptop' (Size = 6), @p1='Laptop HP Pavilion' (Size = 19)], CommandType='Text', CommandTimeout='30']
INSERT INTO "Products" ("Name", "Description", "Price", "SKU", "Stock", "Created", "CreatedBy", "Active", "IsDeleted") VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
```

### Logs de Errores
```
2024-01-15 10:30:15.1234|ERROR|ProductController|Error al crear Product: Ya existe un elemento con el nombre 'Laptop'
```

## 🔒 Validaciones

### Validaciones de Modelo
```csharp
public class CreateProductDto : CreateSimpleEntityDto
{
    [Required(ErrorMessage = "El precio es requerido")]
    [Range(0, double.MaxValue, ErrorMessage = "El precio debe ser mayor o igual a 0")]
    public decimal Price { get; set; }
    
    [StringLength(50, ErrorMessage = "El SKU no puede exceder 50 caracteres")]
    public string? SKU { get; set; }
}
```

### Validaciones de Negocio
```csharp
// Validación de nombre único
if (await ExistsByNameAsync(dto.Name))
{
    throw new InvalidOperationException($"Ya existe un elemento con el nombre '{dto.Name}'");
}
```

## 📈 Paginación

Soporte completo para paginación:

```csharp
public class PagedResponse<T>
{
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
    public List<T> Data { get; set; }
}
```

Ejemplo de respuesta:
```json
{
  "pageNumber": 1,
  "pageSize": 10,
  "totalRecords": 25,
  "data": [
    {
      "id": 1,
      "name": "Laptop HP Pavilion",
      "price": 899.99,
      "stock": 25
    }
  ]
}
```

## 🎯 Ventajas del Template

### ✅ Productividad
- **Desarrollo rápido** - CRUD automático
- **Código reutilizable** - Sistema SimpleEntity
- **Configuración automática** - EF Core y NLog

### ✅ Mantenibilidad
- **Arquitectura limpia** - Separación de responsabilidades
- **Código consistente** - Patrones establecidos
- **Logging completo** - Trazabilidad total

### ✅ Escalabilidad
- **Genéricos** - Fácil extensión
- **Modular** - Componentes independientes
- **Configurable** - Adaptable a diferentes necesidades

### ✅ Calidad
- **Validaciones** - Automáticas y personalizables
- **Auditoría** - Tracking completo
- **Manejo de errores** - Robusto y detallado

## 🚀 Próximos Pasos

1. **Personalizar** - Adaptar a tus necesidades específicas
2. **Agregar autenticación** - JWT, OAuth, etc.
3. **Implementar caché** - Redis, Memory Cache
4. **Agregar tests** - Unit tests, integration tests
5. **Configurar CI/CD** - GitHub Actions, Azure DevOps
6. **Documentar API** - Swagger/OpenAPI
7. **Monitoreo** - Application Insights, etc.

## 📝 Licencia

Este template está diseñado para uso interno y como base para proyectos empresariales.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor, abre un issue o pull request para sugerencias y mejoras.

---

**¡Disfruta desarrollando con este template!** 🎉
