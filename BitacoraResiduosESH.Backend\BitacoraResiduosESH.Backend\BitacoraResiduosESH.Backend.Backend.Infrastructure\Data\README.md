# Configuración de Base de Datos SQLite

## Descripción

Este proyecto utiliza **SQLite** como base de datos principal. SQLite es una base de datos ligera, sin servidor y
autocontenida que es perfecta para desarrollo, pruebas y aplicaciones pequeñas a medianas.

## Características

- **Base de datos de archivo único**: `app.db`
- **Sin configuración de servidor**: No requiere instalación de SQL Server o PostgreSQL
- **Portable**: El archivo de base de datos se puede mover fácilmente
- **Ideal para desarrollo**: Configuración rápida y simple
- **Soporte completo de Entity Framework**: Todas las características de EF Core

## Configuración

### 1. Cadena de Conexión

La cadena de conexión se configura en `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=app.db"
  }
}
```

### 2. Configuración en Program.cs

```csharp
// Configurar Entity Framework con SQLite
builder.Services.AddDatabase(builder.Configuration);

// Asegurar que la base de datos se cree
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
    context.Database.EnsureCreated();
}
```

### 3. Extensiones de Servicios

La configuración se maneja en `ServiceCollectionExtensions.cs`:

```csharp
public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
{
    var connectionString = configuration.GetConnectionString("DefaultConnection");
    
    services.AddDbContext<AppDbContext>(options =>
    {
        options.UseSqlite(connectionString, sqliteOptions =>
        {
            sqliteOptions.MigrationsAssembly(typeof(AppDbContext).Assembly.GetName().Name);
        });
    });
    
    return services;
}
```

## Estructura de la Base de Datos

### Tablas Principales

1. **Examples** - Entidades de ejemplo
2. **Categories** - Categorías para organizar elementos

### Campos de Auditoría (BaseEntity)

Todas las entidades heredan de `BaseEntity` que incluye:

- `Id` - Clave primaria auto-incremental
- `Created` - Fecha de creación
- `CreatedBy` - Usuario que creó la entidad
- `Updated` - Fecha de última actualización
- `UpdatedBy` - Usuario que actualizó la entidad
- `IsDeleted` - Indica eliminación lógica
- `Deleted` - Fecha de eliminación lógica
- `DeletedBy` - Usuario que eliminó la entidad
- `Active` - Estado activo/inactivo

## Migraciones

### Crear una Migración

```bash
# Desde el directorio del proyecto WebAPI
dotnet ef migrations add InitialCreate --project ../ToolsMS.Backend.Infrastructure

# O desde el directorio Infrastructure
dotnet ef migrations add InitialCreate --startup-project ../ToolsMS.Backend.WebAPI
```

### Aplicar Migraciones

```bash
# Aplicar migraciones a la base de datos
dotnet ef database update --project ../ToolsMS.Backend.Infrastructure --startup-project ../ToolsMS.Backend.WebAPI
```

### Revertir Migración

```bash
# Revertir la última migración
dotnet ef database update PreviousMigrationName --project ../ToolsMS.Backend.Infrastructure --startup-project ../ToolsMS.Backend.WebAPI
```

## Desarrollo

### Configuración de Desarrollo

En `appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=app.db"
  },
  "Logging": {
    "LogLevel": {
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  }
}
```

### Logging de Consultas SQL

En desarrollo, las consultas SQL se muestran en la consola para facilitar el debugging.

### Creación Automática de Base de Datos

La aplicación crea automáticamente la base de datos si no existe:

```csharp
context.Database.EnsureCreated();
```

## Producción

### Configuración de Producción

Para producción, considera:

1. **Ubicación del archivo**: Coloca `app.db` en una ubicación segura
2. **Backup**: Implementa estrategias de backup
3. **Permisos**: Asegura permisos de escritura apropiados
4. **Logging**: Deshabilita logging de consultas SQL

### Cadena de Conexión de Producción

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=/path/to/production/app.db"
  }
}
```

## Herramientas de Gestión

### SQLite Browser

Para inspeccionar y gestionar la base de datos manualmente:

1. **DB Browser for SQLite**: https://sqlitebrowser.org/
2. **DBeaver**: https://dbeaver.io/
3. **Azure Data Studio**: Con extensión SQLite

### Comandos Útiles

```bash
# Verificar el estado de las migraciones
dotnet ef migrations list --project ../ToolsMS.Backend.Infrastructure --startup-project ../ToolsMS.Backend.WebAPI

# Generar script SQL de migración
dotnet ef migrations script --project ../ToolsMS.Backend.Infrastructure --startup-project ../ToolsMS.Backend.WebAPI

# Eliminar base de datos
dotnet ef database drop --project ../ToolsMS.Backend.Infrastructure --startup-project ../ToolsMS.Backend.WebAPI
```

## Ventajas de SQLite

### Para Desarrollo

- ✅ Configuración rápida
- ✅ No requiere servidor
- ✅ Archivo único
- ✅ Fácil de respaldar/restaurar
- ✅ Portable

### Para Producción (Aplicaciones Pequeñas/Medianas)

- ✅ Confiable y estable
- ✅ Bajo mantenimiento
- ✅ Sin dependencias externas
- ✅ Excelente rendimiento para cargas moderadas

## Limitaciones

### Consideraciones

- ⚠️ No es ideal para aplicaciones con alta concurrencia
- ⚠️ Limitaciones en consultas complejas
- ⚠️ No soporta usuarios múltiples simultáneos
- ⚠️ Tamaño de base de datos limitado (aunque puede manejar GB)

## Migración a Otros Proveedores

Si necesitas migrar a SQL Server o PostgreSQL:

1. **Cambiar el proveedor** en `ServiceCollectionExtensions.cs`
2. **Actualizar cadena de conexión** en `appsettings.json`
3. **Regenerar migraciones** si es necesario
4. **Actualizar paquetes NuGet**

### Ejemplo: Migrar a SQL Server

```csharp
// En ServiceCollectionExtensions.cs
services.AddDbContext<AppDbContext>(options =>
{
    options.UseSqlServer(connectionString);
});
```

## Troubleshooting

### Problemas Comunes

1. **Archivo de base de datos bloqueado**
    - Verificar que no hay otras aplicaciones usando el archivo
    - Reiniciar la aplicación

2. **Permisos de escritura**
    - Verificar permisos en el directorio
    - Ejecutar como administrador si es necesario

3. **Migraciones fallan**
    - Verificar que el proyecto Infrastructure está referenciado
    - Limpiar y reconstruir la solución

4. **Consultas lentas**
    - Verificar índices en las configuraciones de entidades
    - Optimizar consultas LINQ 