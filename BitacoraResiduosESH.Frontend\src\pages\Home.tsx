import React from "react";
import ProductionChart from "../components/ProductionChart";

const Home: React.FC = () => {
    return (
        <div className="p-6">
            {/* Header section */}
            <div className="mb-8">
                <h1
                    className="text-3xl font-bold mb-2"
                    style={{color: 'var(--text-primary)'}}
                >
                    Bienvenido al Sistema de Gestión
                </h1>
                <p
                    className="text-lg"
                    style={{color: 'var(--text-secondary)'}}
                >
                    Gestiona tu producción, inventario y recursos de manera eficiente
                </p>
            </div>

            {/* Stats cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div
                    className="p-6 rounded-lg shadow-sm border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <p
                                className="text-sm font-medium"
                                style={{color: 'var(--text-secondary)'}}
                            >
                                Órdenes Activas
                            </p>
                            <p
                                className="text-2xl font-bold"
                                style={{color: 'var(--text-primary)'}}
                            >
                                24
                            </p>
                        </div>
                        <div
                            className="p-3 rounded-full"
                            style={{backgroundColor: '#dbeafe'}}
                        >
                            <svg
                                className="w-6 h-6"
                                style={{color: '#1e40af'}}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                />
                            </svg>
                        </div>
                    </div>
                </div>

                <div
                    className="p-6 rounded-lg shadow-sm border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <p
                                className="text-sm font-medium"
                                style={{color: 'var(--text-secondary)'}}
                            >
                                Producción Diaria
                            </p>
                            <p
                                className="text-2xl font-bold"
                                style={{color: 'var(--text-primary)'}}
                            >
                                1,247
                            </p>
                        </div>
                        <div
                            className="p-3 rounded-full"
                            style={{backgroundColor: '#dcfce7'}}
                        >
                            <svg
                                className="w-6 h-6"
                                style={{color: '#166534'}}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                                />
                            </svg>
                        </div>
                    </div>
                </div>

                <div
                    className="p-6 rounded-lg shadow-sm border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <p
                                className="text-sm font-medium"
                                style={{color: 'var(--text-secondary)'}}
                            >
                                Empleados Activos
                            </p>
                            <p
                                className="text-2xl font-bold"
                                style={{color: 'var(--text-primary)'}}
                            >
                                156
                            </p>
                        </div>
                        <div
                            className="p-3 rounded-full"
                            style={{backgroundColor: '#fef3c7'}}
                        >
                            <svg
                                className="w-6 h-6"
                                style={{color: '#d97706'}}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                                />
                            </svg>
                        </div>
                    </div>
                </div>

                <div
                    className="p-6 rounded-lg shadow-sm border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <p
                                className="text-sm font-medium"
                                style={{color: 'var(--text-secondary)'}}
                            >
                                Eficiencia
                            </p>
                            <p
                                className="text-2xl font-bold"
                                style={{color: 'var(--text-primary)'}}
                            >
                                94.2%
                            </p>
                        </div>
                        <div
                            className="p-3 rounded-full"
                            style={{backgroundColor: '#fce7f3'}}
                        >
                            <svg
                                className="w-6 h-6"
                                style={{color: '#be185d'}}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            {/* Production Chart */}
            <div className="mb-8">
                <ProductionChart/>
            </div>

            {/* Quick actions and Recent activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div
                    className="p-6 rounded-lg shadow-sm border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    <h2
                        className="text-xl font-semibold mb-4"
                        style={{color: 'var(--text-primary)'}}
                    >
                        Acciones Rápidas
                    </h2>
                    <div className="space-y-3">
                        <button
                            className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200"
                            style={{
                                backgroundColor: 'var(--hover-bg)',
                                color: 'var(--text-primary)'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#dbeafe';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                            }}
                        >
                            <span>Nueva Orden de Trabajo</span>
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                        <button
                            className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200"
                            style={{
                                backgroundColor: 'var(--hover-bg)',
                                color: 'var(--text-primary)'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#dbeafe';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                            }}
                        >
                            <span>Registrar Producción</span>
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                        <button
                            className="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200"
                            style={{
                                backgroundColor: 'var(--hover-bg)',
                                color: 'var(--text-primary)'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#dbeafe';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                            }}
                        >
                            <span>Ver Reportes</span>
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div
                    className="p-6 rounded-lg shadow-sm border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    <h2
                        className="text-xl font-semibold mb-4"
                        style={{color: 'var(--text-primary)'}}
                    >
                        Actividad Reciente
                    </h2>
                    <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full" style={{backgroundColor: '#10b981'}}></div>
                            <div className="flex-1">
                                <p
                                    className="text-sm font-medium"
                                    style={{color: 'var(--text-primary)'}}
                                >
                                    Orden #1234 completada
                                </p>
                                <p
                                    className="text-xs"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    Hace 5 minutos
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full" style={{backgroundColor: '#3b82f6'}}></div>
                            <div className="flex-1">
                                <p
                                    className="text-sm font-medium"
                                    style={{color: 'var(--text-primary)'}}
                                >
                                    Nueva orden #1235 creada
                                </p>
                                <p
                                    className="text-xs"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    Hace 15 minutos
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full" style={{backgroundColor: '#f59e0b'}}></div>
                            <div className="flex-1">
                                <p
                                    className="text-sm font-medium"
                                    style={{color: 'var(--text-primary)'}}
                                >
                                    Mantenimiento programado
                                </p>
                                <p
                                    className="text-xs"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    Hace 1 hora
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Home;
