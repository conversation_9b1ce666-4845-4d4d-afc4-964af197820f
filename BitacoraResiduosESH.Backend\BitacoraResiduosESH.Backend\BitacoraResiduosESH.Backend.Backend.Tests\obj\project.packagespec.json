﻿"restore":{"projectUniqueName":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Tests\\BitacoraResiduosESH.Backend.Backend.Tests.csproj","projectName":"BitacoraResiduosESH.Backend.Backend.Tests","projectPath":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Tests\\BitacoraResiduosESH.Backend.Backend.Tests.csproj","outputPath":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Tests\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.12.0, )"},"NUnit":{"target":"Package","version":"[4.2.2, )"},"NUnit.Analyzers":{"target":"Package","version":"[4.4.0, )"},"NUnit3TestAdapter":{"target":"Package","version":"[4.6.0, )"},"coverlet.collector":{"target":"Package","version":"[6.0.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}