﻿namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry;

public class BitacoraEntryFilterDto : BaseEntityFilterDto
{
    public string? Comments { get; set; }
    // Waste Type filter
    public int? WasteTypeId { get; set; }

    // Weight filters
    public decimal? MinGrossWeight { get; set; }
    public decimal? MaxGrossWeight { get; set; }
    public decimal? MinNetWeightLB { get; set; }
    public decimal? MaxNetWeightLB { get; set; }
    public decimal? MinNetWeightKG { get; set; }
    public decimal? MaxNetWeightKG { get; set; }

    // Price filters
    public decimal? MinUnitPrice { get; set; }
    public decimal? MaxUnitPrice { get; set; }

    // Container Type filter
    public int? ContainerTypeId { get; set; }

    // Area filter
    public int? AreaId { get; set; }

    // Date filters
    public DateTime? EntryDateFrom { get; set; }
    public DateTime? EntryDateTo { get; set; }
    public DateTime? DepartureDateFrom { get; set; }
    public DateTime? DepartureDateTo { get; set; }

    // User filter
    public string? EnteredBy { get; set; }
}
