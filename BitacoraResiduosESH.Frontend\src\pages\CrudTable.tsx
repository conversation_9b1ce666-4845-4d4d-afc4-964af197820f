import React, {useCallback, useMemo, useState} from 'react';
import {
    type ColumnDef,
    type ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    type PaginationState,
    type SortingState,
    useReactTable,
} from '@tanstack/react-table';
import {ArrowDown, ArrowUp, ArrowUpDown, Edit, Eye, Plus, Trash2} from 'lucide-react';
import FuzzySearch from '../components/table/FuzzySearch';
import TablePagination from '../components/table/TablePagination';
import ConfirmModal from '../components/table/ConfirmModal';
import DetailsModal from '../components/table/DetailsModal';
import FormModal from '../components/form/FormModal';

// Tipo de datos
interface DataItem {
    id: number;
    name: string;
    updated: string;
    updatedBy: string;
}

// Datos dummy
const generateDummyData = (): DataItem[] => {
    const names = [
        'Producto A', 'Producto B', 'Producto C', 'Producto D', 'Producto E',
        'Servicio X', 'Servicio Y', 'Servicio Z', 'Componente 1', 'Componente 2',
        'Material Alpha', 'Material Beta', 'Material Gamma', 'Herramienta 1', 'Herramienta 2',
        'Equipo A', 'Equipo B', 'Equipo C', 'Sistema X', 'Sistema Y'
    ];

    const users = ['Juan Pérez', '<PERSON> García', 'Carlos López', 'Ana Martínez', 'Luis Rodríguez'];

    return Array.from({length: 100}, (_, index) => ({
        id: index + 1,
        name: names[Math.floor(Math.random() * names.length)],
        updated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedBy: users[Math.floor(Math.random() * users.length)],
    }));
};

const CrudTable: React.FC = () => {
    // Estados
    const [data, setData] = useState<DataItem[]>(() => generateDummyData());
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [globalFilter, setGlobalFilter] = useState('');
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: 0,
        pageSize: 10,
    });

    // Estados de modales
    const [isFormModalOpen, setIsFormModalOpen] = useState(false);
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<DataItem | null>(null);
    const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
    const [loading, setLoading] = useState(false);

    // Función de búsqueda fuzzy
    const fuzzyFilter = useCallback(
        (row: any, columnId: string, value: string) => {
            const itemValue = row.getValue(columnId);
            if (!value) return true;

            const searchValue = value.toLowerCase();
            const itemString = String(itemValue).toLowerCase();

            // Búsqueda simple que incluye coincidencias parciales
            return itemString.includes(searchValue);
        },
        []
    );

    // Definición de columnas
    const columns = useMemo<ColumnDef<DataItem>[]>(
        () => [
            {
                accessorKey: 'id',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>ID</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => (
                    <span style={{color: 'var(--text-primary)'}}>
            {row.getValue('id')}
          </span>
                ),
                size: 80,
            },
            {
                accessorKey: 'name',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>Nombre</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => (
                    <span style={{color: 'var(--text-primary)'}}>
            {row.getValue('name')}
          </span>
                ),
            },
            {
                accessorKey: 'updated',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>Última Actualización</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => {
                    const date = new Date(row.getValue('updated'));
                    return (
                        <span style={{color: 'var(--text-primary)'}}>
              {date.toLocaleDateString('es-ES')}
            </span>
                    );
                },
            },
            {
                accessorKey: 'updatedBy',
                header: ({column}) => (
                    <button
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                        className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                        style={{color: 'var(--text-primary)'}}
                    >
                        <span>Actualizado Por</span>
                        {column.getIsSorted() === 'asc' ? (
                            <ArrowUp className="h-4 w-4"/>
                        ) : column.getIsSorted() === 'desc' ? (
                            <ArrowDown className="h-4 w-4"/>
                        ) : (
                            <ArrowUpDown className="h-4 w-4"/>
                        )}
                    </button>
                ),
                cell: ({row}) => (
                    <span style={{color: 'var(--text-primary)'}}>
            {row.getValue('updatedBy')}
          </span>
                ),
            },
            {
                id: 'actions',
                header: 'Acciones',
                cell: ({row}) => {
                    const item = row.original;
                    return (
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => handleViewDetails(item)}
                                className="p-1 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/20 transition-colors duration-200"
                                style={{color: '#3b82f6'}}
                                title="Ver detalles"
                            >
                                <Eye className="h-4 w-4"/>
                            </button>
                            <button
                                onClick={() => handleEdit(item)}
                                className="p-1 rounded-md hover:bg-yellow-100 dark:hover:bg-yellow-900/20 transition-colors duration-200"
                                style={{color: '#d97706'}}
                                title="Editar"
                            >
                                <Edit className="h-4 w-4"/>
                            </button>
                            <button
                                onClick={() => handleDelete(item)}
                                className="p-1 rounded-md hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors duration-200"
                                style={{color: '#dc2626'}}
                                title="Eliminar"
                            >
                                <Trash2 className="h-4 w-4"/>
                            </button>
                        </div>
                    );
                },
                size: 120,
            },
        ],
        []
    );

    // Configuración de la tabla
    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnFilters,
            globalFilter,
            pagination,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        globalFilterFn: fuzzyFilter,
        enableSorting: true,
        enableColumnFilters: true,
        enableGlobalFilter: true,
    });

    // Handlers
    const handleCreate = () => {
        setFormMode('create');
        setSelectedItem(null);
        setIsFormModalOpen(true);
    };

    const handleEdit = (item: DataItem) => {
        setFormMode('edit');
        setSelectedItem(item);
        setIsFormModalOpen(true);
    };

    const handleViewDetails = (item: DataItem) => {
        if (!item) return;
        setSelectedItem(item);
        setIsDetailsModalOpen(true);
    };

    const handleDelete = (item: DataItem) => {
        setSelectedItem(item);
        setIsDeleteModalOpen(true);
    };

    const handleFormSubmit = async (formData: { id?: number; name: string }) => {
        setLoading(true);

        try {
            // Simular llamada a API
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (formMode === 'create') {
                const newItem: DataItem = {
                    id: Math.max(...data.map(item => item.id)) + 1,
                    name: formData.name,
                    updated: new Date().toISOString(),
                    updatedBy: 'Usuario Actual',
                };
                setData(prev => [...prev, newItem]);
            } else {
                setData(prev => prev.map(item =>
                    item.id === formData.id
                        ? {...item, name: formData.name, updated: new Date().toISOString(), updatedBy: 'Usuario Actual'}
                        : item
                ));
            }
        } catch (error) {
            console.error('Error al guardar:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleConfirmDelete = async () => {
        if (!selectedItem) return;

        setLoading(true);

        try {
            // Simular llamada a API
            await new Promise(resolve => setTimeout(resolve, 1000));

            setData(prev => prev.filter(item => item.id !== selectedItem.id));
            setIsDeleteModalOpen(false);
            setSelectedItem(null);
        } catch (error) {
            console.error('Error al eliminar:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1
                        className="text-2xl font-bold"
                        style={{color: 'var(--text-primary)'}}
                    >
                        Gestión de Elementos
                    </h1>
                    <p
                        className="text-sm mt-1"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Administra los elementos del sistema
                    </p>
                </div>
                <button
                    onClick={handleCreate}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                >
                    <Plus className="h-4 w-4"/>
                    <span>Crear Nuevo</span>
                </button>
            </div>

            {/* Filtros y búsqueda */}
            <div className="flex items-center justify-between">
                <FuzzySearch
                    value={globalFilter}
                    onChange={setGlobalFilter}
                    placeholder="Buscar elementos..."
                    className="w-80"
                />
            </div>

            {/* Tabla */}
            <div
                className="border rounded-lg overflow-hidden"
                style={{borderColor: 'var(--border-color)'}}
            >
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead
                            className="border-b"
                            style={{borderColor: 'var(--border-color)', backgroundColor: 'var(--bg-secondary)'}}
                        >
                        {table.getHeaderGroups().map(headerGroup => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <th
                                        key={header.id}
                                        className="px-4 py-3 text-left text-sm font-medium"
                                        style={{color: 'var(--text-primary)'}}
                                    >
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </th>
                                ))}
                            </tr>
                        ))}
                        </thead>
                        <tbody>
                        {table.getRowModel().rows.map(row => (
                            <tr
                                key={row.id}
                                className="border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                                style={{borderColor: 'var(--border-color)'}}
                            >
                                {row.getVisibleCells().map(cell => (
                                    <td
                                        key={cell.id}
                                        className="px-4 py-3 text-sm"
                                    >
                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                    </td>
                                ))}
                            </tr>
                        ))}
                        </tbody>
                    </table>
                </div>

                {/* Paginación */}
                <TablePagination
                    currentPage={table.getState().pagination.pageIndex + 1}
                    totalPages={table.getPageCount()}
                    totalItems={table.getFilteredRowModel().rows.length}
                    pageSize={table.getState().pagination.pageSize}
                    onPageChange={(page) => table.setPageIndex(page - 1)}
                    onPageSizeChange={(pageSize) => table.setPageSize(pageSize)}
                />
            </div>

            {/* Modales */}
            <FormModal
                isOpen={isFormModalOpen}
                onClose={() => setIsFormModalOpen(false)}
                onSubmit={handleFormSubmit}
                data={selectedItem || undefined}
                mode={formMode}
                loading={loading}
            />

            <DetailsModal
                isOpen={isDetailsModalOpen && !!selectedItem}
                onClose={() => setIsDetailsModalOpen(false)}
                data={selectedItem || undefined}
            />

            <ConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                title="Confirmar Eliminación"
                message={`¿Estás seguro de que deseas eliminar "${selectedItem?.name}"? Esta acción no se puede deshacer.`}
                confirmText="Eliminar"
                variant="danger"
                loading={loading}
            />
        </div>
    );
};

export default CrudTable; 