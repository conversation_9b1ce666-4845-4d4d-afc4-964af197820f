# Controladores - Documentación de API

## Descripción

Este directorio contiene todos los controladores de la API REST. La arquitectura incluye controladores genéricos y
específicos para diferentes tipos de entidades.

## Tipos de Controladores

### 1. Controlador Genérico para BaseEntity (`GenericController<TEntity, TDto, TFilterDto>`)

El controlador genérico proporciona funcionalidad CRUD completa para cualquier entidad que herede de `BaseEntity`. Es el
nivel más básico de abstracción.

#### Características

- **Herencia**: `ControllerBase`
- **Genérico**: `TEntity : BaseEntity`, `TDto : BaseEntityDto`, `TFilterDto : BaseEntityFilterDto`
- **Funcionalidad**: Operaciones CRUD completas con logging automático
- **Auditoría**: Manejo automático de fechas y usuarios

#### Endpoints Disponibles

##### Operaciones de Lectura

- `GET /api/{controller}/{id}` - Obtener por ID
- `GET /api/{controller}` - Obtener todos
- `GET /api/{controller}/paged` - Obtener paginados con filtros
- `GET /api/{controller}/active` - Obtener solo activos
- `GET /api/{controller}/count` - Contar elementos

##### Operaciones de Escritura

- `POST /api/{controller}` - Agregar un elemento
- `POST /api/{controller}/batch` - Agregar múltiples elementos
- `PUT /api/{controller}` - Actualizar un elemento
- `PUT /api/{controller}/batch` - Actualizar múltiples elementos

##### Operaciones de Eliminación

- `DELETE /api/{controller}/{id}` - Eliminar elemento (soft delete)
- `DELETE /api/{controller}/batch` - Eliminar múltiples elementos
- `DELETE /api/{controller}/{id}/permanent` - Eliminar permanentemente
- `DELETE /api/{controller}/batch/permanent` - Eliminar múltiples permanentemente

##### Operaciones de Estado

- `PATCH /api/{controller}/{id}/activate` - Activar elemento
- `PATCH /api/{controller}/{id}/deactivate` - Desactivar elemento

##### Operaciones de Validación

- `GET /api/{controller}/{id}/exists` - Verificar existencia

##### Operaciones de Búsqueda

- `GET /api/{controller}/search` - Búsqueda personalizada (virtual, debe sobrescribirse)

#### Ejemplo de Uso

```csharp
[ApiController]
[Route("api/[controller]")]
public class MiEntidadController : GenericController<MiEntidad, MiEntidadDto, MiEntidadFilterDto>
{
    public MiEntidadController(IMiEntidadService service) : base(service)
    {
        // Constructor simple - hereda toda la funcionalidad
    }

    // Agregar endpoints específicos si es necesario
    [HttpGet("especifico")]
    public ActionResult GetEspecifico()
    {
        return Ok("Endpoint específico");
    }
}
```

### 2. Controlador Genérico para SimpleEntity (

`SimpleEntityController<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>`)

El controlador genérico para entidades que heredan de `SimpleEntity` (que incluyen `Name` y `Description`).

#### Características

- **Herencia**: `ControllerBase`
- **Genérico**: `TEntity : SimpleEntity`, `TDto : SimpleEntityDto`, etc.
- **Funcionalidad**: CRUD completo + búsquedas por nombre y descripción
- **DTOs**: Manejo de CreateDto y UpdateDto separados

#### Endpoints Adicionales

- `GET /api/{controller}/search/name` - Buscar por nombre
- `GET /api/{controller}/search/description` - Buscar por descripción

### 3. Controladores Específicos

#### AuthController

Maneja la autenticación OpenID Connect:

- `GET /api/auth/login` - Iniciar flujo de autenticación
- `GET /api/auth/callback` - Procesar callback de autorización
- `POST /api/auth/refresh` - Refrescar token
- `POST /api/auth/revoke` - Revocar token
- `GET /api/auth/userinfo` - Obtener información del usuario

#### CategoryController

Gestiona categorías con funcionalidad jerárquica:

- Endpoints CRUD básicos
- `GET /api/category/root` - Categorías raíz
- `GET /api/category/subcategories/{parentId}` - Subcategorías
- `GET /api/category/tree` - Árbol completo de categorías
- `GET /api/category/with-icon` - Categorías con icono

#### ExampleController

Controlador específico para ExampleEntity:

- Endpoints CRUD completos
- `GET /api/example/category/{categoryId}` - Por categoría
- `GET /api/example/test` - Endpoint de prueba

#### ItemController

Controlador que hereda de SimpleEntityController:

- Funcionalidad CRUD automática
- Sin endpoints adicionales

#### ProductController

Controlador que hereda de SimpleEntityController + endpoints específicos:

- `GET /api/product/by-price-range` - Por rango de precio
- `GET /api/product/by-sku` - Por SKU
- `GET /api/product/in-stock` - En stock
- `GET /api/product/out-of-stock` - Sin stock
- `GET /api/product/by-stock-range` - Por rango de stock

#### UserController

Gestiona información del usuario autenticado:

- `GET /api/user/profile` - Perfil completo
- `GET /api/user/info` - Información básica
- `GET /api/user/authenticated` - Verificar autenticación

## Jerarquía de Controladores

```
ControllerBase
├── GenericController<TEntity, TDto, TFilterDto> (BaseEntity)
│   └── ExampleGenericController (ejemplo de uso)
├── SimpleEntityController<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto> (SimpleEntity)
│   ├── ItemController
│   └── ProductController
└── Controladores Específicos
    ├── AuthController
    ├── CategoryController
    ├── ExampleController
    └── UserController
```

## Características Comunes

### 1. Logging Automático

Todos los controladores incluyen logging detallado usando NLog:

- Debug para operaciones normales
- Info para operaciones exitosas
- Warn para advertencias
- Error para excepciones

### 2. Manejo de Errores

- Try-catch en todos los métodos
- Respuestas HTTP apropiadas
- Mensajes de error descriptivos

### 3. Validación

- Validación de modelo automática
- Validación de datos de entrada
- Verificación de existencia

### 4. Auditoría

- Registro de usuario que realiza la operación
- Timestamps automáticos
- Trazabilidad completa

### 5. Documentación

- Comentarios XML para Swagger
- Atributos ProducesResponseType
- Descripción detallada de parámetros

## Códigos de Estado HTTP

- **200 OK**: Operación exitosa
- **201 Created**: Elemento creado exitosamente
- **400 Bad Request**: Error en la solicitud
- **404 Not Found**: Elemento no encontrado
- **409 Conflict**: Conflicto (ej: nombre duplicado)
- **401 Unauthorized**: No autenticado
- **403 Forbidden**: No autorizado

## Ejemplos de Uso

### Crear un Controlador Genérico

```csharp
// 1. Crear la entidad
public class MiEntidad : BaseEntity
{
    public string PropiedadEspecifica { get; set; }
}

// 2. Crear los DTOs
public class MiEntidadDto : BaseEntityDto
{
    public string PropiedadEspecifica { get; set; }
}

public class MiEntidadFilterDto : BaseEntityFilterDto
{
    public string? PropiedadEspecifica { get; set; }
}

// 3. Crear el servicio
public interface IMiEntidadService : IGenericService<MiEntidad, MiEntidadDto, MiEntidadFilterDto>
{
    // Métodos específicos si son necesarios
}

// 4. Crear el controlador
[ApiController]
[Route("api/[controller]")]
public class MiEntidadController : GenericController<MiEntidad, MiEntidadDto, MiEntidadFilterDto>
{
    public MiEntidadController(IMiEntidadService service) : base(service)
    {
        // ¡Listo! Tienes todos los endpoints CRUD automáticamente
    }
}
```

### Agregar Endpoints Específicos

```csharp
public class MiEntidadController : GenericController<MiEntidad, MiEntidadDto, MiEntidadFilterDto>
{
    public MiEntidadController(IMiEntidadService service) : base(service)
    {
    }

    // Endpoint específico
    [HttpGet("especifico")]
    public ActionResult GetEspecifico()
    {
        return Ok("Endpoint específico");
    }

    // Sobrescribir búsqueda personalizada
    public override async Task<ActionResult<IEnumerable<MiEntidadDto>>> Search(
        [FromQuery] string predicate,
        [FromQuery] bool includeDeleted = false)
    {
        // Implementación específica
        return Ok(await _service.FindAsync(e => e.PropiedadEspecifica.Contains(predicate), includeDeleted));
    }
}
```

## Configuración en Program.cs

Para usar los controladores, asegúrate de registrar los servicios:

```csharp
// En Program.cs
builder.Services.AddRepositories();
builder.Services.AddApplicationServices();
builder.Services.AddControllers();
```

## Consideraciones de Seguridad

1. **Autenticación**: Implementa autenticación JWT o similar
2. **Autorización**: Agrega atributos `[Authorize]` según los roles necesarios
3. **Validación**: Los DTOs incluyen validaciones de entrada
4. **Rate Limiting**: Considera implementar rate limiting
5. **CORS**: Configura CORS apropiadamente

## Ventajas del Sistema

1. **Reutilización de código**: Funcionalidad CRUD automática
2. **Consistencia**: Mismo patrón en todos los controladores
3. **Mantenibilidad**: Cambios centralizados
4. **Escalabilidad**: Fácil agregar nuevas entidades
5. **Documentación**: Generación automática de Swagger
6. **Logging**: Trazabilidad completa
7. **Auditoría**: Control automático de cambios 