﻿namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class SimpleEntityController<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>(
    ISimpleEntityService<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto> service)
    : ControllerBase
    where TEntity : SimpleEntity
    where TDto : SimpleEntityDto
    where TCreateDto : CreateSimpleEntityDto
    where TUpdateDto : UpdateSimpleEntityDto
    where TFilterDto : SimpleEntityFilterDto
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    #region Operaciones de validación

    /// <summary>
    ///     Verifica si existe un elemento
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>True si existe, false en caso contrario</returns>
    [HttpGet("{id:int}/exists")]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<bool>> Exists(int id, [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Verificación de existencia para {EntityType} ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, id, includeDeleted);

        try
        {
            var result = await service.ExistsAsync(id, includeDeleted);
            Logger.Debug("Verificación de existencia completada para {EntityType} ID {Id}: {Exists}",
                typeof(TEntity).Name, id, result);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al verificar existencia del {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al verificar la existencia del {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de lectura

    /// <summary>
    ///     Obtiene un elemento por su ID
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elemento encontrado</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(SimpleEntityDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TDto>> GetById(int id, [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Solicitud GET para {EntityType} con ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, id, includeDeleted);

        try
        {
            var result = await service.GetByIdAsync(id, includeDeleted);

            if (result == null)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Debug("{EntityType} con ID {Id} recuperado exitosamente", typeof(TEntity).Name, id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest("Error al obtener el elemento");
        }
    }

    /// <summary>
    ///     Obtiene todos los elementos con paginación
    /// </summary>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elementos paginados</returns>
    [HttpGet]
    [ProducesResponseType(typeof(PagedResponse<SimpleEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetAll(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug(
            "Solicitud GET para todos los {EntityType} paginados - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, pageNumber, pageSize, includeDeleted);

        try
        {
            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.GetAllAsync(filter);
            Logger.Debug("Recuperados {Count} {EntityType} de {TotalRecords} totales", result.Data.Count,
                typeof(TEntity).Name, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener todos los {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al obtener los {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Obtiene elementos paginados con filtros
    /// </summary>
    /// <param name="filter">Filtros de búsqueda</param>
    /// <returns>Elementos paginados</returns>
    [HttpGet("paged")]
    [ProducesResponseType(typeof(PagedResponse<SimpleEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetPaged([FromQuery] TFilterDto filter)
    {
        Logger.Debug(
            "Solicitud GET paginada para {EntityType} - Página: {PageNumber}, Tamaño: {PageSize}, Filtros: {@Filter}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, filter);

        try
        {
            var result = await service.GetPagedAsync(filter);
            Logger.Debug("Paginación completada para {EntityType} - Total: {TotalRecords}, Página: {PageNumber}",
                typeof(TEntity).Name, result.TotalRecords, result.PageNumber);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener {EntityType} paginados", typeof(TEntity).Name);
            return BadRequest($"Error al obtener los {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Obtiene solo elementos activos con paginación
    /// </summary>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <returns>Lista de elementos activos paginados</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(PagedResponse<SimpleEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetActive(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        Logger.Debug("Solicitud GET para {EntityType} activos paginados - Página: {PageNumber}, Tamaño: {PageSize}",
            typeof(TEntity).Name, pageNumber, pageSize);

        try
        {
            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = false
            };

            var result = await service.GetActiveAsync(filter);
            Logger.Debug("Recuperados {Count} {EntityType} activos de {TotalRecords} totales", result.Data.Count,
                typeof(TEntity).Name, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener {EntityType} activos", typeof(TEntity).Name);
            return BadRequest($"Error al obtener los {typeof(TEntity).Name} activos");
        }
    }

    /// <summary>
    ///     Busca elementos por nombre con paginación
    /// </summary>
    /// <param name="name">Nombre a buscar</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elementos que coinciden con el nombre paginados</returns>
    [HttpGet("search/name")]
    [ProducesResponseType(typeof(PagedResponse<SimpleEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetByName(
        [FromQuery] string name,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug(
            "Búsqueda por nombre paginada para {EntityType}: {Name}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, name, pageNumber, pageSize, includeDeleted);

        try
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                Logger.Warn("Búsqueda por nombre con parámetro vacío para {EntityType}", typeof(TEntity).Name);
                return BadRequest("El nombre es requerido");
            }

            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.GetByNameAsync(name, filter, includeDeleted);
            Logger.Debug(
                "Búsqueda por nombre paginada completada para {EntityType} - {Count} resultados de {TotalRecords} totales",
                typeof(TEntity).Name, result.Data.Count, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar {EntityType} por nombre: {Name}", typeof(TEntity).Name, name);
            return BadRequest("Error al buscar elementos");
        }
    }

    /// <summary>
    ///     Busca elementos por descripción con paginación
    /// </summary>
    /// <param name="description">Descripción a buscar</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elementos que coinciden con la descripción paginados</returns>
    [HttpGet("search/description")]
    [ProducesResponseType(typeof(PagedResponse<SimpleEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> GetByDescription(
        [FromQuery] string description,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug(
            "Búsqueda por descripción paginada para {EntityType}: {Description}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, description, pageNumber, pageSize, includeDeleted);

        try
        {
            if (string.IsNullOrWhiteSpace(description))
            {
                Logger.Warn("Búsqueda por descripción con parámetro vacío para {EntityType}", typeof(TEntity).Name);
                return BadRequest("La descripción es requerida");
            }

            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.GetByDescriptionAsync(description, filter, includeDeleted);
            Logger.Debug(
                "Búsqueda por descripción paginada completada para {EntityType} - {Count} resultados de {TotalRecords} totales",
                typeof(TEntity).Name, result.Data.Count, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar {EntityType} por descripción: {Description}", typeof(TEntity).Name,
                description);
            return BadRequest("Error al buscar elementos");
        }
    }

    /// <summary>
    ///     Realiza búsqueda fuzzy en múltiples campos con paginación
    /// </summary>
    /// <param name="q">Término de búsqueda</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir elementos eliminados</param>
    /// <returns>Elementos que coinciden con el término de búsqueda paginados</returns>
    [HttpGet("search")]
    [ProducesResponseType(typeof(PagedResponse<SimpleEntityDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResponse<TDto>>> FuzzySearch(
        [FromQuery] string q,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug(
            "Búsqueda fuzzy para {EntityType}: {SearchTerm}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, q, pageNumber, pageSize, includeDeleted);

        try
        {
            if (string.IsNullOrWhiteSpace(q))
            {
                Logger.Warn("Búsqueda fuzzy con parámetro vacío para {EntityType}", typeof(TEntity).Name);
                return BadRequest("El término de búsqueda es requerido");
            }

            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.FuzzySearchAsync(q, filter, includeDeleted);
            Logger.Debug(
                "Búsqueda fuzzy completada para {EntityType} - {Count} resultados de {TotalRecords} totales",
                typeof(TEntity).Name, result.Data.Count, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error en búsqueda fuzzy para {EntityType}: {SearchTerm}", typeof(TEntity).Name, q);
            return BadRequest("Error al realizar la búsqueda");
        }
    }

    #endregion

    #region Operaciones de escritura

    /// <summary>
    ///     Crea un nuevo elemento
    /// </summary>
    /// <param name="dto">Datos del elemento a crear</param>
    /// <returns>Elemento creado</returns>
    [HttpPost]
    [ProducesResponseType(typeof(SimpleEntityDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<ActionResult<TDto>> Create([FromBody] TCreateDto dto)
    {
        Logger.Debug("Solicitud POST para crear {EntityType}: {@Dto}", typeof(TEntity).Name, dto);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud POST para {EntityType}: {@ModelState}", typeof(TEntity).Name,
                    ModelState);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var createdBy = User.Identity?.Name ?? "system";

            var result = await service.CreateAsync(dto, createdBy);

            Logger.Info("{EntityType} creado exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
                result.Id, createdBy);
            return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
        }
        catch (InvalidOperationException ex)
        {
            Logger.Warn(ex, "Error de validación al crear {EntityType}", typeof(TEntity).Name);
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al crear {EntityType}", typeof(TEntity).Name);
            return BadRequest($"Error al crear el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Actualiza un elemento existente
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <param name="dto">Datos actualizados</param>
    /// <returns>Elemento actualizado</returns>
    [HttpPut("{id:int}")]
    [ProducesResponseType(typeof(SimpleEntityDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<ActionResult<TDto>> Update(int id, [FromBody] TUpdateDto dto)
    {
        Logger.Debug("Solicitud PUT para actualizar {EntityType} ID: {Id}, DTO: {@Dto}", typeof(TEntity).Name, id, dto);

        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Modelo inválido en solicitud PUT para {EntityType} ID: {Id}", typeof(TEntity).Name, id);
                return BadRequest(ModelState);
            }

            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.UpdateAsync(id, dto, updatedBy);

            Logger.Info("{EntityType} con ID {Id} actualizado exitosamente por usuario: {User}", typeof(TEntity).Name,
                id, updatedBy);
            return Ok(result);
        }
        catch (InvalidOperationException ex)
        {
            Logger.Warn(ex, "Error de validación al actualizar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al actualizar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al actualizar el {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de eliminación

    /// <summary>
    ///     Elimina un elemento (soft delete)
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> Delete(int id)
    {
        Logger.Debug("Solicitud DELETE para {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var deletedBy = User.Identity?.Name ?? "system";

            var result = await service.DeleteAsync(id, deletedBy);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para eliminar", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} eliminado exitosamente por usuario: {User}", typeof(TEntity).Name, id,
                deletedBy);
            return Ok(new { message = "Elemento eliminado correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al eliminar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al eliminar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Elimina un elemento permanentemente (hard delete)
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpDelete("{id:int}/permanent")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> HardDelete(int id)
    {
        Logger.Debug("Solicitud DELETE permanente para {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            var result = await service.HardDeleteAsync(id);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para eliminación permanente", typeof(TEntity).Name,
                    id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} eliminado permanentemente", typeof(TEntity).Name, id);
            return Ok(new { message = "Elemento eliminado permanentemente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al eliminar permanentemente {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al eliminar el {typeof(TEntity).Name}");
        }
    }

    #endregion

    #region Operaciones de activación/desactivación

    /// <summary>
    ///     Activa un elemento
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpPatch("{id:int}/activate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> Activate(int id)
    {
        Logger.Debug("Solicitud PATCH para activar {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.ActivateAsync(id, updatedBy);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para activar", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} activado exitosamente por usuario: {User}", typeof(TEntity).Name, id,
                updatedBy);
            return Ok(new { message = "Elemento activado correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al activar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al activar el {typeof(TEntity).Name}");
        }
    }

    /// <summary>
    ///     Desactiva un elemento
    /// </summary>
    /// <param name="id">ID del elemento</param>
    /// <returns>Resultado de la operación</returns>
    [HttpPatch("{id:int}/deactivate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> Deactivate(int id)
    {
        Logger.Debug("Solicitud PATCH para desactivar {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        try
        {
            // En un entorno real, obtendrías el usuario del contexto de autenticación
            var updatedBy = User.Identity?.Name ?? "system";

            var result = await service.DeactivateAsync(id, updatedBy);

            if (!result)
            {
                Logger.Warn("{EntityType} con ID {Id} no encontrado para desactivar", typeof(TEntity).Name, id);
                return NotFound($"No se encontró el elemento con ID {id}");
            }

            Logger.Info("{EntityType} con ID {Id} desactivado exitosamente por usuario: {User}", typeof(TEntity).Name,
                id, updatedBy);
            return Ok(new { message = "Elemento desactivado correctamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al desactivar {EntityType} con ID {Id}", typeof(TEntity).Name, id);
            return BadRequest($"Error al desactivar el {typeof(TEntity).Name}");
        }
    }

    #endregion
}