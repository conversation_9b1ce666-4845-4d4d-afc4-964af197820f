// Interfaces genéricas para entidades SimpleEntity
export interface SimpleEntity {
    id: number;
    name: string;
    description?: string;
    created: string;
    createdString: string;
    createdBy: string;
    updated?: string;
    updatedString?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deleted?: string;
    deletedString?: string;
    deletedBy?: string;
    active: boolean;

    // Propiedades para fechas locales
    createdLocal: string;
    createdLocalString: string;
    updatedLocal?: string;
    updatedLocalString?: string;
    deletedLocal?: string;
    deletedLocalString?: string;
}

export interface CreateSimpleEntityDto {
    name: string;
    description?: string;
    active?: boolean;
}

export interface UpdateSimpleEntityDto {
    id: number;
    name: string;
    description?: string;
    active?: boolean;
}

export interface SimpleEntityFilterDto {
    pageNumber: number;
    pageSize: number;
    includeDeleted: boolean;
    name?: string;
    description?: string;
}