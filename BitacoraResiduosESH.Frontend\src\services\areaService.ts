import type { Area, CreateAreaDto, UpdateAreaDto } from '@/types/area';
import { BaseSimpleEntityService } from './simpleEntityService';

/**
 * Service for Area entity operations
 * Extends the base SimpleEntity service with Area-specific typing
 */
class AreaService extends BaseSimpleEntityService<Area, CreateAreaDto, UpdateAreaDto> {
    constructor() {
        super('areas'); // Pass the API endpoint base URL
    }

    // Add any Area-specific methods here if needed in the future
    // For now, all standard CRUD operations are inherited from BaseSimpleEntityService
}

// Export a singleton instance
export const areaService = new AreaService();

// Alternative approach using factory utilities:
// import { createSimpleEntityService } from '@/utils/simpleEntityFactory';
// export const areaService = createSimpleEntityService<Area, CreateAreaDto, UpdateAreaDto>('areas');