import React, {useEffect, useRef, useState} from "react";
import ThemeToggle from "./ThemeToggle";
import {Bell, HelpCircle, LogOut, Settings, User} from "lucide-react";
import Tooltip from "./Tooltip";
import {useAuthStore} from "../stores/authStore";
import {usePermissions} from "../hooks/usePermissions";
import {toast} from "sonner";

interface HeaderProps {
    onMenuToggle: () => void;
}

const Header: React.FC<HeaderProps> = ({onMenuToggle}) => {
    const {user, logout} = useAuthStore();
    const {userRole} = usePermissions();
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const userMenuRef = useRef<HTMLDivElement>(null);

    const handleLogout = () => {
        logout();
        toast.success('Sesión cerrada exitosamente');
        setIsUserMenuOpen(false);
    };

    // Cerrar menú al hacer clic fuera
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setIsUserMenuOpen(false);
            }
        };

        if (isUserMenuOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isUserMenuOpen]);

    // Obtener iniciales del usuario
    const getUserInitials = (name: string): string => {
        return name
            .split(' ')
            .map(word => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const notifications = [
        {
            id: 1,
            title: "Nueva orden de trabajo",
            message: "Se ha creado la orden #1235 para el cliente ABC Corp",
            time: "Hace 5 minutos",
            type: "info"
        },
        {
            id: 2,
            title: "Reporte mensual listo",
            message: "El reporte de producción de enero está disponible",
            time: "Hace 1 hora",
            type: "success"
        },
        {
            id: 3,
            title: "Mantenimiento programado",
            message: "Mantenimiento preventivo en línea 3 mañana a las 8:00 AM",
            time: "Hace 2 horas",
            type: "warning"
        }
    ];

    const notificationContent = (
        <div>
            <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-sm">Notificaciones</h3>
                <span className="text-xs" style={{color: 'var(--text-secondary)'}}>
          {notifications.length} nuevas
        </span>
            </div>

            <div className="space-y-3 max-h-64 overflow-y-auto">
                {notifications.map((notification) => (
                    <div
                        key={notification.id}
                        className="p-3 rounded-lg transition-colors duration-200 cursor-pointer"
                        style={{backgroundColor: 'var(--hover-bg)'}}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                        }}
                    >
                        <div className="flex items-start space-x-3">
                            <div
                                className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                    notification.type === 'info' ? 'bg-blue-500' :
                                        notification.type === 'success' ? 'bg-green-500' :
                                            notification.type === 'warning' ? 'bg-yellow-500' :
                                                'bg-red-500'
                                }`}
                            />
                            <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium mb-1">
                                    {notification.title}
                                </p>
                                <p
                                    className="text-xs mb-2"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    {notification.message}
                                </p>
                                <p
                                    className="text-xs"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    {notification.time}
                                </p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            <div className="mt-3 pt-3 border-t" style={{borderColor: 'var(--border-color)'}}>
                <button
                    className="w-full text-center text-sm font-medium transition-colors duration-200"
                    style={{color: 'var(--text-primary)'}}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.color = '#3b82f6';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.color = 'var(--text-primary)';
                    }}
                >
                    Ver todas las notificaciones
                </button>
            </div>
        </div>
    );

    const userMenuContent = (
        <div className="py-2">
            <div className="p-3 border-b" style={{borderColor: 'var(--border-color)'}}>
                <div className="flex items-center space-x-3">
                    <div
                        className="w-10 h-10 rounded-full flex items-center justify-center"
                        style={{
                            backgroundColor: 'var(--text-secondary)',
                            color: 'var(--bg-secondary)'
                        }}
                    >
            <span className="text-sm font-medium">
              {user ? getUserInitials(user.name) : 'U'}
            </span>
                    </div>
                    <div>
                        <p className="text-sm font-medium">
                            {user?.name || 'Usuario'}
                        </p>
                        <p
                            className="text-xs"
                            style={{color: 'var(--text-secondary)'}}
                        >
                            {user?.email || user?.employeeNumber || 'Sin información'}
                        </p>
                        <p
                            className="text-xs font-medium"
                            style={{color: 'var(--text-secondary)'}}
                        >
                            Rol: {userRole || 'Sin rol'}
                        </p>
                    </div>
                </div>
            </div>

            <div className="py-1">
                <button
                    className="w-full text-left px-3 py-2 text-sm transition-colors duration-200 flex items-center space-x-2"
                    style={{color: 'var(--text-primary)'}}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    onClick={() => setIsUserMenuOpen(false)}
                >
                    <User size={16}/>
                    <span>Mi Perfil</span>
                </button>
                <button
                    className="w-full text-left px-3 py-2 text-sm transition-colors duration-200 flex items-center space-x-2"
                    style={{color: 'var(--text-primary)'}}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    onClick={() => setIsUserMenuOpen(false)}
                >
                    <Settings size={16}/>
                    <span>Configuración</span>
                </button>
                <button
                    className="w-full text-left px-3 py-2 text-sm transition-colors duration-200 flex items-center space-x-2"
                    style={{color: 'var(--text-primary)'}}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    onClick={() => setIsUserMenuOpen(false)}
                >
                    <HelpCircle size={16}/>
                    <span>Ayuda</span>
                </button>
            </div>

            <div className="border-t py-1" style={{borderColor: 'var(--border-color)'}}>
                <button
                    onClick={handleLogout}
                    className="w-full text-left px-3 py-2 text-sm transition-colors duration-200 flex items-center space-x-2"
                    style={{color: '#ef4444'}}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#fef2f2';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                >
                    <LogOut size={16}/>
                    <span>Cerrar Sesión</span>
                </button>
            </div>
        </div>
    );

    return (
        <header
            className="fixed top-0 left-0 right-0 z-10 shadow-sm border-b transition-all duration-200"
            style={{
                backgroundColor: 'var(--header-bg)',
                borderColor: 'var(--border-color)'
            }}
        >
            <div className="flex items-center justify-between h-16 px-4">
                {/* Left side - Menu button and title */}
                <div className="flex items-center space-x-4">
                    <button
                        onClick={onMenuToggle}
                        className="p-2 rounded-md transition-colors duration-200"
                        style={{
                            color: 'var(--text-primary)',
                            backgroundColor: 'transparent'
                        }}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                        }}
                    >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                  d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>

                    <div>
                        <h1
                            className="text-lg font-semibold"
                            style={{color: 'var(--text-primary)'}}
                        >
                            Sistema de Gestión de Herramientas
                        </h1>
                        <p
                            className="text-xs"
                            style={{color: 'var(--text-secondary)'}}
                        >
                            Molex Nogales
                        </p>
                    </div>
                </div>

                {/* Right side - Notifications, theme toggle, and user menu */}
                <div className="flex items-center space-x-2">
                    {/* Notifications */}
                    <Tooltip content={notificationContent} position="bottom">
                        <button
                            className="relative p-2 rounded-md transition-colors duration-200"
                            style={{
                                color: 'var(--text-primary)',
                                backgroundColor: 'transparent'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                            }}
                        >
                            <Bell className="w-5 h-5"/>
                            {notifications.length > 0 && (
                                <span
                                    className="absolute -top-1 -right-1 w-5 h-5 rounded-full text-xs flex items-center justify-center font-medium"
                                    style={{
                                        backgroundColor: '#ef4444',
                                        color: 'white'
                                    }}
                                >
                  {notifications.length}
                </span>
                            )}
                        </button>
                    </Tooltip>

                    {/* Theme Toggle */}
                    <ThemeToggle/>

                    {/* User Menu */}
                    <div className="relative" ref={userMenuRef}>
                        <button
                            className="flex items-center space-x-2 p-2 rounded-md transition-colors duration-200"
                            style={{
                                color: 'var(--text-primary)',
                                backgroundColor: isUserMenuOpen ? 'var(--hover-bg)' : 'transparent'
                            }}
                            onMouseEnter={(e) => {
                                if (!isUserMenuOpen) {
                                    e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                if (!isUserMenuOpen) {
                                    e.currentTarget.style.backgroundColor = 'transparent';
                                }
                            }}
                            onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                        >
                            <div
                                className="w-8 h-8 rounded-full flex items-center justify-center"
                                style={{
                                    backgroundColor: 'var(--text-secondary)',
                                    color: 'var(--bg-secondary)'
                                }}
                            >
                <span className="text-sm font-medium">
                  {user ? getUserInitials(user.name) : 'U'}
                </span>
                            </div>
                            <svg
                                className={`w-4 h-4 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>

                        {/* Dropdown Menu */}
                        {isUserMenuOpen && (
                            <div
                                className="absolute right-0 mt-2 w-64 rounded-md shadow-lg border transition-all duration-200 z-50"
                                style={{
                                    backgroundColor: 'var(--card-bg)',
                                    borderColor: 'var(--border-color)',
                                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                                }}
                            >
                                {userMenuContent}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;
