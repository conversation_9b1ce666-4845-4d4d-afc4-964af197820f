import React from 'react';
import {create} from 'zustand';
import {createJSONStorage, persist} from 'zustand/middleware';
import dayjs from 'dayjs';
import type {AuthState, LoginRequest, UserAuthInfo} from '../types/auth';
import {authService} from '../services/authService';

// Claves para localStorage
const STORAGE_KEYS = {
    AUTH_STORE: 'auth-store',
    TOKEN: 'auth-token',
    USER: 'auth-user',
} as const;

// Función para obtener token del localStorage
const getStoredToken = (): string | null => {
    return localStorage.getItem(STORAGE_KEYS.TOKEN);
};

// Función para obtener usuario del localStorage
const getStoredUser = (): UserAuthInfo | null => {
    const userStr = localStorage.getItem(STORAGE_KEYS.USER);
    return userStr ? JSON.parse(userStr) : null;
};

// Función para calcular la expiración del token
const calculateTokenExpiry = (expiresIn: number): Date => {
    return dayjs().add(expiresIn, 'minute').toDate();
};

// Función para verificar si un token está expirado
const isTokenExpired = (expiryDate: Date | null): boolean => {
    if (!expiryDate) return true;
    return dayjs().isAfter(dayjs(expiryDate));
};

// Función para verificar roles
const hasRole = (userRole: string, requiredRoles: string | string[]): boolean => {
    if (typeof requiredRoles === 'string') {
        return userRole === requiredRoles;
    }
    return requiredRoles.includes(userRole);
};

export const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            // Estado inicial
            isAuthenticated: false,
            isLoading: false,
            error: null,
            user: null,
            token: null,
            tokenExpiry: null,

            // Acciones
            login: async (credentials: LoginRequest): Promise<boolean> => {
                set({isLoading: true, error: null});

                try {
                    const response = await authService.login(credentials);

                    // Calcular expiración del token
                    const expiryDate = calculateTokenExpiry(response.expiresIn);

                    // Guardar en localStorage
                    localStorage.setItem(STORAGE_KEYS.TOKEN, response.token);
                    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.user));

                    // Actualizar estado
                    set({
                        isAuthenticated: true,
                        user: response.user,
                        token: response.token,
                        tokenExpiry: expiryDate,
                        isLoading: false,
                        error: null,
                    });

                    return true;
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Error de autenticación';
                    set({
                        isAuthenticated: false,
                        user: null,
                        token: null,
                        tokenExpiry: null,
                        isLoading: false,
                        error: errorMessage,
                    });
                    return false;
                }
            },

            logout: () => {
                const {token} = get();

                // Intentar revocar el token en el servidor
                if (token) {
                    authService.revokeToken(token).catch(() => {
                        // Ignorar errores al revocar token
                    });
                }

                // Limpiar localStorage
                localStorage.removeItem(STORAGE_KEYS.TOKEN);
                localStorage.removeItem(STORAGE_KEYS.USER);

                // Limpiar estado
                set({
                    isAuthenticated: false,
                    user: null,
                    token: null,
                    tokenExpiry: null,
                    error: null,
                });
            },

            refreshToken: async (): Promise<boolean> => {
                const {token} = get();

                if (!token) {
                    return false;
                }

                set({isLoading: true});

                try {
                    const response = await authService.refreshToken(token);

                    // Calcular nueva expiración
                    const expiryDate = calculateTokenExpiry(response.expiresIn);

                    // Actualizar localStorage
                    localStorage.setItem(STORAGE_KEYS.TOKEN, response.token);
                    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.user));

                    // Actualizar estado
                    set({
                        isAuthenticated: true,
                        user: response.user,
                        token: response.token,
                        tokenExpiry: expiryDate,
                        isLoading: false,
                        error: null,
                    });

                    return true;
                } catch (error) {
                    // Si falla el refresh, hacer logout
                    get().logout();
                    return false;
                }
            },

            validateToken: async (): Promise<boolean> => {
                const {token} = get();

                if (!token) {
                    return false;
                }

                try {
                    const user = await authService.validateToken(token);

                    // Actualizar usuario en estado y localStorage
                    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));

                    set({
                        isAuthenticated: true,
                        user,
                        error: null,
                    });

                    return true;
                } catch (error) {
                    // Si el token no es válido, hacer logout
                    get().logout();
                    return false;
                }
            },

            clearError: () => {
                set({error: null});
            },

            // Utilidades
            hasRole: (role: string | string[]): boolean => {
                const {user} = get();
                if (!user) return false;
                return hasRole(user.role, role);
            },

            isTokenExpired: (): boolean => {
                const {tokenExpiry} = get();
                return isTokenExpired(tokenExpiry);
            },

            getTokenExpiryTime: (): number => {
                const {tokenExpiry} = get();
                if (!tokenExpiry) return 0;
                return dayjs(tokenExpiry).valueOf();
            },
        }),
        {
            name: STORAGE_KEYS.AUTH_STORE,
            storage: createJSONStorage(() => localStorage),
            // Solo persistir ciertos campos
            partialize: (state) => ({
                isAuthenticated: state.isAuthenticated,
                user: state.user,
                token: state.token,
                tokenExpiry: state.tokenExpiry,
            }),
            // Restaurar estado al cargar
            onRehydrateStorage: () => (state) => {
                if (state) {
                    // Verificar si el token está expirado al cargar
                    if (state.tokenExpiry && isTokenExpired(state.tokenExpiry)) {
                        state.logout();
                    }
                }
            },
        }
    )
);

// Hook para verificar autenticación al cargar la app
export const useAuthInit = () => {
    const {validateToken, isAuthenticated, token} = useAuthStore();

    // Verificar token al cargar si existe
    React.useEffect(() => {
        if (token && !isAuthenticated) {
            validateToken();
        }
    }, [token, isAuthenticated, validateToken]);
};

// Hook para auto-refresh del token
export const useTokenRefresh = () => {
    const {refreshToken, isTokenExpired, getTokenExpiryTime} = useAuthStore();

    React.useEffect(() => {
        const checkTokenExpiry = () => {
            if (isTokenExpired()) {
                refreshToken();
            }
        };

        // Verificar cada minuto
        const interval = setInterval(checkTokenExpiry, 60000);

        return () => clearInterval(interval);
    }, [refreshToken, isTokenExpired]);
}; 