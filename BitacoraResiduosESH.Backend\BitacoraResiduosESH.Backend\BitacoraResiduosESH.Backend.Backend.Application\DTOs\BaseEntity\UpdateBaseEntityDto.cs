namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;

public class UpdateBaseEntityDto
{
    public int Id { get; set; }

    // Propiedades básicas que pueden ser útiles para actualizar entidades

    // No se incluyen propiedades específicas ya que BaseEntity solo tiene propiedades de auditoría
    // que se manejan automáticamente en el servicio

    // Las entidades que hereden de BaseEntity pueden agregar sus propiedades específicas
    // heredando de este DTO
}