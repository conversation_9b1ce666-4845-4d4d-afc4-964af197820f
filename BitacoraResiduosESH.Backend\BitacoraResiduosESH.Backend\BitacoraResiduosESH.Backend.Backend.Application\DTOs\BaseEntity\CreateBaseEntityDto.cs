namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;

public class CreateBaseEntityDto
{
    // Propiedades básicas que pueden ser útiles para crear entidades
    public bool Active { get; set; } = true;

    // No se incluyen propiedades específicas ya que BaseEntity solo tiene propiedades de auditoría
    // que se manejan automáticamente en el servicio

    // Las entidades que hereden de BaseEntity pueden agregar sus propiedades específicas
    // heredando de este DTO
}