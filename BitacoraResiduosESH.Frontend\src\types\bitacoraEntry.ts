import type { BaseEntity, CreateBaseEntityDto, UpdateBaseEntityDto, BaseEntityFilterDto } from "./baseEntity";
import type { Area } from "./area";
import type { WasteType } from "./wasteType";
import type { ContainerType } from "./containerType";

// BitacoraEntry entity types
export interface BitacoraEntry extends BaseEntity {
    comments?: string;

    // Waste Type relationship
    wasteTypeId: number;
    wasteType?: WasteType;

    // Weight properties
    grossWeight: number;
    tare: number;
    netWeightLB: number;
    netWeightKG: number;

    // Price
    unitPrice: number;

    // Container Type relationship
    containerTypeId: number;
    containerType?: ContainerType;

    // Area relationship
    areaId: number;
    area?: Area;

    // Dates
    entryDate: string; // ISO string
    entryDateString: string; // Formatted string
    departureDate?: string; // ISO string
    departureDateString?: string; // Formatted string

    // User who entered the record
    enteredBy: string;
}

export interface CreateBitacoraEntryDto extends CreateBaseEntityDto {
    // Basic properties
    name: string;
    description?: string;

    // Waste Type relationship
    wasteTypeId: number;

    // Weight properties
    grossWeight: number;
    tare: number;
    netWeightLB: number;
    netWeightKG: number;

    // Price
    unitPrice: number;

    // Container Type relationship
    containerTypeId: number;

    // Area relationship
    areaId: number;

    // Dates
    entryDate: string; // ISO string
    departureDate?: string; // ISO string

    // User who entered the record
    enteredBy: string;
}

export interface UpdateBitacoraEntryDto extends UpdateBaseEntityDto {
    id: number;
    // Basic properties
    name: string;
    description?: string;

    // Waste Type relationship
    wasteTypeId: number;

    // Weight properties
    grossWeight: number;
    tare: number;
    netWeightLB: number;
    netWeightKG: number;

    // Price
    unitPrice: number;

    // Container Type relationship
    containerTypeId: number;

    // Area relationship
    areaId: number;

    // Dates
    entryDate: string; // ISO string
    departureDate?: string; // ISO string

    // User who entered the record
    enteredBy: string;
}

export interface BitacoraEntryFilterDto extends BaseEntityFilterDto {
    // Basic properties filters
    name?: string;
    description?: string;

    // Waste Type filter
    wasteTypeId?: number;

    // Weight filters
    minGrossWeight?: number;
    maxGrossWeight?: number;
    minNetWeightLB?: number;
    maxNetWeightLB?: number;
    minNetWeightKG?: number;
    maxNetWeightKG?: number;

    // Price filters
    minUnitPrice?: number;
    maxUnitPrice?: number;

    // Container Type filter
    containerTypeId?: number;

    // Area filter
    areaId?: number;

    // Date filters
    entryDateFrom?: string;
    entryDateTo?: string;
    departureDateFrom?: string;
    departureDateTo?: string;

    // User filter
    enteredBy?: string;
}
