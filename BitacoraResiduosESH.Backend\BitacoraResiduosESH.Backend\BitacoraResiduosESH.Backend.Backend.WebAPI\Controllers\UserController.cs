namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Authorize(Roles = "Admin")] // Solo administradores pueden acceder
public class UserController(IUserService service)
    : GenericController<User, UserDto, UserFilterDto, CreateUserDto, UpdateUserDto>(service)
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    /// <summary>
    ///     Obtiene usuarios por nombre (búsqueda parcial) con paginación
    /// </summary>
    /// <param name="name">Nombre a buscar</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>Lista de usuarios que coinciden con el nombre paginados</returns>
    [HttpGet("search/name")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetByName(
        [FromQuery] string name,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                Logger.Warn("Intento de búsqueda por nombre con parámetro vacío");
                return BadRequest("El parámetro 'name' es requerido");
            }

            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.GetByNameAsync(name, filter, includeDeleted);
            Logger.Debug("Búsqueda por nombre paginada completada - {Count} resultados de {TotalRecords} totales",
                result.Data.Count, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar usuarios por nombre: {Name}", name);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Obtiene usuarios por email (búsqueda parcial) con paginación
    /// </summary>
    /// <param name="email">Email a buscar</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>Lista de usuarios que coinciden con el email paginados</returns>
    [HttpGet("search/email")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetByEmail(
        [FromQuery] string email,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                Logger.Warn("Intento de búsqueda por email con parámetro vacío");
                return BadRequest("El parámetro 'email' es requerido");
            }

            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            var result = await service.GetByEmailAsync(email, filter, includeDeleted);
            Logger.Debug("Búsqueda por email paginada completada - {Count} resultados de {TotalRecords} totales",
                result.Data.Count, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar usuarios por email: {Email}", email);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Obtiene un usuario por email exacto
    /// </summary>
    /// <param name="email">Email exacto a buscar</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>Usuario que coincide exactamente con el email</returns>
    [HttpGet("search/email/exact")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetByEmailExact([FromQuery] string email, [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                Logger.Warn("Intento de búsqueda por email exacto con parámetro vacío");
                return BadRequest("El parámetro 'email' es requerido");
            }

            var result = await service.GetByEmailExactAsync(email, includeDeleted);

            if (result == null)
            {
                Logger.Debug("Usuario no encontrado por email exacto: {Email}", email);
                return NotFound($"No se encontró un usuario con el email '{email}'");
            }

            Logger.Debug("Usuario encontrado por email exacto: {Email}", email);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar usuario por email exacto: {Email}", email);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Obtiene un usuario por número de empleado exacto
    /// </summary>
    /// <param name="employeeNumber">Número de empleado exacto a buscar</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>Usuario que coincide exactamente con el número de empleado</returns>
    [HttpGet("search/employee-number/exact")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetByEmployeeNumberExact([FromQuery] string employeeNumber,
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(employeeNumber))
            {
                Logger.Warn("Intento de búsqueda por numero de empleado exacto con parámetro vacío");
                return BadRequest("El parámetro 'employeeNumber' es requerido");
            }

            var result = await service.GetByEmployeeNumberExactAsync(employeeNumber, includeDeleted);

            if (result == null)
            {
                Logger.Debug("Usuario no encontrado por numero de empleado exacto: {EmployeeNumber}", employeeNumber);
                return NotFound($"No se encontró un usuario con el numero de empleado '{employeeNumber}'");
            }

            Logger.Debug("Usuario encontrado por numero de empleado exacto: {EmployeeNumber}", employeeNumber);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar usuario por numero de empleado exacto: {EmployeeNumber}", employeeNumber);
            return StatusCode(500, "Error interno del servidor");
        }
    }


    /// <summary>
    ///     Verifica si existe un usuario con el email especificado
    /// </summary>
    /// <param name="email">Email a verificar</param>
    /// <param name="excludeId">ID de usuario a excluir de la verificación</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>True si existe, False si no existe</returns>
    [HttpGet("exists/email")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ExistsByEmail([FromQuery] string email, [FromQuery] int? excludeId = null,
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                Logger.Warn("Intento de verificación de existencia con email vacío");
                return BadRequest("El parámetro 'email' es requerido");
            }

            var result = await service.ExistsByEmailAsync(email, excludeId, includeDeleted);
            Logger.Debug("Verificación de existencia por email completada para '{Email}': {Exists}", email, result);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al verificar existencia por email: {Email}", email);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Verifica si existe un usuario con el número de empleado especificado
    /// </summary>
    /// <param name="employeeNumber">Número de empleado a verificar</param>
    /// <param name="excludeId">ID de usuario a excluir de la verificación</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>True si existe, False si no existe</returns>
    [HttpGet("exists/employee-number")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ExistsByEmployeeNumber([FromQuery] string employeeNumber,
        [FromQuery] int? excludeId = null, [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(employeeNumber))
            {
                Logger.Warn("Intento de verificación de existencia con numero de empleado vacío");
                return BadRequest("El parámetro 'employeeNumber' es requerido");
            }

            var result = await service.ExistsByEmployeeNumberAsync(employeeNumber, excludeId, includeDeleted);
            Logger.Debug(
                "Verificación de existencia por numero de empleado completada para '{EmployeeNumber}': {Exists}",
                employeeNumber, result);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al verificar existencia por numero de empleado: {EmployeeNumber}", employeeNumber);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Endpoint personalizado para búsqueda avanzada con paginación
    /// </summary>
    /// <param name="predicate">Predicado de búsqueda</param>
    /// <param name="pageNumber">Número de página</param>
    /// <param name="pageSize">Tamaño de página</param>
    /// <param name="includeDeleted">Incluir usuarios eliminados</param>
    /// <returns>Lista de usuarios que coinciden con el predicado paginados</returns>
    [HttpGet("search/custom")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> CustomSearch(
        [FromQuery] string predicate,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] bool includeDeleted = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(predicate))
            {
                Logger.Warn("Intento de búsqueda personalizada con predicado vacío");
                return BadRequest("El parámetro 'predicate' es requerido");
            }

            var filter = new PaginationFilter
            {
                PageNumber = pageNumber,
                PageSize = pageSize,
                IncludeDeleted = includeDeleted
            };

            // Implementación específica: buscar por nombre que contenga el predicado
            var result = await service.GetByNameAsync(predicate, filter, includeDeleted);

            Logger.Debug("Búsqueda personalizada paginada completada - {Count} resultados de {TotalRecords} totales",
                result.Data.Count, result.TotalRecords);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error en búsqueda personalizada con predicado: {Predicate}", predicate);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Obtiene información básica del usuario autenticado
    /// </summary>
    /// <returns>Información básica del usuario</returns>
    [HttpGet("profile")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public ActionResult GetProfile()
    {
        Logger.Debug("Solicitud de perfil de usuario");

        try
        {
            // Obtener información del usuario del contexto (seteado por el middleware)
            if (HttpContext.Items.TryGetValue("UserInfo", out var userInfoObj) && userInfoObj is UserInfo userInfo)
            {
                var profile = new
                {
                    userInfo.Name,
                    Email = userInfo.PreferedUserName,
                    Timestamp = DateTime.Now
                };

                Logger.Info("Perfil obtenido para usuario: {Email}", userInfo.PreferedUserName);
                return Ok(profile);
            }

            Logger.Warn("No se encontró información del usuario en el contexto");
            return Unauthorized(new { error = "Usuario no autenticado" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener perfil de usuario");
            return BadRequest(new { error = "Error al obtener el perfil" });
        }
    }

    /// <summary>
    ///     Verifica si el usuario está autenticado
    /// </summary>
    /// <returns>Estado de autenticación</returns>
    [HttpGet("authenticated")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public ActionResult CheckAuthentication()
    {
        Logger.Debug("Verificación de autenticación");

        var isAuthenticated = HttpContext.Items.ContainsKey("UserInfo");
        var userInfo = HttpContext.Items["UserInfo"] as UserInfo;

        var result = new
        {
            IsAuthenticated = isAuthenticated,
            UserEmail = userInfo?.PreferedUserName,
            UserName = userInfo?.Name
        };

        Logger.Debug("Verificación completada: {IsAuthenticated}", isAuthenticated);
        return Ok(result);
    }

    /// <summary>
    ///     Permite a un usuario cambiar su propia contraseña
    /// </summary>
    /// <param name="dto">Datos para el cambio de contraseña</param>
    /// <returns>True si el cambio fue exitoso</returns>
    [HttpPost("change-password")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Intento de cambio de contraseña con datos inválidos");
                return BadRequest(ModelState);
            }

            var result = await service.ChangePasswordAsync(dto);

            Logger.Info("Contraseña cambiada exitosamente para usuario ID: {UserId}", dto.UserId);
            return Ok(new { success = true, message = "Contraseña cambiada exitosamente" });
        }
        catch (InvalidOperationException ex)
        {
            Logger.Warn("Error en cambio de contraseña: {Message}", ex.Message);
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al cambiar contraseña para usuario ID: {UserId}", dto.UserId);
            return StatusCode(500, "Error interno del servidor");
        }
    }

    /// <summary>
    ///     Permite a un administrador cambiar la contraseña de cualquier usuario
    /// </summary>
    /// <param name="dto">Datos para el cambio de contraseña por administrador</param>
    /// <returns>True si el cambio fue exitoso</returns>
    [HttpPost("admin/change-password")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AdminChangePassword([FromBody] AdminChangePasswordDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                Logger.Warn("Intento de cambio de contraseña por administrador con datos inválidos");
                return BadRequest(ModelState);
            }

            // Obtener información del usuario administrador del contexto
            var adminUser = "Administrador"; // En un sistema real, obtener del contexto de autenticación
            if (HttpContext.Items.TryGetValue("UserInfo", out var userInfoObj) && userInfoObj is UserInfo userInfo)
                adminUser = userInfo.PreferedUserName ?? userInfo.Name ?? "Administrador";

            var result = await service.AdminChangePasswordAsync(dto, adminUser);

            Logger.Info("Contraseña cambiada por administrador exitosamente para usuario ID: {UserId} por: {AdminUser}",
                dto.UserId, adminUser);
            return Ok(new { success = true, message = "Contraseña cambiada exitosamente" });
        }
        catch (InvalidOperationException ex)
        {
            Logger.Warn("Error en cambio de contraseña por administrador: {Message}", ex.Message);
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al cambiar contraseña por administrador para usuario ID: {UserId}", dto.UserId);
            return StatusCode(500, "Error interno del servidor");
        }
    }
}