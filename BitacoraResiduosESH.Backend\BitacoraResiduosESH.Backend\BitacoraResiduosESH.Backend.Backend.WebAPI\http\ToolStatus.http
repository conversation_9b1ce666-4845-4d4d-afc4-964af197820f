### Variables de entorno
@baseUrl = https://localhost:7001
@token = {{login.response.body.token}}

### Login para obtener token
# @name login
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}

###

### ========================================
### OPERACIONES DE LECTURA
### ========================================

### Obtener todos los estados de herramientas
GET {{baseUrl}}/api/toolstatus?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Obtener estados de herramientas paginados con filtros
GET {{baseUrl}}/api/toolstatus/paged?pageNumber=1&pageSize=10&name=&description=&active=true&includeDeleted=false
Authorization: Bearer {{token}}

###

### Obtener solo estados de herramientas activos
GET {{baseUrl}}/api/toolstatus/active?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Obtener estado de herramienta por ID
GET {{baseUrl}}/api/toolstatus/1
Authorization: Bearer {{token}}

###

### Buscar estados de herramientas por nombre
GET {{baseUrl}}/api/toolstatus/search/name?name=disponible&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Buscar estados de herramientas por descripción
GET {{baseUrl}}/api/toolstatus/search/description?description=uso&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Verificar si existe un estado de herramienta
GET {{baseUrl}}/api/toolstatus/1/exists
Authorization: Bearer {{token}}

###

### ========================================
### OPERACIONES DE ESCRITURA
### ========================================

### Crear nuevo estado de herramienta
POST {{baseUrl}}/api/toolstatus
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "En Reparación",
  "description": "Herramienta en proceso de reparación o mantenimiento correctivo"
}

###

### Crear otro estado de herramienta
POST {{baseUrl}}/api/toolstatus
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Reservada",
  "description": "Herramienta reservada para un proyecto específico"
}

###

### Crear estado de herramienta sin descripción
POST {{baseUrl}}/api/toolstatus
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "En Calibración"
}

###

### Actualizar estado de herramienta
PUT {{baseUrl}}/api/toolstatus/1
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Disponible Actualizado",
  "description": "Descripción actualizada para herramientas disponibles"
}

###

### ========================================
### OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN
### ========================================

### Desactivar estado de herramienta
PATCH {{baseUrl}}/api/toolstatus/1/deactivate
Authorization: Bearer {{token}}

###

### Activar estado de herramienta
PATCH {{baseUrl}}/api/toolstatus/1/activate
Authorization: Bearer {{token}}

###

### ========================================
### OPERACIONES DE ELIMINACIÓN
### ========================================

### Eliminar estado de herramienta (soft delete)
DELETE {{baseUrl}}/api/toolstatus/1
Authorization: Bearer {{token}}

###

### Eliminar estado de herramienta permanentemente
DELETE {{baseUrl}}/api/toolstatus/1/permanent
Authorization: Bearer {{token}}

###

### ========================================
### CASOS DE ERROR
### ========================================

### Intentar crear estado de herramienta sin nombre
POST {{baseUrl}}/api/toolstatus
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "description": "Solo descripción sin nombre"
}

###

### Intentar crear estado de herramienta con nombre duplicado
POST {{baseUrl}}/api/toolstatus
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Disponible",
  "description": "Intento de duplicado"
}

###

### Intentar actualizar estado de herramienta inexistente
PUT {{baseUrl}}/api/toolstatus/999
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Estado Inexistente",
  "description": "Este estado no existe"
}

###

### Intentar acceder sin token
GET {{baseUrl}}/api/toolstatus

###

### Intentar acceder con token inválido
GET {{baseUrl}}/api/toolstatus
Authorization: Bearer token_invalido 