﻿namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

/// <summary>
///     Configuración base para todas las entidades que heredan de SimpleEntity.
///     Proporciona la configuración estándar para entidades con Name y Description.
/// </summary>
/// <typeparam name="T">Tipo de entidad que hereda de SimpleEntity</typeparam>
public abstract class SimpleEntityConfiguration<T> : BaseEntityConfiguration<T> where T : SimpleEntity
{
    public override void Configure(EntityTypeBuilder<T> builder)
    {
        // Configuración base de BaseEntity
        base.Configure(builder);

        // Configuración específica de SimpleEntity
        ConfigureName(builder);
        ConfigureDescription(builder);
        ConfigureSimpleEntityIndexes(builder);
        ConfigureSimpleEntityConstraints(builder);
    }

    /// <summary>
    ///     Configura la propiedad Name
    /// </summary>
    protected virtual void ConfigureName(EntityTypeBuilder<T> builder)
    {
        builder.Property(e => e.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(200)")
            .IsRequired();
    }

    /// <summary>
    ///     Configura la propiedad Description
    /// </summary>
    protected virtual void ConfigureDescription(EntityTypeBuilder<T> builder)
    {
        builder.Property(e => e.Description)
            .HasColumnName("Description")
            .HasColumnType("nvarchar(1000)")
            .IsRequired(false);
    }

    /// <summary>
    ///     Configura índices específicos para SimpleEntity
    /// </summary>
    protected virtual void ConfigureSimpleEntityIndexes(EntityTypeBuilder<T> builder)
    {
        // Índice para Name (usado en búsquedas y ordenamiento)
        builder.HasIndex(e => e.Name)
            .HasDatabaseName($"IX_{TableName}_Name");

        // Índice compuesto para consultas comunes (Name + Active)
        builder.HasIndex(e => new { e.Name, e.Active })
            .HasDatabaseName($"IX_{TableName}_Name_Active");

        // Índice compuesto para consultas de auditoría (Name + Created)
        builder.HasIndex(e => new { e.Name, e.Created })
            .HasDatabaseName($"IX_{TableName}_Name_Created");
    }

    /// <summary>
    ///     Configura restricciones específicas para SimpleEntity
    /// </summary>
    protected virtual void ConfigureSimpleEntityConstraints(EntityTypeBuilder<T> builder)
    {
        // Restricción única: Name debe ser único entre elementos no eliminados
        builder.HasIndex(e => e.Name)
            .IsUnique()
            .HasFilter("[IsDeleted] = 0")
            .HasDatabaseName($"UQ_{TableName}_Name_NotDeleted");
    }
}