import React, {useEffect, useState} from 'react';
import {Area, AreaChart, CartesianGrid, Line, ResponsiveContainer, Tooltip, XAxis, YAxis} from 'recharts';

interface ProductionData {
    time: string;
    goodParts: number;
    scrap: number;
    efficiency: number;
}

const ProductionChart: React.FC = () => {
    const [data, setData] = useState<ProductionData[]>([]);
    const [currentTime, setCurrentTime] = useState(new Date());

    // Generar datos de muestra en tiempo real
    useEffect(() => {
        const generateData = () => {
            const now = new Date();
            const newData: ProductionData[] = [];

            // Generar datos para las últimas 24 horas (cada hora)
            for (let i = 23; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                const hour = time.getHours();

                // Simular producción con variaciones realistas
                const baseProduction = 100 + Math.sin(hour / 24 * Math.PI) * 20; // Variación diurna
                const goodParts = Math.floor(baseProduction + (Math.random() - 0.5) * 30);
                const scrap = Math.floor(goodParts * (0.05 + Math.random() * 0.1)); // 5-15% scrap
                const efficiency = Math.round(((goodParts - scrap) / goodParts) * 100);

                newData.push({
                    time: time.toLocaleTimeString('es-ES', {
                        hour: '2-digit',
                        minute: '2-digit'
                    }),
                    goodParts,
                    scrap,
                    efficiency
                });
            }

            setData(newData);
        };

        // Generar datos iniciales
        generateData();

        // Actualizar cada minuto para simular tiempo real
        const interval = setInterval(() => {
            setCurrentTime(new Date());

            // Agregar nuevo punto de datos
            setData(prevData => {
                const newData = [...prevData];

                // Remover el punto más antiguo
                newData.shift();

                // Agregar nuevo punto
                const hour = new Date().getHours();
                const baseProduction = 100 + Math.sin(hour / 24 * Math.PI) * 20;
                const goodParts = Math.floor(baseProduction + (Math.random() - 0.5) * 30);
                const scrap = Math.floor(goodParts * (0.05 + Math.random() * 0.1));
                const efficiency = Math.round(((goodParts - scrap) / goodParts) * 100);

                newData.push({
                    time: new Date().toLocaleTimeString('es-ES', {
                        hour: '2-digit',
                        minute: '2-digit'
                    }),
                    goodParts,
                    scrap,
                    efficiency
                });

                return newData;
            });
        }, 60000); // Actualizar cada minuto

        return () => clearInterval(interval);
    }, []);

    // Configurar tooltip personalizado
    const CustomTooltip = ({active, payload, label}: any) => {
        if (active && payload && payload.length) {
            return (
                <div
                    className="p-3 rounded-lg shadow-lg border"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)',
                        color: 'var(--text-primary)'
                    }}
                >
                    <p className="font-medium mb-2">{`Hora: ${label}`}</p>
                    <div className="space-y-1">
                        <p className="text-sm">
                            <span style={{color: '#10b981'}}>●</span> Partes Buenas: {payload[0].value}
                        </p>
                        <p className="text-sm">
                            <span style={{color: '#ef4444'}}>●</span> Scrap: {payload[1].value}
                        </p>
                        <p className="text-sm">
                            <span style={{color: '#3b82f6'}}>●</span> Eficiencia: {payload[2].value}%
                        </p>
                    </div>
                </div>
            );
        }
        return null;
    };

    // Configurar leyenda personalizada
    const CustomLegend = ({payload}: any) => {
        return (
            <div className="flex justify-center space-x-6 mt-4">
                {payload?.map((entry: any, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                        <div
                            className="w-3 h-3 rounded-full"
                            style={{backgroundColor: entry.color}}
                        />
                        <span
                            className="text-sm"
                            style={{color: 'var(--text-primary)'}}
                        >
              {entry.value}
            </span>
                    </div>
                ))}
            </div>
        );
    };

    return (
        <div
            className="p-6 rounded-lg shadow-sm border transition-all duration-200"
            style={{
                backgroundColor: 'var(--card-bg)',
                borderColor: 'var(--border-color)'
            }}
        >
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h2
                        className="text-xl font-semibold"
                        style={{color: 'var(--text-primary)'}}
                    >
                        Producción en Tiempo Real
                    </h2>
                    <p
                        className="text-sm"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Últimas 24 horas - Actualizado cada minuto
                    </p>
                </div>
                <div className="text-right">
                    <p
                        className="text-sm"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Última actualización:
                    </p>
                    <p
                        className="text-sm font-medium"
                        style={{color: 'var(--text-primary)'}}
                    >
                        {currentTime.toLocaleTimeString('es-ES')}
                    </p>
                </div>
            </div>

            <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={data} margin={{top: 5, right: 30, left: 20, bottom: 5}}>
                    <defs>
                        <linearGradient id="goodPartsGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10b981" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                        </linearGradient>
                        <linearGradient id="scrapGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3}/>
                            <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1}/>
                        </linearGradient>
                    </defs>

                    <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="var(--border-color)"
                        opacity={0.3}
                    />

                    <XAxis
                        dataKey="time"
                        stroke="var(--text-secondary)"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                    />

                    <YAxis
                        stroke="var(--text-secondary)"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        label={{
                            value: 'Cantidad',
                            angle: -90,
                            position: 'insideLeft',
                            style: {
                                textAnchor: 'middle',
                                fill: 'var(--text-secondary)',
                                fontSize: 12
                            }
                        }}
                    />

                    <Tooltip content={<CustomTooltip/>}/>

                    <Area
                        type="monotone"
                        dataKey="goodParts"
                        stroke="#10b981"
                        strokeWidth={2}
                        fill="url(#goodPartsGradient)"
                        name="Partes Buenas"
                    />

                    <Area
                        type="monotone"
                        dataKey="scrap"
                        stroke="#ef4444"
                        strokeWidth={2}
                        fill="url(#scrapGradient)"
                        name="Scrap"
                    />

                    <Line
                        type="monotone"
                        dataKey="efficiency"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        dot={{fill: '#3b82f6', strokeWidth: 2, r: 4}}
                        name="Eficiencia (%)"
                        yAxisId={1}
                    />
                </AreaChart>
            </ResponsiveContainer>

            <CustomLegend/>

            {/* Estadísticas rápidas */}
            <div className="grid grid-cols-3 gap-4 mt-6 pt-6 border-t" style={{borderColor: 'var(--border-color)'}}>
                <div className="text-center">
                    <p
                        className="text-2xl font-bold"
                        style={{color: '#10b981'}}
                    >
                        {data.length > 0 ? data[data.length - 1].goodParts : 0}
                    </p>
                    <p
                        className="text-xs"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Partes Buenas (actual)
                    </p>
                </div>
                <div className="text-center">
                    <p
                        className="text-2xl font-bold"
                        style={{color: '#ef4444'}}
                    >
                        {data.length > 0 ? data[data.length - 1].scrap : 0}
                    </p>
                    <p
                        className="text-xs"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Scrap (actual)
                    </p>
                </div>
                <div className="text-center">
                    <p
                        className="text-2xl font-bold"
                        style={{color: '#3b82f6'}}
                    >
                        {data.length > 0 ? data[data.length - 1].efficiency : 0}%
                    </p>
                    <p
                        className="text-xs"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        Eficiencia (actual)
                    </p>
                </div>
            </div>
        </div>
    );
};

export default ProductionChart; 