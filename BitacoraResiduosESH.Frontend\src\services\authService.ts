import type {AuthResponse, LoginRequest, RefreshTokenRequest, UserAuthInfo, ValidateTokenRequest} from '../types/auth';
import {API_CONFIG} from '../config/api';

class AuthService {
    private readonly baseUrl = `${API_CONFIG.BASE_URL}/auth`;

    /**
     * Inicia sesión con email/número de empleado y contraseña
     */
    async login(credentials: LoginRequest): Promise<AuthResponse> {
        return this.request<AuthResponse>('/login', {
            method: 'POST',
            body: JSON.stringify(credentials),
        });
    }

    /**
     * Refresca el token JWT
     */
    async refreshToken(token: string): Promise<AuthResponse> {
        const request: RefreshTokenRequest = {refreshToken: token};
        return this.request<AuthResponse>('/refresh', {
            method: 'POST',
            body: JSON.stringify(request),
        });
    }

    /**
     * Valida un token JWT
     */
    async validateToken(token: string): Promise<UserAuthInfo> {
        const request: ValidateTokenRequest = {token};
        return this.request<UserAuthInfo>('/validate', {
            method: 'POST',
            body: JSON.stringify(request),
        });
    }

    /**
     * Revoca un token (logout)
     */
    async revokeToken(token: string): Promise<void> {
        await this.request('/revoke', {
            method: 'POST',
            body: JSON.stringify({token}),
        });
    }

    /**
     * Obtiene información del usuario desde el token
     */
    async getUserInfo(): Promise<UserAuthInfo> {
        return this.request<UserAuthInfo>('/userinfo', {
            method: 'GET',
        });
    }

    /**
     * Realiza una petición HTTP
     */
    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> {
        const url = `${this.baseUrl}${endpoint}`;
        const config: RequestInit = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers,
            },
            ...options,
        };

        const response = await fetch(url, config);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }

        return response.json();
    }
}

export const authService = new AuthService(); 