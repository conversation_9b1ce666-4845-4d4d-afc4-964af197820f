import React from 'react';

export interface FormCheckboxProps {
    name: string;
    label: string;
    checked: boolean;
    onChange: (checked: boolean) => void;
    disabled?: boolean;
    required?: boolean;
    error?: string;
    touched?: boolean;
    className?: string;
    description?: string;
}

const FormCheckbox: React.FC<FormCheckboxProps> = ({
                                                       name,
                                                       label,
                                                       checked,
                                                       onChange,
                                                       disabled = false,
                                                       required = false,
                                                       error,
                                                       touched = false,
                                                       className = '',
                                                       description
                                                   }) => {
    const hasError = touched && error;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.checked);
    };

    return (
        <div className={`flex items-start ${className}`}>
            <div className="flex items-center h-5">
                <input
                    id={name}
                    name={name}
                    type="checkbox"
                    checked={checked}
                    onChange={handleChange}
                    disabled={disabled}
                    required={required}
                    className={`
            w-4 h-4 rounded border transition-colors duration-200
            focus:ring-2 focus:ring-offset-0
            ${hasError
                        ? 'border-red-300 text-red-600 focus:ring-red-500'
                        : 'border-gray-300 text-blue-600 focus:ring-blue-500'
                    }
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-500
          `}
                    style={{
                        backgroundColor: checked ? '#2563eb' : 'var(--card-bg)',
                        borderColor: hasError ? '#fca5a5' : 'var(--border-color)'
                    }}
                />
            </div>

            <div className="ml-3 text-sm">
                <label
                    htmlFor={name}
                    className={`
            font-medium cursor-pointer
            ${disabled ? 'cursor-not-allowed opacity-50' : ''}
            ${hasError ? 'text-red-600 dark:text-red-400' : 'text-gray-900 dark:text-gray-100'}
          `}
                    style={{color: hasError ? '#dc2626' : 'var(--text-primary)'}}
                >
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </label>

                {description && (
                    <p
                        className="mt-1 text-xs"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        {description}
                    </p>
                )}

                {hasError && (
                    <p className="mt-1 text-xs text-red-500 dark:text-red-400 flex items-center">
                        <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                  clipRule="evenodd"/>
                        </svg>
                        {error}
                    </p>
                )}
            </div>
        </div>
    );
};

export default FormCheckbox; 