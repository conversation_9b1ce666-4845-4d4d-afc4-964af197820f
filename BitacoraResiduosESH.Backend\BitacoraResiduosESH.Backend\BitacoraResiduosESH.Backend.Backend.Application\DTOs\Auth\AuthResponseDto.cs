namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth;

/// <summary>
///     DTO para la respuesta de autenticación exitosa
/// </summary>
public class AuthResponseDto
{
    /// <summary>
    ///     Token JWT
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    ///     Tipo de token
    /// </summary>
    public string TokenType { get; set; } = "Bearer";

    /// <summary>
    ///     Tiempo de expiración en minutos
    /// </summary>
    public int ExpiresIn { get; set; }

    /// <summary>
    ///     Información del usuario autenticado
    /// </summary>
    public UserAuthInfoDto User { get; set; } = new();

    /// <summary>
    ///     Mensaje de respuesta
    /// </summary>
    public string Message { get; set; } = "Autenticación exitosa";
}

/// <summary>
///     DTO con información básica del usuario para la respuesta de autenticación
/// </summary>
public class UserAuthInfoDto
{
    /// <summary>
    ///     ID del usuario
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     Nombre del usuario
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    ///     Email del usuario
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    ///     Número de empleado
    /// </summary>
    public string EmployeeNumber { get; set; } = string.Empty;

    /// <summary>
    ///     Rol del usuario
    /// </summary>
    public string Role { get; set; } = string.Empty;
}