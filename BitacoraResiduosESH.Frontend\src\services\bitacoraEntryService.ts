import type {
    BitacoraEntry,
    CreateBitacoraEntryDto,
    UpdateBitacoraEntryDto,
    BitacoraEntryFilterDto
} from '@/types/bitacoraEntry';
import type { PagedResponse } from '../config/api';
import { buildApiUrl, getAuthHeadersFromStore, handleApiError } from '../config/api';

/**
 * Service for BitacoraEntry entity operations
 * Handles all CRUD operations for BitacoraEntry entities
 */
class BitacoraEntryService {
    private readonly baseUrl = 'bitacoraentry';

    /**
     * Helper method to make API requests
     */
    private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
        const url = buildApiUrl(`${this.baseUrl}${endpoint}`);
        const headers = {
            'Content-Type': 'application/json',
            ...getAuthHeadersFromStore(),
            ...options.headers,
        };

        try {
            const response = await fetch(url, {
                ...options,
                headers,
            });

            if (!response.ok) {
                await handleApiError(response);
            }

            return await response.json();
        } catch (error) {
            throw error;
        }
    }

    /**
     * Get all BitacoraEntries with pagination
     */
    async getAll(pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`?pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }

    /**
     * Get active BitacoraEntries with pagination
     */
    async getActive(pageNumber = 1, pageSize = 10): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/active?pageNumber=${pageNumber}&pageSize=${pageSize}`);
    }

    /**
     * Get BitacoraEntry by ID
     */
    async getById(id: number, includeDeleted = false): Promise<BitacoraEntry> {
        return this.request<BitacoraEntry>(`/${id}?includeDeleted=${includeDeleted}`);
    }

    /**
     * Search BitacoraEntries with filters
     */
    async search(filters: BitacoraEntryFilterDto): Promise<PagedResponse<BitacoraEntry>> {
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                queryParams.append(key, value.toString());
            }
        });
        return this.request<PagedResponse<BitacoraEntry>>(`/search?${queryParams.toString()}`);
    }

    /**
     * Get paged BitacoraEntries with filters
     */
    async getPaged(filters: BitacoraEntryFilterDto): Promise<PagedResponse<BitacoraEntry>> {
        const queryParams = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
                queryParams.append(key, value.toString());
            }
        });
        return this.request<PagedResponse<BitacoraEntry>>(`/paged?${queryParams.toString()}`);
    }

    /**
     * Create a new BitacoraEntry
     */
    async create(data: CreateBitacoraEntryDto): Promise<BitacoraEntry> {
        return this.request<BitacoraEntry>('', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    /**
     * Update an existing BitacoraEntry
     */
    async update(id: number, data: UpdateBitacoraEntryDto): Promise<BitacoraEntry> {
        return this.request<BitacoraEntry>(`/${id}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    /**
     * Delete a BitacoraEntry (soft delete)
     */
    async delete(id: number): Promise<void> {
        await this.request<void>(`/${id}`, {
            method: 'DELETE',
        });
    }

    /**
     * Hard delete a BitacoraEntry
     */
    async hardDelete(id: number): Promise<void> {
        await this.request<void>(`/${id}/hard`, {
            method: 'DELETE',
        });
    }

    /**
     * Activate a BitacoraEntry
     */
    async activate(id: number): Promise<BitacoraEntry> {
        return this.request<BitacoraEntry>(`/${id}/activate`, {
            method: 'PATCH',
        });
    }

    /**
     * Deactivate a BitacoraEntry
     */
    async deactivate(id: number): Promise<BitacoraEntry> {
        return this.request<BitacoraEntry>(`/${id}/deactivate`, {
            method: 'PATCH',
        });
    }

    /**
     * Search BitacoraEntries by name
     */
    async searchByName(
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/search?name=${encodeURIComponent(name)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }

    /**
     * Search BitacoraEntries by description
     */
    async searchByDescription(
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/search?description=${encodeURIComponent(description)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }

    /**
     * Search BitacoraEntries by area
     */
    async searchByArea(
        areaId: number,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/search?areaId=${areaId}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }

    /**
     * Search BitacoraEntries by waste type
     */
    async searchByWasteType(
        wasteTypeId: number,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/search?wasteTypeId=${wasteTypeId}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }

    /**
     * Search BitacoraEntries by container type
     */
    async searchByContainerType(
        containerTypeId: number,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/search?containerTypeId=${containerTypeId}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }

    /**
     * Search BitacoraEntries by date range
     */
    async searchByDateRange(
        entryDateFrom?: string,
        entryDateTo?: string,
        departureDateFrom?: string,
        departureDateTo?: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        const params = new URLSearchParams();
        if (entryDateFrom) params.append('entryDateFrom', entryDateFrom);
        if (entryDateTo) params.append('entryDateTo', entryDateTo);
        if (departureDateFrom) params.append('departureDateFrom', departureDateFrom);
        if (departureDateTo) params.append('departureDateTo', departureDateTo);
        params.append('pageNumber', pageNumber.toString());
        params.append('pageSize', pageSize.toString());
        params.append('includeDeleted', includeDeleted.toString());

        return this.request<PagedResponse<BitacoraEntry>>(`/search?${params.toString()}`);
    }

    /**
     * Search BitacoraEntries by entered by user
     */
    async searchByEnteredBy(
        enteredBy: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<BitacoraEntry>> {
        return this.request<PagedResponse<BitacoraEntry>>(`/search?enteredBy=${encodeURIComponent(enteredBy)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`);
    }
}

// Export a singleton instance
export const bitacoraEntryService = new BitacoraEntryService();
