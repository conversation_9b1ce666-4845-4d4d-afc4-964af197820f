# Configuración de Logging con NLog

## Descripción

Este proyecto utiliza **NLog** como framework de logging principal. NLog es una biblioteca de logging muy potente y
flexible que permite configurar múltiples targets (destinos) y niveles de logging.

## Características

- **Múltiples targets**: Archivos, consola, JSON estructurado
- **Logging estructurado**: Soporte para JSON y logging semántico
- **Configuración flexible**: Diferentes configuraciones por entorno
- **Performance**: Logging asíncrono y optimizado
- **Integración completa**: Con Entity Framework y ASP.NET Core

## Configuración

### 1. Archivos de Configuración

- **nlog.config**: Configuración principal para producción
- **nlog.Development.config**: Configuración específica para desarrollo

### 2. Targets Configurados

#### Archivos de Log

- **allfile**: Todos los logs con información básica
- **ownFile-web**: Logs de la aplicación con detalles web
- **efFile**: Logs específicos de Entity Framework
- **errorFile**: Solo errores críticos
- **performanceFile**: Logs de rendimiento
- **debugFile**: Logs detallados de debugging (solo desarrollo)
- **jsonFile**: Logs estructurados en formato JSON

#### Consola

- **console**: Logs en consola para desarrollo
- **lifetimeConsole**: Logs del ciclo de vida de la aplicación

### 3. Niveles de Logging

- **Trace**: Información muy detallada
- **Debug**: Información de debugging
- **Info**: Información general
- **Warn**: Advertencias
- **Error**: Errores
- **Fatal**: Errores críticos

## Uso en el Código

### 1. Obtener Logger

```csharp
using NLog;

public class MyService
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger<MyService>();
    
    // O usar inyección de dependencias
    public MyService(ILogger<MyService> logger)
    {
        _logger = logger;
    }
}
```

### 2. Ejemplos de Logging

```csharp
// Logging básico
Logger.Info("Operación completada exitosamente");
Logger.Debug("Procesando elemento con ID: {Id}", elementId);
Logger.Warn("Elemento no encontrado: {Id}", elementId);
Logger.Error(ex, "Error al procesar elemento: {Id}", elementId);

// Logging estructurado
Logger.Info("Usuario {UserId} creó elemento {ElementId} en categoría {CategoryId}", 
    userId, elementId, categoryId);

// Logging de objetos complejos
Logger.Debug("DTO recibido: {@Dto}", createDto);
Logger.Debug("ModelState: {@ModelState}", ModelState);
```

### 3. Logging en Controladores

```csharp
[ApiController]
public class ExampleController : ControllerBase
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger<ExampleController>();

    [HttpGet("{id}")]
    public async Task<ActionResult<ExampleEntityDto>> GetById(int id)
    {
        Logger.Debug("Solicitud GET para elemento con ID: {Id}", id);
        
        try
        {
            var result = await _service.GetByIdAsync(id);
            
            if (result == null)
            {
                Logger.Warn("Elemento con ID {Id} no encontrado", id);
                return NotFound();
            }

            Logger.Debug("Elemento con ID {Id} recuperado exitosamente", id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener elemento con ID {Id}", id);
            return BadRequest();
        }
    }
}
```

### 4. Logging en Entity Framework

```csharp
public class AppDbContext : DbContext
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger<AppDbContext>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        Logger.Debug("Configurando modelo de Entity Framework");
        // ... configuración
        Logger.Debug("Modelo de Entity Framework configurado exitosamente");
    }

    public async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, string username, CancellationToken cancellationToken = default)
    {
        Logger.Debug("Iniciando SaveChangesAsync para usuario: {Username}", username);
        
        // ... lógica de auditoría
        
        var result = await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        Logger.Debug("SaveChangesAsync completado. {Count} entidades guardadas", result);
        
        return result;
    }
}
```

## Estructura de Archivos de Log

### Directorio de Logs

```
logs/
├── 2024-01-15/
│   ├── all-2024-01-15.log          # Todos los logs
│   ├── own-2024-01-15.log          # Logs de la aplicación
│   ├── ef-2024-01-15.log           # Logs de Entity Framework
│   ├── errors-2024-01-15.log       # Solo errores
│   ├── performance-2024-01-15.log  # Logs de rendimiento
│   ├── debug-2024-01-15.log        # Logs de debugging (desarrollo)
│   └── structured-2024-01-15.json  # Logs estructurados
└── internal-nlog.txt               # Logs internos de NLog
```

### Formato de Logs

#### Logs de Texto

```
2024-01-15 10:30:45.123|0|INFO|ExampleController|Elemento creado exitosamente con ID: 1 por usuario: admin
2024-01-15 10:30:46.456|0|DEBUG|AppDbContext|SaveChangesAsync completado. 1 entidades guardadas
```

#### Logs JSON

```json
{
  "timestamp": "2024-01-15 10:30:45.123",
  "level": "INFO",
  "logger": "ExampleController",
  "message": "Elemento creado exitosamente",
  "properties": {
    "EventId": "0",
    "RequestId": "12345678-1234-1234-1234-123456789012",
    "Url": "/api/example",
    "Method": "POST",
    "UserAgent": "PostmanRuntime/7.32.3",
    "IP": "127.0.0.1"
  }
}
```

## Configuración por Entorno

### Desarrollo

- **Nivel mínimo**: Trace
- **Targets**: Consola, archivos detallados, JSON
- **Logging de EF**: Detallado con consultas SQL
- **Archivos**: Separados por tipo y fecha

### Producción

- **Nivel mínimo**: Info
- **Targets**: Archivos principales, errores
- **Logging de EF**: Solo errores y warnings
- **Archivos**: Rotación automática

## Mejores Prácticas

### 1. Niveles de Logging

- **Trace**: Información muy detallada para debugging
- **Debug**: Información útil para desarrollo
- **Info**: Eventos importantes del negocio
- **Warn**: Situaciones inesperadas pero manejables
- **Error**: Errores que requieren atención
- **Fatal**: Errores críticos que pueden causar fallos

### 2. Mensajes de Log

```csharp
// ✅ Bueno
Logger.Info("Usuario {UserId} creó elemento {ElementId}", userId, elementId);
Logger.Error(ex, "Error al procesar pago para usuario {UserId}", userId);

// ❌ Malo
Logger.Info("Usuario creó elemento"); // Sin contexto
Logger.Error("Error"); // Sin detalles
```

### 3. Logging de Excepciones

```csharp
try
{
    // Código que puede fallar
}
catch (Exception ex)
{
    Logger.Error(ex, "Error al procesar solicitud para usuario {UserId}", userId);
    throw; // Re-lanzar la excepción
}
```

### 4. Logging de Performance

```csharp
var stopwatch = Stopwatch.StartNew();
try
{
    // Operación costosa
    var result = await _service.ProcessDataAsync();
    stopwatch.Stop();
    Logger.Info("Procesamiento completado en {Duration}ms", stopwatch.ElapsedMilliseconds);
    return result;
}
catch (Exception ex)
{
    stopwatch.Stop();
    Logger.Error(ex, "Error en procesamiento después de {Duration}ms", stopwatch.ElapsedMilliseconds);
    throw;
}
```

## Configuración Avanzada

### 1. Filtros Personalizados

```xml
<rules>
    <!-- Solo logs de errores en producción -->
    <logger name="*" minlevel="Error" writeTo="errorFile" />
    
    <!-- Logs de performance -->
    <logger name="*" minlevel="Info" writeTo="performanceFile" 
            when="${when:when=starts-with(logger,'Performance')}" />
</rules>
```

### 2. Layouts Personalizados

```xml
<target name="customFile" xsi:type="File" fileName="logs/custom.log"
        layout="${longdate}|${level:uppercase=true}|${logger}|${message}|${exception:format=tostring}" />
```

### 3. Rotación de Archivos

```xml
<target name="rotatingFile" xsi:type="File" 
        fileName="logs/app-${shortdate}.log"
        archiveFileName="logs/archive/app-{#}.log"
        archiveNumbering="Rolling"
        archiveEvery="Day"
        maxArchiveFiles="30" />
```

## Monitoreo y Análisis

### 1. Herramientas Recomendadas

- **Log Parser**: Para análisis de logs de texto
- **ELK Stack**: Para logs estructurados
- **Splunk**: Para análisis avanzado
- **Azure Application Insights**: Para monitoreo en la nube

### 2. Alertas

```csharp
// Logging de errores críticos
Logger.Fatal("Sistema de base de datos no disponible");
Logger.Error("Tasa de errores excede el 5%");
```

### 3. Métricas

```csharp
// Contadores de operaciones
Logger.Info("API Request Count: {Count}", requestCount);
Logger.Info("Average Response Time: {Time}ms", averageTime);
```

## Troubleshooting

### 1. Logs No Aparecen

- Verificar configuración de NLog
- Verificar permisos de escritura en directorio logs
- Verificar nivel de logging configurado

### 2. Performance Issues

- Usar logging asíncrono
- Configurar buffers apropiados
- Filtrar logs innecesarios

### 3. Archivos de Log Muy Grandes

- Configurar rotación automática
- Implementar compresión
- Establecer límites de tamaño

## Migración desde Logging Básico

### Antes (Logging Básico)

```csharp
_logger.LogError(ex, "Error al procesar elemento {Id}", id);
```

### Después (NLog)

```csharp
Logger.Error(ex, "Error al procesar elemento {Id}", id);
```

### Ventajas de NLog

- ✅ Configuración más flexible
- ✅ Múltiples targets
- ✅ Logging estructurado
- ✅ Mejor performance
- ✅ Filtros avanzados
- ✅ Rotación automática 