using BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role;

namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Authorize(Roles = "Admin")] // Solo administradores pueden acceder
public class RoleController : SimpleEntityController<Role, RoleDto, CreateRoleDto, UpdateRoleDto, RoleFilterDto>
{
    public RoleController(IRoleService service) : base(service)
    {
        // No se agregan métodos adicionales
        // Solo hereda todos los endpoints de SimpleEntityController:
        // - GET /api/item/{id} - Obtener por ID
        // - GET /api/item - Obtener todos
        // - GET /api/item/paged - Obtener paginados
        // - GET /api/item/active - Obtener activos
        // - GET /api/item/search/name - Buscar por nombre
        // - GET /api/item/search/description - Buscar por descripción
        // - POST /api/item - Crear
        // - PUT /api/item/{id} - Actualizar
        // - DELETE /api/item/{id} - Eliminar (soft delete)
        // - DELETE /api/item/{id}/permanent - Eliminar permanentemente
        // - PATCH /api/item/{id}/activate - Activar
        // - PATCH /api/item/{id}/deactivate - Desactivar
        // - GET /api/item/{id}/exists - Verificar existencia
        // - Manejo de errores automático
        // - Logging automático
        // - Validaciones automáticas
    }
}