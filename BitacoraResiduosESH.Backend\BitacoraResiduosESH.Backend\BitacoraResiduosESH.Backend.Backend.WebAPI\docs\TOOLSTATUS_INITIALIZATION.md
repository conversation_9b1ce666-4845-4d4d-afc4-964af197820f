# Inicialización de Estados de Herramientas

## Descripción

Este documento describe los estados de herramientas que se crean automáticamente al inicializar la base de datos por primera vez.

## Estados Predefinidos

### 1. Disponible
- **Nombre**: `Disponible`
- **Descripción**: `Herramienta disponible para uso`
- **Uso**: Herramientas que están listas para ser utilizadas
- **Color sugerido**: Verde

### 2. En Uso
- **Nombre**: `En Uso`
- **Descripción**: `Herramienta actualmente en uso`
- **Uso**: Herramientas que están siendo utilizadas en producción
- **Color sugerido**: Azul

### 3. Mantenimiento
- **Nombre**: `Mantenimiento`
- **Descripción**: `Herramienta en mantenimiento preventivo o correctivo`
- **Uso**: Herramientas en proceso de mantenimiento
- **Color sugerido**: Amarillo

### 4. Fuera de Servicio
- **Nombre**: `Fuera de Servicio`
- **Descripción**: `Herramienta temporalmente fuera de servicio`
- **Uso**: Herramientas temporalmente no disponibles
- **Color sugerido**: Naranja

### 5. Retirada
- **Nombre**: `Retirada`
- **Descripción**: `Herramienta retirada permanentemente del servicio`
- **Uso**: Herramientas que ya no se utilizan
- **Color sugerido**: Rojo

## Workflow de Estados

### Flujo Típico
```
Disponible → En Uso → Disponible
     ↓
Mantenimiento → Disponible
     ↓
Fuera de Servicio → Disponible
     ↓
Retirada (final)
```

### Transiciones Permitidas

1. **Disponible** puede cambiar a:
   - En Uso
   - Mantenimiento
   - Fuera de Servicio
   - Retirada

2. **En Uso** puede cambiar a:
   - Disponible
   - Mantenimiento
   - Fuera de Servicio

3. **Mantenimiento** puede cambiar a:
   - Disponible
   - Fuera de Servicio
   - Retirada

4. **Fuera de Servicio** puede cambiar a:
   - Disponible
   - Mantenimiento
   - Retirada

5. **Retirada** es un estado final (no puede cambiar)

## Casos de Uso

### Gestión de Inventario
- **Disponible**: Herramientas listas para asignar
- **En Uso**: Herramientas asignadas a operadores
- **Mantenimiento**: Herramientas en servicio técnico
- **Fuera de Servicio**: Herramientas con problemas temporales
- **Retirada**: Herramientas obsoletas o dañadas

### Reportes y Analytics
- **Disponibilidad**: Porcentaje de herramientas disponibles
- **Utilización**: Tiempo promedio en uso
- **Mantenimiento**: Frecuencia de mantenimientos
- **Retiradas**: Tasa de retiro de herramientas

### Notificaciones
- **Cambios de estado**: Notificar a responsables
- **Mantenimiento programado**: Recordatorios automáticos
- **Herramientas fuera de servicio**: Alertas inmediatas
- **Retiradas pendientes**: Notificaciones de limpieza

## Configuración

### Creación Automática
Los estados se crean automáticamente en:
- `ServiceCollectionExtensions.cs` - Método `InitializeDatabaseAsync`
- Solo si la base de datos está vacía
- Con usuario "system" como creador

### Personalización
Para agregar nuevos estados:
1. Modificar el método `InitializeDatabaseAsync`
2. Agregar nuevos elementos a la lista `toolStatuses`
3. Reiniciar la aplicación

### Ejemplo de Nuevo Estado
```csharp
new()
{
    Name = "En Calibración",
    Description = "Herramienta en proceso de calibración",
    CreatedBy = "system",
    Active = true
}
```

## Integración con Frontend

### Colores Sugeridos
- **Disponible**: `#10B981` (Verde)
- **En Uso**: `#3B82F6` (Azul)
- **Mantenimiento**: `#F59E0B` (Amarillo)
- **Fuera de Servicio**: `#F97316` (Naranja)
- **Retirada**: `#EF4444` (Rojo)

### Badges en UI
```tsx
const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case 'disponible': return 'bg-green-100 text-green-800';
        case 'en uso': return 'bg-blue-100 text-blue-800';
        case 'mantenimiento': return 'bg-yellow-100 text-yellow-800';
        case 'fuera de servicio': return 'bg-orange-100 text-orange-800';
        case 'retirada': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
};
```

## Próximos Pasos

### Mejoras Planificadas
1. **Estados personalizados**: Permitir crear estados específicos
2. **Workflow configurable**: Definir transiciones permitidas
3. **Notificaciones automáticas**: Alertas por cambios de estado
4. **Historial de cambios**: Tracking de transiciones
5. **Estados temporales**: Estados con duración limitada

### Integración con Herramientas
1. **Entidad Tool**: Relación con estados
2. **Historial de estados**: Tracking de cambios
3. **Reportes**: Analytics de utilización
4. **Dashboard**: Vista general de estados 