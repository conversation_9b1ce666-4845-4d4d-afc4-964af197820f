### Variables de entorno
@baseUrl = https://localhost:7001
@contentType = application/json

### ========================================
### OPERACIONES BÁSICAS CRUD (HEREDADAS DE SIMPLEENTITY)
### ========================================

### 1. Crear un producto
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Laptop HP Pavilion",
  "description": "Laptop HP Pavilion 15.6 pulgadas con procesador Intel i5",
  "price": 899.99,
  "sku": "HP-PAV-001",
  "stock": 25
}

### 2. Crear otro producto
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Mouse Inalámbrico Logitech",
  "description": "Mouse inalámbrico Logitech M185 con sensor óptico",
  "price": 19.99,
  "sku": "LOG-M185-001",
  "stock": 50
}

### 3. Obtener todos los productos
GET {{baseUrl}}/api/product

### 4. Obtener producto por ID
GET {{baseUrl}}/api/product/1

### 5. Obtener producto por ID (incluyendo eliminados)
GET {{baseUrl}}/api/product/1?includeDeleted=true

### 6. Actualizar producto
PUT {{baseUrl}}/api/product/1
Content-Type: {{contentType}}

{
  "name": "Laptop HP Pavilion Pro",
  "description": "Laptop HP Pavilion 15.6 pulgadas con procesador Intel i5 - Versión Pro",
  "price": 999.99,
  "sku": "HP-PAV-PRO-001",
  "stock": 20
}

### 7. Eliminar producto (soft delete)
DELETE {{baseUrl}}/api/product/1

### 8. Eliminar producto permanentemente
DELETE {{baseUrl}}/api/product/1/permanent

### ========================================
### OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN (HEREDADAS)
### ========================================

### 9. Desactivar producto
PATCH {{baseUrl}}/api/product/1/deactivate

### 10. Activar producto
PATCH {{baseUrl}}/api/product/1/activate

### ========================================
### OPERACIONES DE BÚSQUEDA (HEREDADAS)
### ========================================

### 11. Buscar productos por nombre
GET {{baseUrl}}/api/product/search/name?name=laptop

### 12. Buscar productos por descripción
GET {{baseUrl}}/api/product/search/description?description=inalámbrico

### 13. Obtener productos activos
GET {{baseUrl}}/api/product/active

### 14. Obtener productos paginados
GET {{baseUrl}}/api/product/paged?pageNumber=1&pageSize=10

### 15. Obtener productos paginados con filtros
GET {{baseUrl}}/api/product/paged?pageNumber=1&pageSize=10&name=laptop&active=true

### ========================================
### OPERACIONES ESPECÍFICAS DE PRODUCT
### ========================================

### 16. Obtener productos por rango de precio
GET {{baseUrl}}/api/product/by-price-range?minPrice=10&maxPrice=100

### 17. Buscar productos por SKU
GET {{baseUrl}}/api/product/by-sku?sku=HP

### 18. Obtener productos en stock
GET {{baseUrl}}/api/product/in-stock

### 19. Obtener productos sin stock
GET {{baseUrl}}/api/product/out-of-stock

### 20. Obtener productos por rango de stock
GET {{baseUrl}}/api/product/by-stock-range?minStock=10&maxStock=50

### ========================================
### OPERACIONES DE VALIDACIÓN (HEREDADAS)
### ========================================

### 21. Verificar si existe un producto
GET {{baseUrl}}/api/product/1/exists

### ========================================
### EJEMPLOS DE DATOS PARA PRUEBAS
### ========================================

### Crear productos de ejemplo para pruebas
### Producto 1: Laptop
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Laptop Dell Inspiron",
  "description": "Laptop Dell Inspiron 14 pulgadas con procesador AMD Ryzen 5",
  "price": 749.99,
  "sku": "DELL-INS-001",
  "stock": 15
}

### Producto 2: Monitor
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Monitor Samsung 24\"",
  "description": "Monitor Samsung 24 pulgadas Full HD LED",
  "price": 199.99,
  "sku": "SAMS-24-001",
  "stock": 30
}

### Producto 3: Teclado
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Teclado Mecánico Corsair",
  "description": "Teclado mecánico Corsair K70 con switches Cherry MX Red",
  "price": 149.99,
  "sku": "COR-K70-001",
  "stock": 8
}

### Producto 4: Auriculares
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Auriculares Sony WH-1000XM4",
  "description": "Auriculares inalámbricos Sony con cancelación de ruido",
  "price": 349.99,
  "sku": "SONY-WH4-001",
  "stock": 0
}

### Producto 5: SSD
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "SSD Samsung 970 EVO 1TB",
  "description": "SSD Samsung 970 EVO NVMe M.2 de 1TB",
  "price": 129.99,
  "sku": "SAMS-970-1TB",
  "stock": 45
}

### ========================================
### PRUEBAS DE FILTROS AVANZADOS
### ========================================

### Filtrar productos por precio alto
GET {{baseUrl}}/api/product/by-price-range?minPrice=300&maxPrice=1000

### Filtrar productos por precio bajo
GET {{baseUrl}}/api/product/by-price-range?minPrice=0&maxPrice=50

### Buscar productos por SKU específico
GET {{baseUrl}}/api/product/by-sku?sku=SAMS

### Obtener productos con stock bajo
GET {{baseUrl}}/api/product/by-stock-range?minStock=0&maxStock=10

### Obtener productos con stock alto
GET {{baseUrl}}/api/product/by-stock-range?minStock=20&maxStock=100

### Filtrar por múltiples criterios usando paginación
GET {{baseUrl}}/api/product/paged?pageNumber=1&pageSize=10&name=laptop&active=true&minPrice=500&maxPrice=1000

### ========================================
### PRUEBAS DE CASOS ESPECIALES
### ========================================

### Crear producto sin SKU
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Producto Sin SKU",
  "description": "Producto de prueba sin SKU asignado",
  "price": 29.99,
  "stock": 5
}

### Crear producto con stock cero
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Producto Agotado",
  "description": "Producto de prueba con stock cero",
  "price": 99.99,
  "sku": "AGOT-001",
  "stock": 0
}

### Crear producto con precio cero
POST {{baseUrl}}/api/product
Content-Type: {{contentType}}

{
  "name": "Producto Gratuito",
  "description": "Producto de prueba con precio cero",
  "price": 0.00,
  "sku": "FREE-001",
  "stock": 100
} 