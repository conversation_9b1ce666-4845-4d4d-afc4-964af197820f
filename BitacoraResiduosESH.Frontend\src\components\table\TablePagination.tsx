import React from 'react';
import {ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight} from 'lucide-react';

export interface TablePaginationProps {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    pageSize: number;
    onPageChange: (page: number) => void;
    onPageSizeChange?: (pageSize: number) => void;
    pageSizeOptions?: number[];
    showPageSizeSelector?: boolean;
}

const TablePagination: React.FC<TablePaginationProps> = ({
                                                             currentPage,
                                                             totalPages,
                                                             totalItems,
                                                             pageSize,
                                                             onPageChange,
                                                             onPageSizeChange,
                                                             pageSizeOptions = [10, 25, 50, 100],
                                                             showPageSizeSelector = true,
                                                         }) => {
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);

    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;

        if (totalPages <= maxVisiblePages) {
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            if (currentPage <= 3) {
                for (let i = 1; i <= 4; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            } else if (currentPage >= totalPages - 2) {
                pages.push(1);
                pages.push('...');
                for (let i = totalPages - 3; i <= totalPages; i++) {
                    pages.push(i);
                }
            } else {
                pages.push(1);
                pages.push('...');
                for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                    pages.push(i);
                }
                pages.push('...');
                pages.push(totalPages);
            }
        }

        return pages;
    };

    const handlePageClick = (page: number | string) => {
        if (typeof page === 'number') {
            onPageChange(page);
        }
    };

    return (
        <div className="flex items-center justify-between px-4 py-3 border-t"
             style={{borderColor: 'var(--border-color)'}}>
            {/* Información de resultados */}
            <div className="flex items-center space-x-4">
        <span
            className="text-sm"
            style={{color: 'var(--text-secondary)'}}
        >
          Mostrando {startItem} a {endItem} de {totalItems} resultados
        </span>

                {/* Selector de tamaño de página */}
                {showPageSizeSelector && onPageSizeChange && (
                    <div className="flex items-center space-x-2">
            <span
                className="text-sm"
                style={{color: 'var(--text-secondary)'}}
            >
              Por página:
            </span>
                        <select
                            value={pageSize}
                            onChange={(e) => onPageSizeChange(Number(e.target.value))}
                            className="border rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            style={{
                                backgroundColor: 'var(--card-bg)',
                                borderColor: 'var(--border-color)',
                                color: 'var(--text-primary)',
                            }}
                        >
                            {pageSizeOptions.map((size) => (
                                <option key={size} value={size}>
                                    {size}
                                </option>
                            ))}
                        </select>
                    </div>
                )}
            </div>

            {/* Navegación de páginas */}
            <div className="flex items-center space-x-1">
                {/* Botón primera página */}
                <button
                    onClick={() => onPageChange(1)}
                    disabled={currentPage === 1}
                    className="p-2 rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700"
                    style={{color: 'var(--text-secondary)'}}
                >
                    <ChevronsLeft className="h-4 w-4"/>
                </button>

                {/* Botón página anterior */}
                <button
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="p-2 rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700"
                    style={{color: 'var(--text-secondary)'}}
                >
                    <ChevronLeft className="h-4 w-4"/>
                </button>

                {/* Números de página */}
                {getPageNumbers().map((page, index) => (
                    <button
                        key={index}
                        onClick={() => handlePageClick(page)}
                        disabled={page === '...'}
                        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                            page === currentPage
                                ? 'bg-blue-600 text-white'
                                : page === '...'
                                    ? 'cursor-default'
                                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                        }`}
                        style={{
                            color: page === currentPage ? '#ffffff' : 'var(--text-primary)',
                        }}
                    >
                        {page}
                    </button>
                ))}

                {/* Botón página siguiente */}
                <button
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700"
                    style={{color: 'var(--text-secondary)'}}
                >
                    <ChevronRight className="h-4 w-4"/>
                </button>

                {/* Botón última página */}
                <button
                    onClick={() => onPageChange(totalPages)}
                    disabled={currentPage === totalPages}
                    className="p-2 rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-700"
                    style={{color: 'var(--text-secondary)'}}
                >
                    <ChevronsRight className="h-4 w-4"/>
                </button>
            </div>
        </div>
    );
};

export default TablePagination; 