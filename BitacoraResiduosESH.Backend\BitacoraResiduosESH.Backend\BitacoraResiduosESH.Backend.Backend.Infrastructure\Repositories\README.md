# Repositorio Genérico - Documentación

## Descripción

El `GenericRepository<T>` es una implementación completa de un repositorio genérico que proporciona todas las
operaciones CRUD básicas para entidades que heredan de `BaseEntity`. Está diseñado para seguir los principios de Clean
Architecture y proporcionar una base sólida para todos los repositorios de la aplicación.

## Características

### Operaciones de Lectura

- `GetByIdAsync(int id, bool includeDeleted = false)` - Obtiene una entidad por ID
- `GetAllAsync(bool includeDeleted = false)` - Obtiene todas las entidades
- `GetPagedAsync(PaginationFilter filter)` - Obtiene entidades paginadas
- `GetActiveAsync()` - Obtiene solo entidades activas
- `ExistsAsync(int id, bool includeDeleted = false)` - Verifica si existe una entidad
- `CountAsync(bool includeDeleted = false)` - Cuenta el total de entidades

### Operaciones de Escritura

- `AddAsync(T entity)` - Agrega una nueva entidad
- `AddRangeAsync(IEnumerable<T> entities)` - Agrega múltiples entidades
- `UpdateAsync(T entity)` - Actualiza una entidad
- `UpdateRangeAsync(IEnumerable<T> entities)` - Actualiza múltiples entidades

### Operaciones de Eliminación

- `DeleteAsync(int id, string deletedBy)` - Eliminación lógica por ID
- `DeleteAsync(T entity, string deletedBy)` - Eliminación lógica de entidad
- `DeleteRangeAsync(IEnumerable<int> ids, string deletedBy)` - Eliminación lógica múltiple por IDs
- `DeleteRangeAsync(IEnumerable<T> entities, string deletedBy)` - Eliminación lógica múltiple
- `HardDeleteAsync(int id)` - Eliminación física por ID
- `HardDeleteAsync(T entity)` - Eliminación física de entidad
- `HardDeleteRangeAsync(IEnumerable<int> ids)` - Eliminación física múltiple por IDs
- `HardDeleteRangeAsync(IEnumerable<T> entities)` - Eliminación física múltiple

### Operaciones de Activación/Desactivación

- `ActivateAsync(int id, string updatedBy)` - Activa una entidad por ID
- `DeactivateAsync(int id, string updatedBy)` - Desactiva una entidad por ID
- `ActivateAsync(T entity, string updatedBy)` - Activa una entidad
- `DeactivateAsync(T entity, string updatedBy)` - Desactiva una entidad

### Operaciones de Búsqueda

- `FindAsync(Func<T, bool> predicate, bool includeDeleted = false)` - Busca entidades con predicado
- `FirstOrDefaultAsync(Func<T, bool> predicate, bool includeDeleted = false)` - Obtiene la primera entidad que coincida
- `FirstAsync(Func<T, bool> predicate, bool includeDeleted = false)` - Obtiene la primera entidad (lanza excepción si no
  encuentra)

## Uso Básico

### 1. Crear un Repositorio Específico

```csharp
// Interfaz
public interface IExampleRepository : IGenericRepository<ExampleEntity>
{
    // Métodos específicos adicionales
    Task<IEnumerable<ExampleEntity>> GetByNameAsync(string name);
}

// Implementación
public class ExampleRepository : GenericRepository<ExampleEntity>, IExampleRepository
{
    public ExampleRepository(AppDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<ExampleEntity>> GetByNameAsync(string name)
    {
        return await _dbSet
            .Where(e => !e.IsDeleted && e.Name.Contains(name))
            .ToListAsync();
    }
}
```

### 2. Registrar en el Contenedor de Dependencias

```csharp
// En Program.cs o Startup.cs
services.AddScoped<IExampleRepository, ExampleRepository>();
```

### 3. Usar en un Servicio o Controlador

```csharp
public class ExampleService
{
    private readonly IExampleRepository _repository;

    public ExampleService(IExampleRepository repository)
    {
        _repository = repository;
    }

    public async Task<ExampleEntity> CreateExampleAsync(string name, string description)
    {
        var entity = new ExampleEntity
        {
            Name = name,
            Description = description,
            CreatedBy = "current-user"
        };

        return await _repository.AddAsync(entity);
    }

    public async Task<IEnumerable<ExampleEntity>> GetActiveExamplesAsync()
    {
        return await _repository.GetActiveAsync();
    }

    public async Task<bool> DeleteExampleAsync(int id, string deletedBy)
    {
        return await _repository.DeleteAsync(id, deletedBy);
    }
}
```

## Características del BaseEntity

Todas las entidades que heredan de `BaseEntity` automáticamente obtienen:

- `Id` - Identificador único
- `Created` - Fecha de creación
- `CreatedBy` - Usuario que creó la entidad
- `IsDeleted` - Indica si está eliminado lógicamente
- `Deleted` - Fecha de eliminación lógica
- `DeletedBy` - Usuario que eliminó la entidad
- `Updated` - Fecha de última actualización
- `UpdatedBy` - Usuario que actualizó la entidad
- `Active` - Indica si está activo

## Consideraciones

1. **Soft Delete**: Por defecto, las operaciones de eliminación son lógicas (soft delete). Use `HardDeleteAsync` para
   eliminación física.

2. **Auditoría**: Todas las operaciones de escritura actualizan automáticamente los campos de auditoría.

3. **Filtros**: Los métodos incluyen opciones para incluir o excluir entidades eliminadas lógicamente.

4. **Transacciones**: El repositorio maneja las transacciones automáticamente con `SaveChangesAsync()`.

5. **Rendimiento**: Use `GetPagedAsync` para grandes conjuntos de datos en lugar de `GetAllAsync`.

## Patrón de Uso Recomendado

1. Siempre herede de `IGenericRepository<T>` para interfaces específicas
2. Siempre herede de `GenericRepository<T>` para implementaciones específicas
3. Agregue métodos específicos solo cuando sea necesario
4. Use el patrón de inyección de dependencias para registrar repositorios
5. Mantenga la lógica de negocio en servicios, no en repositorios

# Repositorios - Guía de Uso

## Descripción

Este directorio contiene los repositorios que implementan el patrón Repository para acceder a los datos. Los
repositorios utilizan Entity Framework Core y están configurados con logging detallado usando NLog.

## Estructura

- **GenericRepository.cs**: Repositorio genérico con operaciones CRUD básicas
- **ExampleRepository.cs**: Repositorio específico para ExampleEntity con métodos de búsqueda personalizados

## Características

- ✅ **Logging completo** con NLog
- ✅ **Auditoría automática** (Created, Updated, Deleted, etc.)
- ✅ **Soft Delete** y **Hard Delete**
- ✅ **Activación/Desactivación** de entidades
- ✅ **Paginación** integrada
- ✅ **Filtros** por entidades eliminadas
- ✅ **Búsquedas personalizadas** por entidad

## Uso de Comparaciones de Strings

### ❌ Problemas Comunes

**Error frecuente con Entity Framework:**

```csharp
// ❌ NO FUNCIONA - Entity Framework no puede traducir esto a SQL
.Where(e => e.Name.Equals(name, StringComparison.OrdinalIgnoreCase))

// ❌ NO FUNCIONA - Entity Framework no puede traducir esto a SQL
.Where(e => string.Equals(e.Name, name, StringComparison.OrdinalIgnoreCase))
```

### ✅ Soluciones Correctas

#### 1. Comparación Exacta (Case-Sensitive)

```csharp
// ✅ FUNCIONA - Comparación exacta
.Where(e => e.Name == name)

// ✅ FUNCIONA - Usando EF.Functions
.Where(e => EF.Functions.Like(e.Name, name))
```

#### 2. Comparación Exacta (Case-Insensitive)

```csharp
// ✅ FUNCIONA - Usando EF.Functions para case-insensitive
.Where(e => EF.Functions.Like(e.Name.ToLower(), name.ToLower()))

// ✅ FUNCIONA - Usando EF.Functions.Collate (SQL Server)
.Where(e => EF.Functions.Collate(e.Name, "SQL_Latin1_General_CP1_CI_AS") == name)
```

#### 3. Búsqueda por Contenido (Contains)

```csharp
// ✅ FUNCIONA - Búsqueda por contenido
.Where(e => e.Name.Contains(name))

// ✅ FUNCIONA - Búsqueda case-insensitive
.Where(e => e.Name.ToLower().Contains(name.ToLower()))
```

#### 4. Búsqueda por Inicio (StartsWith)

```csharp
// ✅ FUNCIONA - Búsqueda por inicio
.Where(e => e.Name.StartsWith(name))

// ✅ FUNCIONA - Búsqueda case-insensitive
.Where(e => e.Name.ToLower().StartsWith(name.ToLower()))
```

#### 5. Búsqueda por Fin (EndsWith)

```csharp
// ✅ FUNCIONA - Búsqueda por fin
.Where(e => e.Name.EndsWith(name))

// ✅ FUNCIONA - Búsqueda case-insensitive
.Where(e => e.Name.ToLower().EndsWith(name.ToLower()))
```

## Ejemplos de Implementación

### GenericRepository

```csharp
public class GenericRepository<T> : IGenericRepository<T>
    where T : BaseEntity
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger<GenericRepository<T>>();

    // Búsqueda con predicado
    public async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        Logger.Debug("Buscando entidades {EntityType} con predicado", typeof(T).Name);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var result = await query.Where(predicate).ToListAsync();

        Logger.Debug("Búsqueda completada: {Count} resultados", result.Count);

        return result;
    }
}
```

### ExampleRepository

```csharp
public class ExampleRepository : GenericRepository<ExampleEntity>, IExampleRepository
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger<ExampleRepository>();

    // Búsqueda por nombre (case-insensitive)
    public async Task<IEnumerable<ExampleEntity>> GetByNameAsync(string name, bool includeDeleted = false)
    {
        Logger.Debug("Buscando por nombre: {Name}", name);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var result = await query
            .Where(e => e.Name.ToLower().Contains(name.ToLower()))
            .ToListAsync();

        Logger.Debug("Búsqueda completada: {Count} resultados", result.Count);

        return result;
    }

    // Búsqueda exacta por nombre
    public async Task<ExampleEntity?> GetByNameExactAsync(string name, bool includeDeleted = false)
    {
        Logger.Debug("Buscando nombre exacto: {Name}", name);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var result = await query
            .FirstOrDefaultAsync(e => e.Name.ToLower() == name.ToLower());

        Logger.Debug("Búsqueda exacta completada: {Found}", result != null);

        return result;
    }
}
```

## Mejores Prácticas

### 1. Logging

```csharp
// ✅ Siempre loggear operaciones importantes
Logger.Debug("Iniciando búsqueda por {Parameter}", parameter);
Logger.Info("Operación completada exitosamente");
Logger.Warn("Elemento no encontrado: {Id}", id);
Logger.Error(ex, "Error en operación: {Message}", ex.Message);
```

### 2. Manejo de Nulos

```csharp
// ✅ Verificar nulos antes de usar
.Where(e => e.Description != null && e.Description.Contains(description))

// ✅ Usar operador null-coalescing
var userName = entity.CreatedBy ?? "system";
```

### 3. Performance

```csharp
// ✅ Usar AsQueryable() para construir queries dinámicas
var query = DbSet.AsQueryable();

if (!includeDeleted)
    query = query.Where(e => !e.IsDeleted);

if (!string.IsNullOrEmpty(name))
    query = query.Where(e => e.Name.Contains(name));

return await query.ToListAsync();
```

### 4. Auditoría

```csharp
// ✅ Usar el método SaveChangesAsync con usuario
await context.SaveChangesAsync(true, username);

// ✅ Establecer campos de auditoría
entity.Created = DateTime.Now;
entity.CreatedBy = username;
entity.Active = true;
entity.IsDeleted = false;
```

## Troubleshooting

### Error: "Translation of the 'string.Equals' overload with a 'StringComparison' parameter is not supported"

**Causa:** Entity Framework no puede traducir `string.Equals` con `StringComparison` a SQL.

**Solución:**

```csharp
// ❌ NO USAR
.Where(e => e.Name.Equals(name, StringComparison.OrdinalIgnoreCase))

// ✅ USAR EN SU LUGAR
.Where(e => e.Name.ToLower() == name.ToLower())
.Where(e => e.Name.ToLower().Contains(name.ToLower()))
```

### Error: "The LINQ expression could not be translated"

**Causa:** Entity Framework no puede traducir la expresión LINQ a SQL.

**Soluciones:**

1. **Usar métodos compatibles con EF:**

   ```csharp
   // ✅ Compatible
   .Where(e => e.Name.Contains(name))
   .Where(e => e.Name.StartsWith(name))
   .Where(e => e.Name.EndsWith(name))
   ```

2. **Usar EF.Functions:**

   ```csharp
   // ✅ Compatible
   .Where(e => EF.Functions.Like(e.Name, $"%{name}%"))
   .Where(e => EF.Functions.Like(e.Name.ToLower(), $"%{name.ToLower()}%"))
   ```

3. **Evaluación en cliente (último recurso):**
   ```csharp
   // ⚠️ Solo usar si es absolutamente necesario
   var result = await query.ToListAsync();
   return result.Where(e => e.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
   ```

### Error: "Duplicate filter conditions"

**Causa:** Aplicar el mismo filtro múltiples veces.

**Solución:**

```csharp
// ❌ EVITAR - Filtro duplicado
var query = DbSet.AsQueryable();
query = query.Where(e => !e.IsDeleted); // Primera vez
query = query.Where(e => !e.IsDeleted); // Segunda vez - DUPLICADO

// ✅ CORRECTO - Filtro una sola vez
var query = DbSet.AsQueryable();
if (!includeDeleted)
    query = query.Where(e => !e.IsDeleted);
```

## Configuración de Logging

Los repositorios están configurados para usar NLog con los siguientes niveles:

- **Debug**: Operaciones de búsqueda y filtrado
- **Info**: Operaciones exitosas de escritura
- **Warn**: Elementos no encontrados
- **Error**: Excepciones y errores

Los logs se escriben en:

- `logs/own-{fecha}.log` - Logs de la aplicación
- `logs/ef-{fecha}.log` - Logs de Entity Framework
- `logs/structured-{fecha}.json` - Logs estructurados

# Repositorios de Infraestructura

Este directorio contiene todos los repositorios que implementan el acceso a datos para las entidades del sistema.

## Repositorios Disponibles

### GenericRepository<T> (Repositorio Base)

- **Archivo**: `GenericRepository.cs`
- **Propósito**: Clase base para repositorios que manejan operaciones CRUD básicas
- **Funcionalidades**:
  - Operaciones CRUD estándar
  - Consultas con filtros básicos
  - Soporte para soft delete
  - Paginación y ordenamiento

### SimpleEntityRepository<T> (Repositorio para Entidades Simples)

- **Archivo**: `SimpleEntityRepository.cs`
- **Hereda de**: `GenericRepository<T>`
- **Propósito**: Repositorio especializado para entidades que heredan de `SimpleEntity`
- **Funcionalidades adicionales**:
  - Búsquedas por nombre y descripción
  - Filtros por estado activo
  - Ordenamiento por nombre por defecto

### RoleRepository

- **Archivo**: `RoleRepository.cs`
- **Entidad**: `Role`
- **Hereda de**: `SimpleEntityRepository<Role>`
- **Funcionalidades**: Operaciones CRUD básicas sin lógica adicional

### ToolTypeRepository

- **Archivo**: `ToolTypeRepository.cs`
- **Entidad**: `ToolType`
- **Hereda de**: `SimpleEntityRepository<ToolType>`
- **Funcionalidades**: Operaciones CRUD básicas sin lógica adicional

### ToolStatusRepository

- **Archivo**: `ToolStatusRepository.cs`
- **Entidad**: `ToolStatus`
- **Hereda de**: `SimpleEntityRepository<ToolStatus>`
- **Funcionalidades**: Operaciones CRUD básicas sin lógica adicional

### LocationTypeRepository

- **Archivo**: `LocationTypeRepository.cs`
- **Entidad**: `LocationType`
- **Hereda de**: `SimpleEntityRepository<LocationType>`
- **Funcionalidades**: Operaciones CRUD básicas sin lógica adicional

### LocationRepository

- **Archivo**: `LocationRepository.cs`
- **Entidad**: `Location`
- **Hereda de**: `SimpleEntityRepository<Location>`
- **Funcionalidades**:
  - Operaciones CRUD con inclusión automática de `LocationType`
  - Sobrescritura de métodos para incluir relación con `LocationType`
  - Búsqueda específica por tipo de ubicación: `GetByLocationTypeIdAsync`
  - Todas las consultas incluyen automáticamente la navegación a `LocationType`

### UserRepository

- **Archivo**: `UserRepository.cs`
- **Entidad**: `User`
- **Hereda de**: `GenericRepository<User>`
- **Funcionalidades**:
  - Operaciones CRUD con inclusión automática de `Role`
  - Búsquedas específicas por número de empleado
  - Validaciones de unicidad de email y número de empleado
  - Todas las consultas incluyen automáticamente la navegación a `Role`

## Patrón de Implementación

### Repositorios Simples

Para entidades que no requieren lógica adicional:

```csharp
public class ExampleRepository : SimpleEntityRepository<Example>, IExampleRepository
{
    public ExampleRepository(AppDbContext context) : base(context)
    {
        // No se agrega lógica adicional
    }
}
```

### Repositorios con Relaciones

Para entidades que tienen navegación a otras entidades:

```csharp
public class LocationRepository : SimpleEntityRepository<Location>, ILocationRepository
{
    public LocationRepository(AppDbContext context) : base(context)
    {
    }

    // Sobrescribir métodos para incluir navegaciones
    public override async Task<Location?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        var query = DbSet.Include(e => e.LocationType).AsQueryable();
        // ... resto de la implementación
    }

    // Métodos específicos
    public async Task<IEnumerable<Location>> GetByLocationTypeIdAsync(int locationTypeId, bool includeDeleted = false)
    {
        // Implementación específica
    }
}
```

## Funcionalidades Comunes

### Operaciones CRUD Básicas

- `CreateAsync(T entity)`: Crear nueva entidad
- `GetByIdAsync(int id, bool includeDeleted = false)`: Obtener por ID
- `GetAllAsync(bool includeDeleted = false)`: Obtener todas
- `UpdateAsync(T entity)`: Actualizar entidad existente
- `DeleteAsync(T entity)`: Eliminar (soft delete)

### Consultas con Paginación

- `GetPagedAsync(int pageNumber, int pageSize, bool includeDeleted = false)`: Paginación
- `GetActivePagedAsync(int pageNumber, int pageSize)`: Solo entidades activas

### Búsquedas (SimpleEntity)

- `SearchByNameAsync(string name, bool includeDeleted = false)`: Buscar por nombre
- `SearchByDescriptionAsync(string description, bool includeDeleted = false)`: Buscar por descripción

### Validaciones

- `ExistsAsync(int id, bool includeDeleted = false)`: Verificar existencia
- `CountAsync(bool includeDeleted = false)`: Contar registros

## Manejo de Relaciones

### Navegación Automática

Los repositorios que manejan entidades con relaciones incluyen automáticamente las entidades relacionadas:

```csharp
// LocationRepository incluye automáticamente LocationType
var query = DbSet.Include(e => e.LocationType).AsQueryable();

// UserRepository incluye automáticamente Role
var query = DbSet.Include(e => e.Role).AsQueryable();
```

### Restricciones de Eliminación

Las configuraciones de modelo implementan `DeleteBehavior.Restrict` para prevenir eliminaciones en cascada no deseadas.

## Optimizaciones

### Índices de Base de Datos

Los repositorios utilizan índices configurados en `ModelConfiguration`:

- Índices en campos de búsqueda frecuente (Name, Description)
- Índices en claves foráneas para optimizar JOINs
- Índices únicos compuestos para evitar duplicados

### Consultas Eficientes

- Uso de `AsQueryable()` para consultas diferidas
- Filtros aplicados a nivel de base de datos
- Proyecciones solo cuando es necesario
- Inclusión selectiva de navegaciones

## Context y Transacciones

### AppDbContext

Todos los repositorios reciben `AppDbContext` en el constructor y utilizan:

- `SaveChangesAsync(bool, string, CancellationToken)` para auditoría
- Manejo automático de campos de auditoría
- Soft delete automático en lugar de eliminación física

### Transacciones

Las operaciones complejas pueden utilizar transacciones explícitas cuando sea necesario para mantener consistencia de datos.
