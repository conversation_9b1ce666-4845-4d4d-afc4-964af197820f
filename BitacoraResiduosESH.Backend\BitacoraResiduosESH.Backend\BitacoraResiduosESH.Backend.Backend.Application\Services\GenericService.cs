namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class GenericService<TEntity, TDto, TFilterDto, TCreateDto, TUpdateDto>(IGenericRepository<TEntity> repository)
    : IGenericService<TEntity, TDto, TFilterDto, TCreateDto, TUpdateDto>
    where TEntity : BaseEntity, new()
    where TDto : BaseEntityDto, new()
    where TFilterDto : BaseEntityFilterDto
    where TCreateDto : CreateBaseEntityDto
    where TUpdateDto : UpdateBaseEntityDto
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    #region Operaciones de validación

    public async Task<bool> ExistsAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Verificando existencia de {EntityType} ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, id, includeDeleted);

        var result = await repository.ExistsAsync(id, includeDeleted);

        Logger.Debug("Verificación de existencia de {EntityType} ID {Id}: {Exists}", typeof(TEntity).Name, id, result);

        return result;
    }

    #endregion

    #region Operaciones de lectura

    public async Task<TDto?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo {EntityType} por ID: {Id}, IncludeDeleted: {IncludeDeleted}", typeof(TEntity).Name, id,
            includeDeleted);

        var entity = await repository.GetByIdAsync(id, includeDeleted);
        var result = entity != null ? MapToDto(entity) : null;

        Logger.Debug("{EntityType} obtenida por ID {Id}: {Found}", typeof(TEntity).Name, id, result != null);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetAllAsync(PaginationFilter filter)
    {
        Logger.Debug(
            "Obteniendo todas las {EntityType} paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, filter.IncludeDeleted);

        var pagedResult = await repository.GetAllAsync(filter);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug("Todas las {EntityType} obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetPagedAsync(TFilterDto filter)
    {
        Logger.Debug("Obteniendo {EntityType} paginadas - Página: {PageNumber}, Tamaño: {PageSize}, Filtros: {@Filter}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, filter);

        var paginationFilter = new PaginationFilter
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            IncludeDeleted = filter.IncludeDeleted
        };

        var pagedResult = await repository.GetPagedAsync(paginationFilter);

        // Aplicar filtros adicionales en memoria
        var filteredData = pagedResult.Data.AsEnumerable();

        if (filter.Active.HasValue)
            filteredData = filteredData.Where(e => e.Active == filter.Active.Value);

        if (filter.CreatedFrom.HasValue)
            filteredData = filteredData.Where(e => e.Created >= filter.CreatedFrom.Value);

        if (filter.CreatedTo.HasValue)
            filteredData = filteredData.Where(e => e.Created <= filter.CreatedTo.Value);

        if (!string.IsNullOrWhiteSpace(filter.CreatedBy))
        {
            var createdByLower = filter.CreatedBy.ToLower();
            filteredData = filteredData.Where(e => e.CreatedBy.ToLower().Contains(createdByLower));
        }

        if (filter.UpdatedFrom.HasValue)
            filteredData = filteredData.Where(e => e.Updated >= filter.UpdatedFrom.Value);

        if (filter.UpdatedTo.HasValue)
            filteredData = filteredData.Where(e => e.Updated <= filter.UpdatedTo.Value);

        if (!string.IsNullOrWhiteSpace(filter.UpdatedBy))
        {
            var updatedByLower = filter.UpdatedBy.ToLower();
            filteredData =
                filteredData.Where(e => e.UpdatedBy != null && e.UpdatedBy.ToLower().Contains(updatedByLower));
        }

        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = filteredData.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "{EntityType} paginadas obtenidas - Total: {TotalRecords}, Página: {PageNumber}, Elementos: {Count}",
            typeof(TEntity).Name, result.TotalRecords, result.PageNumber, result.Data.Count);

        return result;
    }

    public async Task<PagedResponse<TDto>> GetActiveAsync(PaginationFilter filter)
    {
        Logger.Debug("Obteniendo {EntityType} activas paginadas - Página: {PageNumber}, Tamaño: {PageSize}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize);

        var pagedResult = await repository.GetActiveAsync(filter);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug("{EntityType} activas obtenidas paginadas: {Count} elementos de {TotalRecords} totales",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords);

        return result;
    }

    public async Task<int> CountAsync(bool includeDeleted = false)
    {
        Logger.Debug("Contando {EntityType}, IncludeDeleted: {IncludeDeleted}", typeof(TEntity).Name, includeDeleted);

        var result = await repository.CountAsync(includeDeleted);

        Logger.Debug("Conteo de {EntityType} completado: {Count}", typeof(TEntity).Name, result);

        return result;
    }

    #endregion

    #region Operaciones de escritura

    public virtual async Task<TDto> CreateAsync(TCreateDto dto, string createdBy)
    {
        Logger.Debug("Creando nueva {EntityType} con DTO específico: {@Dto}, CreatedBy: {User}",
            typeof(TEntity).Name, dto, createdBy);

        var entity = MapToEntity(dto);
        var result = await AddEntityAsync(entity, createdBy);

        Logger.Info("{EntityType} creada exitosamente con ID: {Id} por usuario: {User}",
            typeof(TEntity).Name, result.Id, createdBy);

        return result;
    }

    public virtual async Task<IEnumerable<TDto>> CreateRangeAsync(IEnumerable<TCreateDto> dtos, string createdBy)
    {
        Logger.Debug("Creando {Count} {EntityType} con DTOs específicos, CreatedBy: {User}",
            dtos.Count(), typeof(TEntity).Name, createdBy);

        var entities = dtos.Select(dto => MapToEntity(dto));
        var result = await AddEntitiesAsync(entities, createdBy);

        Logger.Info("{Count} {EntityType} creadas exitosamente por usuario: {User}",
            result.Count(), typeof(TEntity).Name, createdBy);

        return result;
    }

    public virtual async Task<TDto> UpdateAsync(int id, TUpdateDto dto, string updatedBy)
    {
        Logger.Debug("Actualizando {EntityType} ID: {Id} con DTO específico: {@Dto}, UpdatedBy: {User}",
            typeof(TEntity).Name, id, dto, updatedBy);

        var existingEntity = await repository.GetByIdAsync(id);
        if (existingEntity == null)
        {
            Logger.Warn("Intento de actualizar {EntityType} inexistente con ID: {Id}", typeof(TEntity).Name, id);
            throw new InvalidOperationException($"No se encontró la entidad {typeof(TEntity).Name} con ID {id}");
        }

        var entity = MapToEntity(dto);
        entity.Id = id; // Asegurar que el ID sea correcto
        var result = await UpdateEntityAsync(entity, updatedBy);

        Logger.Info("{EntityType} actualizada exitosamente con ID: {Id} por usuario: {User}",
            typeof(TEntity).Name, result.Id, updatedBy);

        return result;
    }

    public virtual async Task<IEnumerable<TDto>> UpdateRangeAsync(IEnumerable<TUpdateDto> dtos, string updatedBy)
    {
        Logger.Debug("Actualizando {Count} {EntityType} con DTOs específicos, UpdatedBy: {User}",
            dtos.Count(), typeof(TEntity).Name, updatedBy);

        var results = new List<TDto>();
        foreach (var dto in dtos)
        {
            var result = await UpdateAsync(dto.Id, dto, updatedBy);
            results.Add(result);
        }

        Logger.Info("{Count} {EntityType} actualizadas exitosamente por usuario: {User}",
            results.Count, typeof(TEntity).Name, updatedBy);

        return results;
    }

    #endregion

    #region Operaciones de eliminación

    public async Task<bool> DeleteAsync(int id, string deletedBy)
    {
        Logger.Debug("Eliminando {EntityType} ID: {Id}, DeletedBy: {User}", typeof(TEntity).Name, id, deletedBy);

        var entity = await repository.GetByIdAsync(id);
        if (entity == null)
        {
            Logger.Warn("Intento de eliminar {EntityType} inexistente con ID: {Id}", typeof(TEntity).Name, id);
            return false;
        }

        var result = await DeleteAsync(entity, deletedBy);

        Logger.Info("{EntityType} eliminada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name, id,
            deletedBy);

        return result;
    }

    public async Task<bool> DeleteAsync(TEntity entity, string deletedBy)
    {
        Logger.Debug("Eliminando {EntityType} ID: {Id}, DeletedBy: {User}", typeof(TEntity).Name, entity.Id, deletedBy);

        entity.DeletedBy = deletedBy;
        entity.Deleted = DateTime.Now;
        entity.IsDeleted = true;

        var result = await repository.UpdateAsync(entity);

        Logger.Info("{EntityType} eliminada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            entity.Id, deletedBy);

        return result != null;
    }

    public async Task<bool> DeleteRangeAsync(IEnumerable<int> ids, string deletedBy)
    {
        Logger.Debug("Eliminando {Count} {EntityType}, DeletedBy: {User}", ids.Count(), typeof(TEntity).Name,
            deletedBy);

        var entities = new List<TEntity>();
        foreach (var id in ids)
        {
            var entity = await repository.GetByIdAsync(id);
            if (entity != null)
                entities.Add(entity);
        }

        var result = await DeleteRangeAsync(entities, deletedBy);

        Logger.Info("{Count} {EntityType} eliminadas exitosamente por usuario: {User}", entities.Count,
            typeof(TEntity).Name, deletedBy);

        return result;
    }

    public async Task<bool> DeleteRangeAsync(IEnumerable<TEntity> entities, string deletedBy)
    {
        Logger.Debug("Eliminando {Count} {EntityType}, DeletedBy: {User}", entities.Count(), typeof(TEntity).Name,
            deletedBy);

        var entitiesList = entities.ToList();
        foreach (var entity in entitiesList)
        {
            entity.DeletedBy = deletedBy;
            entity.Deleted = DateTime.Now;
            entity.IsDeleted = true;
        }

        var result = await repository.UpdateRangeAsync(entitiesList);

        Logger.Info("{Count} {EntityType} eliminadas exitosamente por usuario: {User}", result.Count(),
            typeof(TEntity).Name, deletedBy);

        return result.Any();
    }

    public async Task<bool> HardDeleteAsync(int id)
    {
        Logger.Debug("Eliminación permanente de {EntityType} ID: {Id}", typeof(TEntity).Name, id);

        var result = await repository.DeleteAsync(id, "hard_delete");

        Logger.Info("Eliminación permanente de {EntityType} completada con ID: {Id}", typeof(TEntity).Name, id);

        return result;
    }

    public async Task<bool> HardDeleteAsync(TEntity entity)
    {
        Logger.Debug("Eliminación permanente de {EntityType} ID: {Id}", typeof(TEntity).Name, entity.Id);

        var result = await repository.DeleteAsync(entity, "hard_delete");

        Logger.Info("Eliminación permanente de {EntityType} completada con ID: {Id}", typeof(TEntity).Name, entity.Id);

        return result;
    }

    public async Task<bool> HardDeleteRangeAsync(IEnumerable<int> ids)
    {
        Logger.Debug("Eliminación permanente de {Count} {EntityType}", ids.Count(), typeof(TEntity).Name);

        var result = await repository.DeleteRangeAsync(ids, "hard_delete");

        Logger.Info("Eliminación permanente de {Count} {EntityType} completada", ids.Count(), typeof(TEntity).Name);

        return result;
    }

    public async Task<bool> HardDeleteRangeAsync(IEnumerable<TEntity> entities)
    {
        Logger.Debug("Eliminación permanente de {Count} {EntityType}", entities.Count(), typeof(TEntity).Name);

        var result = await repository.DeleteRangeAsync(entities, "hard_delete");

        Logger.Info("Eliminación permanente de {Count} {EntityType} completada", entities.Count(),
            typeof(TEntity).Name);

        return result;
    }

    #endregion

    #region Operaciones de activación/desactivación

    public async Task<bool> ActivateAsync(int id, string updatedBy)
    {
        Logger.Debug("Activando {EntityType} ID: {Id}, UpdatedBy: {User}", typeof(TEntity).Name, id, updatedBy);

        var entity = await repository.GetByIdAsync(id);
        if (entity == null)
        {
            Logger.Warn("Intento de activar {EntityType} inexistente con ID: {Id}", typeof(TEntity).Name, id);
            return false;
        }

        var result = await ActivateAsync(entity, updatedBy);

        Logger.Info("{EntityType} activada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name, id,
            updatedBy);

        return result;
    }

    public async Task<bool> DeactivateAsync(int id, string updatedBy)
    {
        Logger.Debug("Desactivando {EntityType} ID: {Id}, UpdatedBy: {User}", typeof(TEntity).Name, id, updatedBy);

        var entity = await repository.GetByIdAsync(id);
        if (entity == null)
        {
            Logger.Warn("Intento de desactivar {EntityType} inexistente con ID: {Id}", typeof(TEntity).Name, id);
            return false;
        }

        var result = await DeactivateAsync(entity, updatedBy);

        Logger.Info("{EntityType} desactivada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name, id,
            updatedBy);

        return result;
    }

    public async Task<bool> ActivateAsync(TEntity entity, string updatedBy)
    {
        Logger.Debug("Activando {EntityType} ID: {Id}, UpdatedBy: {User}", typeof(TEntity).Name, entity.Id, updatedBy);

        entity.Active = true;
        entity.UpdatedBy = updatedBy;
        entity.Updated = DateTime.Now;

        var result = await repository.UpdateAsync(entity);

        Logger.Info("{EntityType} activada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            entity.Id, updatedBy);

        return result != null;
    }

    public async Task<bool> DeactivateAsync(TEntity entity, string updatedBy)
    {
        Logger.Debug("Desactivando {EntityType} ID: {Id}, UpdatedBy: {User}", typeof(TEntity).Name, entity.Id,
            updatedBy);

        entity.Active = false;
        entity.UpdatedBy = updatedBy;
        entity.Updated = DateTime.Now;

        var result = await repository.UpdateAsync(entity);

        Logger.Info("{EntityType} desactivada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            entity.Id, updatedBy);

        return result != null;
    }

    #endregion

    #region Operaciones de búsqueda

    public async Task<PagedResponse<TDto>> FindAsync(Expression<Func<TEntity, bool>> predicate, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando {EntityType} con predicado paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, filter.PageNumber, filter.PageSize, includeDeleted);

        var pagedResult = await repository.FindAsync(predicate, filter, includeDeleted);
        var result = new PagedResponse<TDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "Búsqueda de {EntityType} con predicado paginada completada: {Count} elementos de {TotalRecords} totales",
            typeof(TEntity).Name, result.Data.Count, result.TotalRecords);

        return result;
    }

    public async Task<TDto?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false)
    {
        Logger.Debug("Buscando primera {EntityType} con predicado, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name,
            includeDeleted);

        var entity = await repository.FirstOrDefaultAsync(predicate, includeDeleted);
        var result = entity != null ? MapToDto(entity) : null;

        Logger.Debug("Búsqueda de primera {EntityType} completada: {Found}", typeof(TEntity).Name, result != null);

        return result;
    }

    public async Task<TDto> FirstAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false)
    {
        Logger.Debug("Buscando primera {EntityType} con predicado, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name,
            includeDeleted);

        var entity = await repository.FirstAsync(predicate, includeDeleted);
        var result = MapToDto(entity);

        Logger.Debug("Búsqueda de primera {EntityType} completada", typeof(TEntity).Name);

        return result;
    }

    #endregion

    #region Métodos privados para manejo interno de entidades

    protected async Task<TDto> AddEntityAsync(TEntity entity, string createdBy)
    {
        Logger.Debug("Agregando nueva {EntityType}: {@Entity}, CreatedBy: {User}", typeof(TEntity).Name, entity,
            createdBy);

        entity.CreatedBy = createdBy;
        entity.Created = DateTime.Now;

        var createdEntity = await repository.AddAsync(entity);
        var result = MapToDto(createdEntity);

        Logger.Info("{EntityType} agregada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            result.Id, createdBy);

        return result;
    }

    private async Task<IEnumerable<TDto>> AddEntitiesAsync(IEnumerable<TEntity> entities, string createdBy)
    {
        Logger.Debug("Agregando {Count} {EntityType}, CreatedBy: {User}", entities.Count(), typeof(TEntity).Name,
            createdBy);

        var entitiesList = entities.ToList();
        foreach (var entity in entitiesList)
        {
            entity.CreatedBy = createdBy;
            entity.Created = DateTime.Now;
        }

        var createdEntities = await repository.AddRangeAsync(entitiesList);
        var result = createdEntities.Select(MapToDto);

        Logger.Info("{Count} {EntityType} agregadas exitosamente por usuario: {User}", result.Count(),
            typeof(TEntity).Name, createdBy);

        return result;
    }

    private async Task<TDto> UpdateEntityAsync(TEntity entity, string updatedBy)
    {
        Logger.Debug("Actualizando {EntityType} ID: {Id}, UpdatedBy: {User}", typeof(TEntity).Name, entity.Id,
            updatedBy);

        entity.UpdatedBy = updatedBy;
        entity.Updated = DateTime.Now;

        var updatedEntity = await repository.UpdateAsync(entity);
        var result = MapToDto(updatedEntity);

        Logger.Info("{EntityType} actualizada exitosamente con ID: {Id} por usuario: {User}", typeof(TEntity).Name,
            result.Id, updatedBy);

        return result;
    }

    #endregion

    #region Métodos protegidos para mapeo

    protected virtual TDto MapToDto(TEntity entity)
    {
        var dto = new TDto
        {
            Id = entity.Id,
            Created = entity.Created,
            CreatedString = entity.Created.ToString(DateFormats.LONG_DATE_TIME),
            CreatedBy = entity.CreatedBy,
            IsDeleted = entity.IsDeleted,
            Deleted = entity.Deleted,
            DeletedString = entity.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
            DeletedBy = entity.DeletedBy,
            Updated = entity.Updated,
            UpdatedString = entity.Updated?.ToString(DateFormats.LONG_DATE_TIME),
            UpdatedBy = entity.UpdatedBy,
            Active = entity.Active
        };

        return dto;
    }

    protected virtual TEntity MapToEntity(TCreateDto dto)
    {
        // Implementación base - las clases derivadas pueden sobrescribir este método
        // para mapear propiedades específicas del DTO a la entidad
        var entity = new TEntity
        {
            Active = dto.Active
        };

        // Aquí se pueden agregar mapeos específicos si es necesario
        // Por ejemplo, si el DTO tiene propiedades específicas

        return entity;
    }

    protected virtual TEntity MapToEntity(TUpdateDto dto)
    {
        // Implementación base - las clases derivadas pueden sobrescribir este método
        // para mapear propiedades específicas del DTO a la entidad
        var entity = new TEntity
        {
            Id = dto.Id
        };

        // Aquí se pueden agregar mapeos específicos si es necesario
        // Por ejemplo, si el DTO tiene propiedades específicas

        return entity;
    }

    #endregion
}