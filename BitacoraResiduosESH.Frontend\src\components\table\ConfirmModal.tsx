import React from 'react';
import {AlertTriangle} from 'lucide-react';
import Modal from '../Modal';

export interface ConfirmModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title?: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    variant?: 'danger' | 'warning' | 'info';
    loading?: boolean;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
                                                       isOpen,
                                                       onClose,
                                                       onConfirm,
                                                       title = 'Confirmar acción',
                                                       message,
                                                       confirmText = 'Confirmar',
                                                       cancelText = 'Cancelar',
                                                       variant = 'danger',
                                                       loading = false,
                                                   }) => {
    const getVariantStyles = () => {
        switch (variant) {
            case 'danger':
                return {
                    iconColor: '#dc2626',
                    confirmButtonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
                };
            case 'warning':
                return {
                    iconColor: '#d97706',
                    confirmButtonClass: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
                };
            case 'info':
                return {
                    iconColor: '#2563eb',
                    confirmButtonClass: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
                };
            default:
                return {
                    iconColor: '#dc2626',
                    confirmButtonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
                };
        }
    };

    const {iconColor, confirmButtonClass} = getVariantStyles();

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={title}
            size="sm"
            closeOnOverlayClick={!loading}
            closeOnEscape={!loading}
        >
            <div className="flex flex-col items-center text-center">
                {/* Icono */}
                <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full mb-4"
                     style={{backgroundColor: `${iconColor}20`}}>
                    <AlertTriangle className="h-6 w-6" style={{color: iconColor}}/>
                </div>

                {/* Mensaje */}
                <p
                    className="text-sm mb-6"
                    style={{color: 'var(--text-primary)'}}
                >
                    {message}
                </p>

                {/* Botones */}
                <div className="flex space-x-3 w-full">
                    <button
                        onClick={onClose}
                        disabled={loading}
                        className="flex-1 px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                        style={{
                            borderColor: 'var(--border-color)',
                            color: 'var(--text-primary)',
                        }}
                    >
                        {cancelText}
                    </button>
                    <button
                        onClick={onConfirm}
                        disabled={loading}
                        className={`flex-1 px-4 py-2 text-sm font-medium text-white rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 ${confirmButtonClass}`}
                    >
                        {loading ? (
                            <div className="flex items-center justify-center">
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none"
                                     viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                            strokeWidth="4"/>
                                    <path className="opacity-75" fill="currentColor"
                                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                                </svg>
                                Procesando...
                            </div>
                        ) : (
                            confirmText
                        )}
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default ConfirmModal; 