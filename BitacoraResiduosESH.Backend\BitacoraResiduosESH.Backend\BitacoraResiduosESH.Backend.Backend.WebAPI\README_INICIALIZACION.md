# Inicialización de Datos de Prueba

## Descripción

El sistema incluye una funcionalidad de inicialización automática que crea datos de prueba cuando la base de datos está vacía. Esto facilita el desarrollo y testing de la aplicación.

## Datos Creados Automáticamente

### 1. Rol Administrador
- **Nombre**: `Admin`
- **Descripción**: `Administrador del sistema con acceso completo`

### 2. Usuario Administrador
- **Nombre**: `Administrador`
- **Email**: `<EMAIL>`
- **Número de Empleado**: `ADMIN001`
- **Contraseña**: `Admin123!`
- **Rol**: `Admin`

### 3. Tipos de Herramientas
Se crean 5 tipos de herramientas predefinidos:
1. **Herramienta Manual** - Herramientas operadas manualmente
2. **Herramienta Eléctrica** - Herramientas que requieren energía eléctrica
3. **Herramienta Neumática** - Herramientas que usan aire comprimido
4. **Herramienta de Medición** - Herramientas para medir dimensiones
5. **Herramienta de Corte** - Herramientas para cortar materiales

## Cómo Funciona

### Activación
La inicialización se ejecuta automáticamente en el `Program.cs`:

```csharp
// Inicializar la base de datos con datos de prueba
await app.Services.InitializeDatabaseAsync();
```

### Condiciones
- Solo se ejecuta si la base de datos está vacía
- Verifica si existe al menos un rol antes de proceder
- Es idempotente (seguro de ejecutar múltiples veces)

### Logs
El proceso genera logs detallados:
```
INFO: Inicializando base de datos con datos de prueba...
INFO: Rol Admin creado exitosamente
INFO: Usuario administrador creado exitosamente
INFO: 5 tipos de herramientas creados exitosamente
INFO: Inicialización de base de datos completada exitosamente
```

## Uso de las Credenciales

### Login Inmediato
Una vez que la aplicación se inicia, puedes hacer login inmediatamente:

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}
```

### Acceso Completo
Con estas credenciales tienes acceso a:
- Todos los endpoints de `/api/tooltype/*`
- Todos los endpoints de `/api/role/*`
- Todos los endpoints de `/api/user/*`
- Todas las funcionalidades del sistema

## Configuración

### Deshabilitar Inicialización
Si no quieres datos de prueba automáticos, comenta la línea en `Program.cs`:

```csharp
// await app.Services.InitializeDatabaseAsync();
```

### Modificar Datos de Prueba
Para cambiar los datos de prueba, edita el método `InitializeDatabaseAsync` en `ServiceCollectionExtensions.cs`.

### Entorno de Producción
En producción, se recomienda:
1. Deshabilitar la inicialización automática
2. Crear usuarios manualmente
3. Cambiar las contraseñas por defecto
4. Configurar roles específicos del negocio

## Troubleshooting

### Error: "La base de datos ya contiene datos"
- Esto es normal si ya se ejecutó la inicialización
- Los datos existentes no se modifican

### Error: "Error al inicializar la base de datos"
- Verifica que la base de datos se pueda crear
- Revisa los logs para más detalles
- Asegúrate de que todos los servicios estén registrados

### Error de Autenticación
- Verifica que las credenciales sean exactas
- Asegúrate de que el usuario esté activo
- Revisa que el JWT esté configurado correctamente

## Archivos Relacionados

- `ServiceCollectionExtensions.cs` - Método de inicialización
- `Program.cs` - Llamada al método de inicialización
- `DATOS_DE_PRUEBA.md` - Documentación detallada
- `Auth.http` - Ejemplos de requests de autenticación
- `ToolType.http` - Ejemplos de requests para ToolType

## Próximos Pasos

1. **Ejecutar la aplicación** - Los datos se crearán automáticamente
2. **Hacer login** - Usar las credenciales de prueba
3. **Explorar los endpoints** - Probar las funcionalidades
4. **Crear datos adicionales** - Agregar más tipos de herramientas
5. **Personalizar** - Modificar según las necesidades del negocio 