import React from 'react';
import {useNavigate} from '@tanstack/react-router';
import {toast} from 'sonner';
import {useAuthStore} from '../stores/authStore';
import type {LoginRequest} from '../types/auth';

const Login: React.FC = () => {
    const navigate = useNavigate();
    const {login, isLoading, error, clearError} = useAuthStore();
    const [formData, setFormData] = React.useState<LoginRequest>({
        email: '',
        employeeNumber: '',
        password: '',
    });
    const [useEmail, setUseEmail] = React.useState(true);

    // Limpiar error cuando cambie el formulario
    React.useEffect(() => {
        if (error) {
            clearError();
        }
    }, [formData, clearError, error]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const {name, value} = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validar que se proporcione al menos un identificador
        if (!formData.email && !formData.employeeNumber) {
            toast.error('Debe proporcionar email o número de empleado');
            return;
        }

        if (!formData.password) {
            toast.error('La contraseña es requerida');
            return;
        }

        // Limpiar campos no utilizados
        const credentials: LoginRequest = {
            password: formData.password,
        };

        if (useEmail && formData.email) {
            credentials.email = formData.email;
        } else if (!useEmail && formData.employeeNumber) {
            credentials.employeeNumber = formData.employeeNumber;
        }

        try {
            const success = await login(credentials);
            if (success) {
                toast.success('Inicio de sesión exitoso');
                navigate({to: '/'});
            } else {
                toast.error('Credenciales inválidas');
            }
        } catch (error) {
            toast.error('Error al iniciar sesión');
        }
    };

    const toggleIdentifierType = () => {
        setUseEmail(!useEmail);
        // Limpiar el campo que no se está usando
        setFormData(prev => ({
            ...prev,
            email: useEmail ? '' : prev.email,
            employeeNumber: useEmail ? prev.employeeNumber : '',
        }));
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div>
                    <img
                        className="mx-auto h-12 w-auto"
                        src="/molex-logo-1.png"
                        alt="Logo"
                    />
                    <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                        Iniciar Sesión
                    </h2>
                    <p className="mt-2 text-center text-sm text-gray-600">
                        Sistema de Gestión de Herramientas
                    </p>
                </div>

                <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                    <div className="rounded-md shadow-sm -space-y-px">
                        {/* Selector de tipo de identificador */}
                        <div className="flex mb-4">
                            <button
                                type="button"
                                onClick={toggleIdentifierType}
                                className={`flex-1 py-2 px-4 text-sm font-medium rounded-l-md border ${
                                    useEmail
                                        ? 'bg-blue-600 text-white border-blue-600'
                                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                }`}
                            >
                                Email
                            </button>
                            <button
                                type="button"
                                onClick={toggleIdentifierType}
                                className={`flex-1 py-2 px-4 text-sm font-medium rounded-r-md border-t border-r border-b ${
                                    !useEmail
                                        ? 'bg-blue-600 text-white border-blue-600'
                                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                }`}
                            >
                                Número de Empleado
                            </button>
                        </div>

                        {/* Campo de identificador */}
                        <div>
                            <label htmlFor={useEmail ? 'email' : 'employeeNumber'} className="sr-only">
                                {useEmail ? 'Email' : 'Número de Empleado'}
                            </label>
                            <input
                                id={useEmail ? 'email' : 'employeeNumber'}
                                name={useEmail ? 'email' : 'employeeNumber'}
                                type={useEmail ? 'email' : 'text'}
                                autoComplete={useEmail ? 'email' : 'off'}
                                required
                                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                placeholder={useEmail ? 'Email' : 'Número de Empleado'}
                                value={useEmail ? formData.email : formData.employeeNumber}
                                onChange={handleInputChange}
                            />
                        </div>

                        {/* Campo de contraseña */}
                        <div>
                            <label htmlFor="password" className="sr-only">
                                Contraseña
                            </label>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                autoComplete="current-password"
                                required
                                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                placeholder="Contraseña"
                                value={formData.password}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>

                    {/* Botón de envío */}
                    <div>
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? (
                                <div className="flex items-center">
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                         xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor"
                                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Iniciando sesión...
                                </div>
                            ) : (
                                'Iniciar Sesión'
                            )}
                        </button>
                    </div>

                    {/* Información adicional */}
                    <div className="text-center">
                        <p className="text-xs text-gray-500">
                            Puede usar su email o número de empleado para iniciar sesión
                        </p>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default Login; 