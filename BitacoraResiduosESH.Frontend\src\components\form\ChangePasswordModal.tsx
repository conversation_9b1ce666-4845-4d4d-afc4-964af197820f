import React, {useState} from 'react';
import {Lock, X} from 'lucide-react';
import type {AdminChangePasswordDto} from '../../types/user';

interface ChangePasswordModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: AdminChangePasswordDto) => Promise<void>;
    userId: number;
    userName: string;
    isLoading?: boolean;
}

export const ChangePasswordModal: React.FC<ChangePasswordModalProps> = ({
                                                                            isOpen,
                                                                            onClose,
                                                                            onSubmit,
                                                                            userId,
                                                                            userName,
                                                                            isLoading = false,
                                                                        }) => {
    const [formData, setFormData] = useState({
        newPassword: '',
        confirmNewPassword: '',
    });
    const [errors, setErrors] = useState<Record<string, string>>({});

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const {name, value} = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
        }));

        // Limpiar error del campo cuando el usuario empiece a escribir
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: '',
            }));
        }
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.newPassword.trim()) {
            newErrors.newPassword = 'La nueva contraseña es requerida';
        } else if (formData.newPassword.length < 6) {
            newErrors.newPassword = 'La contraseña debe tener al menos 6 caracteres';
        }

        if (!formData.confirmNewPassword.trim()) {
            newErrors.confirmNewPassword = 'Confirme la nueva contraseña';
        } else if (formData.newPassword !== formData.confirmNewPassword) {
            newErrors.confirmNewPassword = 'Las contraseñas no coinciden';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            await onSubmit({
                userId,
                newPassword: formData.newPassword,
                confirmNewPassword: formData.confirmNewPassword,
            });

            // Limpiar formulario después de éxito
            setFormData({
                newPassword: '',
                confirmNewPassword: '',
            });
            setErrors({});
            onClose();
        } catch (error) {
            // El error se maneja en el componente padre
            console.error('Error al cambiar contraseña:', error);
        }
    };

    const handleClose = () => {
        setFormData({
            newPassword: '',
            confirmNewPassword: '',
        });
        setErrors({});
        onClose();
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* Overlay */}
            <div
                className="absolute inset-0 bg-black/50"
                onClick={handleClose}
            />

            {/* Modal */}
            <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                        <Lock className="h-6 w-6 text-blue-600"/>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Cambiar Contraseña
                        </h3>
                    </div>
                    <button
                        onClick={handleClose}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                    >
                        <X className="h-5 w-5"/>
                    </button>
                </div>

                {/* Content */}
                <form onSubmit={handleSubmit} className="p-6 space-y-4">
                    <div className="mb-4">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            Cambiando contraseña para: <span
                            className="font-medium text-gray-900 dark:text-white">{userName}</span>
                        </p>
                    </div>

                    <div>
                        <label htmlFor="newPassword" className="block text-sm font-medium mb-1">
                            Nueva Contraseña *
                        </label>
                        <input
                            type="password"
                            id="newPassword"
                            name="newPassword"
                            value={formData.newPassword}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                errors.newPassword
                                    ? 'border-red-500 focus:ring-red-500'
                                    : 'border-gray-300 dark:border-gray-600'
                            }`}
                            placeholder="Ingrese la nueva contraseña"
                            disabled={isLoading}
                        />
                        {errors.newPassword && (
                            <p className="mt-1 text-sm text-red-600">{errors.newPassword}</p>
                        )}
                    </div>

                    <div>
                        <label htmlFor="confirmNewPassword" className="block text-sm font-medium mb-1">
                            Confirmar Nueva Contraseña *
                        </label>
                        <input
                            type="password"
                            id="confirmNewPassword"
                            name="confirmNewPassword"
                            value={formData.confirmNewPassword}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                errors.confirmNewPassword
                                    ? 'border-red-500 focus:ring-red-500'
                                    : 'border-gray-300 dark:border-gray-600'
                            }`}
                            placeholder="Confirme la nueva contraseña"
                            disabled={isLoading}
                        />
                        {errors.confirmNewPassword && (
                            <p className="mt-1 text-sm text-red-600">{errors.confirmNewPassword}</p>
                        )}
                    </div>

                    {/* Footer */}
                    <div
                        className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <button
                            type="button"
                            onClick={handleClose}
                            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                            disabled={isLoading}
                        >
                            Cancelar
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled={isLoading}
                        >
                            {isLoading ? 'Cambiando...' : 'Cambiar Contraseña'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}; 