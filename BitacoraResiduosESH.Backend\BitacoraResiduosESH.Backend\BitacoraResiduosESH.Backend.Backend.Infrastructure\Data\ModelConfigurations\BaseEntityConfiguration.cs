﻿namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

/// <summary>
///     Configuración base para todas las entidades que heredan de BaseEntity.
///     Proporciona la configuración estándar para propiedades de auditoría y estado.
/// </summary>
/// <typeparam name="T">Tipo de entidad que hereda de BaseEntity</typeparam>
public abstract class BaseEntityConfiguration<T> : IEntityTypeConfiguration<T> where T : BaseEntity
{
    /// <summary>
    ///     Nombre de la tabla en la base de datos (debe ser singular y Pascal case)
    /// </summary>
    protected abstract string TableName { get; }

    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        // Configuración de la tabla
        builder.ToTable(TableName);

        // Configuración de la clave primaria
        // Regla: ALL Primary Keys will be named <TableName>PK
        builder.HasKey(e => e.Id)
            .HasName($"{TableName}PK");

        // Configuración de propiedades de auditoría y estado
        ConfigureId(builder);
        ConfigureAuditFields(builder);
        ConfigureStateFields(builder);
        ConfigureIndexes(builder);
        ConfigureQueryFilter(builder);
    }

    /// <summary>
    ///     Configura la propiedad Id (clave primaria)
    /// </summary>
    protected virtual void ConfigureId(EntityTypeBuilder<T> builder)
    {
        builder.Property(e => e.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd()
            .IsRequired();
    }

    /// <summary>
    ///     Configura las propiedades de auditoría (Created, CreatedBy)
    /// </summary>
    protected virtual void ConfigureAuditFields(EntityTypeBuilder<T> builder)
    {
        // Created - Fecha de creación
        builder.Property(e => e.Created)
            .HasColumnName("Created")
            .HasColumnType("datetime2(3)")
            .IsRequired()
            .HasDefaultValueSql("getutcdate()");

        // CreatedBy - Usuario que creó
        builder.Property(e => e.CreatedBy)
            .HasColumnName("CreatedBy")
            .HasColumnType("nvarchar(50)")
            .IsRequired()
            .HasDefaultValueSql("suser_name()");
    }

    /// <summary>
    ///     Configura las propiedades de estado (Updated, UpdatedBy, Active, IsDeleted, Deleted, DeletedBy)
    /// </summary>
    protected virtual void ConfigureStateFields(EntityTypeBuilder<T> builder)
    {
        // Updated - Fecha de última actualización
        builder.Property(e => e.Updated)
            .HasColumnName("Updated")
            .HasColumnType("datetime2(3)")
            .IsRequired(false);

        // UpdatedBy - Usuario que actualizó
        builder.Property(e => e.UpdatedBy)
            .HasColumnName("UpdatedBy")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);

        // Active - Estado activo/inactivo
        builder.Property(e => e.Active)
            .HasColumnName("Active")
            .HasColumnType("bit")
            .IsRequired()
            .HasDefaultValue(true);

        // IsDeleted - Indica si está eliminado (soft delete)
        builder.Property(e => e.IsDeleted)
            .HasColumnName("IsDeleted")
            .HasColumnType("bit")
            .IsRequired()
            .HasDefaultValue(false);

        // Deleted - Fecha de eliminación
        builder.Property(e => e.Deleted)
            .HasColumnName("Deleted")
            .HasColumnType("datetime2(3)")
            .IsRequired(false);

        // DeletedBy - Usuario que eliminó
        builder.Property(e => e.DeletedBy)
            .HasColumnName("DeletedBy")
            .HasColumnType("nvarchar(50)")
            .IsRequired(false);
    }

    /// <summary>
    ///     Configura los índices básicos para mejorar el rendimiento
    /// </summary>
    protected virtual void ConfigureIndexes(EntityTypeBuilder<T> builder)
    {
        // Índice para IsDeleted (usado en filtros globales)
        builder.HasIndex(e => e.IsDeleted)
            .HasDatabaseName($"IX_{TableName}_IsDeleted");

        // Índice para Active (usado en consultas de elementos activos)
        builder.HasIndex(e => e.Active)
            .HasDatabaseName($"IX_{TableName}_Active");

        // Índice para Created (usado en ordenamiento y filtros por fecha)
        builder.HasIndex(e => e.Created)
            .HasDatabaseName($"IX_{TableName}_Created");

        // Índice compuesto para consultas comunes
        builder.HasIndex(e => new { e.Active, e.IsDeleted })
            .HasDatabaseName($"IX_{TableName}_Active_IsDeleted");
    }

    /// <summary>
    ///     Configura el filtro global para excluir entidades eliminadas
    /// </summary>
    protected virtual void ConfigureQueryFilter(EntityTypeBuilder<T> builder)
    {
        // Filtro global: excluir entidades eliminadas automáticamente
        //builder.HasQueryFilter(e => !e.IsDeleted);
    }
}