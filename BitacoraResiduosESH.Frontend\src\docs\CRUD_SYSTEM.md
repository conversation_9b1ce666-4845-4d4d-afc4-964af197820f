# SimpleEntity CRUD System Documentation

This document explains how to use the reusable SimpleEntity CRUD system to quickly create complete CRUD interfaces for entities that extend the `SimpleEntity` base type.

## Overview

The SimpleEntity CRUD system provides a complete, reusable solution for creating CRUD interfaces with minimal boilerplate code. It includes:

- **Generic Service Layer**: `BaseSimpleEntityService` for API operations
- **Generic Hook Layer**: `useSimpleEntityCrud` for Tanstack Query integration
- **Generic Component Layer**: `SimpleEntityTable` for UI rendering
- **Type System**: TypeScript interfaces for type safety

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Page/View     │───▶│   Custom Hook    │───▶│   Service       │
│   Component     │    │   (useEntityCrud)│    │   (EntityService)│
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ SimpleEntity    │    │ useSimpleEntity  │    │ BaseSimpleEntity│
│ Table           │    │ Crud             │    │ Service         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Quick Start Guide

### Option A: Using Factory Utilities (Recommended)

For the simplest setup, use the factory utilities:

```typescript
// In src/services/yourEntityService.ts
import type { YourEntity, CreateYourEntityDto, UpdateYourEntityDto } from '@/types/yourEntity';
import { createSimpleEntityCrud } from '@/utils/simpleEntityFactory';

// Create both service and hook in one line
export const { service: yourEntityService, useCrud: useYourEntityCrud } = 
    createSimpleEntityCrud<YourEntity, CreateYourEntityDto, UpdateYourEntityDto>(
        'YourEntity', // Entity name for cache keys and toast messages
        'your-api-endpoint' // API endpoint base URL
    );
```

That's it! You now have a fully functional service and hook ready to use.

### Option B: Manual Setup (For Custom Requirements)

### Step 1: Define Your Entity Types

Create type definitions for your entity in `src/types/yourEntity.ts`:

```typescript
import type { SimpleEntity, CreateSimpleEntityDto, UpdateSimpleEntityDto } from './simpleEntity';

// Your entity interface (extends SimpleEntity)
export interface YourEntity extends SimpleEntity {
  // Add any additional properties specific to your entity here
  // For basic entities, no additional properties are needed
}

// Create DTO (Data Transfer Object)
export interface CreateYourEntityDto extends CreateSimpleEntityDto {
  // Add any additional properties for creation
}

// Update DTO
export interface UpdateYourEntityDto extends UpdateSimpleEntityDto {
  // Add any additional properties for updates
}

// Filter DTO (optional)
export interface YourEntityFilterDto {
  pageNumber: number;
  pageSize: number;
  includeDeleted: boolean;
  name?: string;
  description?: string;
  // Add any additional filter properties
}
```

### Step 2: Create Your Service

Create a service in `src/services/yourEntityService.ts`:

```typescript
import type { YourEntity, CreateYourEntityDto, UpdateYourEntityDto } from '@/types/yourEntity';
import { BaseSimpleEntityService } from './simpleEntityService';

/**
 * Service for YourEntity operations
 * Extends the base SimpleEntity service with YourEntity-specific typing
 */
class YourEntityService extends BaseSimpleEntityService<YourEntity, CreateYourEntityDto, UpdateYourEntityDto> {
    constructor() {
        super('your-api-endpoint'); // Replace with your actual API endpoint
    }

    // Add any entity-specific methods here if needed
    // For basic entities, all CRUD operations are inherited
}

// Export a singleton instance
export const yourEntityService = new YourEntityService();
```

### Step 3: Create Your Custom Hook

Create a custom hook in `src/hooks/useYourEntityCrud.ts`:

```typescript
import type { YourEntity, CreateYourEntityDto, UpdateYourEntityDto } from '@/types/yourEntity';
import { useSimpleEntityCrud } from './useSimpleEntityCrud';
import { yourEntityService } from '@/services/yourEntityService';

/**
 * Custom hook for YourEntity CRUD operations
 * Provides all standard CRUD operations with proper typing
 */
export const useYourEntityCrud = () => {
    return useSimpleEntityCrud<YourEntity, CreateYourEntityDto, UpdateYourEntityDto>(
        'YourEntity', // Entity name for cache keys and toast messages
        yourEntityService
    );
};
```

### Step 4: Create Your Page Component

Create your page component in `src/pages/YourEntities.tsx`:

```typescript
import React, { useState } from 'react';
import { SimpleEntityTable } from '@/components/table/SimpleEntityTable';
import { useYourEntityCrud } from '@/hooks/useYourEntityCrud';
import type { YourEntity, CreateYourEntityDto, UpdateYourEntityDto } from '@/types/yourEntity';

export function YourEntities() {
    // State management
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');

    // CRUD hooks
    const { useGetAll, useCreate, useUpdate, useDelete, useActivate, useDeactivate } = useYourEntityCrud();

    // Data fetching
    const { data, isLoading } = useGetAll(pageNumber, pageSize, false);
    const createMutation = useCreate();
    const updateMutation = useUpdate();
    const deleteMutation = useDelete();
    const activateMutation = useActivate();
    const deactivateMutation = useDeactivate();

    // Event handlers
    const handlePageChange = (page: number) => setPageNumber(page);
    const handlePageSizeChange = (size: number) => setPageSize(size);
    const handleSearch = (term: string) => setSearchTerm(term);

    const handleCreate = async (data: CreateYourEntityDto) => {
        await createMutation.mutateAsync(data);
    };

    const handleUpdate = async (id: number, data: UpdateYourEntityDto) => {
        await updateMutation.mutateAsync({ id, data });
    };

    const handleDelete = async (id: number) => {
        await deleteMutation.mutateAsync(id);
    };

    const handleActivate = async (id: number) => {
        await activateMutation.mutateAsync(id);
    };

    const handleDeactivate = async (id: number) => {
        await deactivateMutation.mutateAsync(id);
    };

    // Form fields renderer (optional customization)
    const renderFormFields = (data?: YourEntity) => (
        <>
            <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium mb-1">Nombre</label>
                    <input
                        type="text"
                        name="name"
                        defaultValue={data?.name || ''}
                        className="w-full px-3 py-2 border rounded-md"
                        required
                    />
                </div>
                <div>
                    <label className="block text-sm font-medium mb-1">Descripción</label>
                    <textarea
                        name="description"
                        defaultValue={data?.description || ''}
                        className="w-full px-3 py-2 border rounded-md"
                        rows={3}
                    />
                </div>
                <div className="flex items-center">
                    <input
                        type="checkbox"
                        name="active"
                        defaultChecked={data?.active ?? true}
                        className="mr-2"
                    />
                    <label className="text-sm font-medium">Activo</label>
                </div>
            </div>
        </>
    );

    // Details renderer (optional customization)
    const renderDetails = (data: YourEntity) => (
        <div className="space-y-4">
            <div>
                <strong>ID:</strong> {data.id}
            </div>
            <div>
                <strong>Nombre:</strong> {data.name}
            </div>
            <div>
                <strong>Descripción:</strong> {data.description || 'N/A'}
            </div>
            <div>
                <strong>Estado:</strong> {data.active ? 'Activo' : 'Inactivo'}
            </div>
            <div>
                <strong>Creado:</strong> {data.createdLocalString}
            </div>
            <div>
                <strong>Actualizado:</strong> {data.updatedLocalString || 'N/A'}
            </div>
        </div>
    );

    return (
        <SimpleEntityTable<YourEntity, CreateYourEntityDto, UpdateYourEntityDto>
            // Data and state
            data={data?.items || []}
            isLoading={isLoading}
            totalRecords={data?.totalRecords || 0}
            pageNumber={pageNumber}
            pageSize={pageSize}
            
            // Event handlers
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            onSearch={handleSearch}
            onCreate={handleCreate}
            onUpdate={handleUpdate}
            onDelete={handleDelete}
            onActivate={handleActivate}
            onDeactivate={handleDeactivate}
            
            // Configuration
            entityName="YourEntity"
            entityNamePlural="YourEntities"
            searchPlaceholder="Buscar entidades..."
            showActiveToggle={true}
            
            // Custom renderers (optional)
            renderFormFields={renderFormFields}
            renderDetails={renderDetails}
        />
    );
}
```

### Step 5: Add Route Configuration

Add your route in `src/routes.tsx`:

```typescript
import { YourEntities } from '@/pages/YourEntities';

// Add to your routes configuration
{
    path: '/your-entities',
    element: (
        <ProtectedRoute requiredRoles={['Admin', 'Manager']}>
            <DashboardLayout>
                <YourEntities />
            </DashboardLayout>
        </ProtectedRoute>
    ),
}
```

## Factory Utilities

The system includes factory utilities in `src/utils/simpleEntityFactory.ts` that provide convenient ways to create services and hooks with minimal boilerplate:

### Available Factory Functions

#### `createSimpleEntityService<T>(baseUrl: string)`
Creates a service instance directly:

```typescript
import { createSimpleEntityService } from '@/utils/simpleEntityFactory';
import type { Category } from '@/types/category';

export const categoryService = createSimpleEntityService<Category>('categories');
```

#### `createSimpleEntityCrudHook<T>(entityName: string, service: Service)`
Creates a custom hook for an existing service:

```typescript
import { createSimpleEntityCrudHook } from '@/utils/simpleEntityFactory';

export const useCategoryCrud = createSimpleEntityCrudHook('Category', categoryService);
```

#### `createSimpleEntityCrud<T>(entityName: string, baseUrl: string)`
Creates both service and hook in one call:

```typescript
import { createSimpleEntityCrud } from '@/utils/simpleEntityFactory';
import type { Category, CreateCategoryDto, UpdateCategoryDto } from '@/types/category';

export const { service: categoryService, useCrud: useCategoryCrud } = 
    createSimpleEntityCrud<Category, CreateCategoryDto, UpdateCategoryDto>(
        'Category',
        'categories'
    );
```

### Benefits of Factory Utilities

1. **Reduced Boilerplate**: No need to create classes or write repetitive code
2. **Type Safety**: Full TypeScript support with proper generic inference
3. **Consistency**: Ensures all services follow the same patterns
4. **Maintainability**: Easier to update and maintain
5. **Quick Prototyping**: Perfect for rapid development

### When to Use Factory vs Manual Setup

**Use Factory Utilities When:**
- Creating simple entities with standard CRUD operations
- Rapid prototyping or development
- You don't need custom service methods
- Consistency across multiple entities is important

**Use Manual Setup When:**
- You need custom service methods
- Complex business logic is required
- You want to extend the base service significantly
- You need fine-grained control over the implementation

## Advanced Customization

### Custom Service Methods

If you need entity-specific API methods, extend your service:

```typescript
class YourEntityService extends BaseSimpleEntityService<YourEntity, CreateYourEntityDto, UpdateYourEntityDto> {
    constructor() {
        super('your-api-endpoint');
    }

    // Add custom methods
    async getBySpecialCriteria(criteria: string): Promise<YourEntity[]> {
        return this.request<YourEntity[]>(`/special/${encodeURIComponent(criteria)}`);
    }

    async performSpecialAction(id: number, data: any): Promise<boolean> {
        return this.request<boolean>(`/${id}/special-action`, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }
}
```

### Custom Hook Extensions

Extend your custom hook with additional queries/mutations:

```typescript
export const useYourEntityCrud = () => {
    const baseCrud = useSimpleEntityCrud<YourEntity, CreateYourEntityDto, UpdateYourEntityDto>(
        'YourEntity',
        yourEntityService
    );

    // Add custom queries
    const useGetBySpecialCriteria = (criteria: string) => {
        return useQuery({
            queryKey: ['YourEntity', 'special', criteria],
            queryFn: () => yourEntityService.getBySpecialCriteria(criteria),
            enabled: !!criteria,
        });
    };

    // Add custom mutations
    const useSpecialAction = () => {
        const queryClient = useQueryClient();
        return useMutation({
            mutationFn: ({ id, data }: { id: number; data: any }) => 
                yourEntityService.performSpecialAction(id, data),
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['YourEntity'] });
                toast.success('Acción especial completada');
            },
        });
    };

    return {
        ...baseCrud,
        useGetBySpecialCriteria,
        useSpecialAction,
    };
};
```

### Custom Table Actions

Add custom actions to your table:

```typescript
const renderActions = (data: YourEntity) => (
    <div className="flex items-center space-x-2">
        {/* Default actions */}
        <button
            onClick={() => handleViewDetails(data)}
            className="p-1 rounded-md hover:bg-blue-100 transition-colors"
            title="Ver detalles"
        >
            <Eye className="h-4 w-4 text-blue-600" />
        </button>
        
        {/* Custom action */}
        <button
            onClick={() => handleSpecialAction(data)}
            className="p-1 rounded-md hover:bg-purple-100 transition-colors"
            title="Acción especial"
        >
            <Star className="h-4 w-4 text-purple-600" />
        </button>
    </div>
);

// Pass to SimpleEntityTable
<SimpleEntityTable
    // ... other props
    renderActions={renderActions}
/>
```

## Best Practices

### 1. Naming Conventions
- Entity types: `PascalCase` (e.g., `UserRole`, `ProductCategory`)
- Service files: `camelCase` + `Service.ts` (e.g., `userRoleService.ts`)
- Hook files: `use` + `PascalCase` + `Crud.ts` (e.g., `useUserRoleCrud.ts`)
- Page components: `PascalCase` + plural (e.g., `UserRoles.tsx`)

### 2. Type Safety
- Always use proper TypeScript types
- Extend base interfaces rather than duplicating properties
- Use discriminated unions for complex state management

### 3. Error Handling
- The system includes built-in error handling with toast notifications
- Add custom error handling for specific business logic
- Use proper loading states and error boundaries

### 4. Performance
- The system includes proper caching with Tanstack Query
- Use pagination for large datasets
- Implement proper query invalidation strategies

### 5. Accessibility
- The SimpleEntityTable includes basic accessibility features
- Add ARIA labels and descriptions for custom components
- Ensure keyboard navigation works properly

## Examples

See the existing implementations for reference:
- **Area Entity**: `src/types/area.ts`, `src/services/areaService.ts`, `src/hooks/useAreaCrud.ts`, `src/pages/Areas.tsx`
- **Role Entity**: `src/types/role.ts`, `src/services/roleService.ts`, `src/hooks/useRoleCrud.ts`, `src/pages/Roles.tsx`

## Troubleshooting

### Common Issues

1. **TypeScript Errors**: Ensure all generic types are properly specified
2. **API Endpoint Issues**: Verify the baseUrl in your service matches the backend endpoint
3. **Cache Issues**: Use the `invalidateCache()` method from your custom hook
4. **Form Validation**: Implement proper validation in your `renderFormFields` function

### Debug Tips

1. Use React DevTools to inspect component state
2. Use Tanstack Query DevTools to debug cache and network requests
3. Check browser network tab for API call details
4. Use console.log in your custom hooks for debugging

## Migration Guide

If you have existing CRUD implementations that you want to migrate to this system:

1. **Extract Types**: Move your entity types to extend `SimpleEntity`
2. **Replace Service**: Replace your custom service with `BaseSimpleEntityService`
3. **Update Hooks**: Replace custom hooks with `useSimpleEntityCrud`
4. **Migrate Components**: Replace custom table components with `SimpleEntityTable`
5. **Test**: Thoroughly test all CRUD operations

This system provides a robust, scalable foundation for CRUD operations while maintaining flexibility for customization when needed.