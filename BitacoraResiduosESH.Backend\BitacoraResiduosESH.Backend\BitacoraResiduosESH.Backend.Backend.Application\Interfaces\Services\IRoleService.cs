namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;

public interface IRoleService : ISimpleEntityService<Role, RoleDto, CreateRoleDto, UpdateRoleDto, RoleFilterDto>
{
    // No se agregan métodos adicionales
    // Solo hereda todos los métodos de ISimpleEntityService:
    // - Operaciones de lectura: GetByIdAsync, GetAllAsync, GetPagedAsync, GetActiveAsync, GetByNameAsync, GetByDescriptionAsync
    // - Operaciones de escritura: CreateAsync, UpdateAsync
    // - Operaciones de eliminación: DeleteAsync, HardDeleteAsync
    // - Operaciones de activación/desactivación: ActivateAsync, DeactivateAsync
    // - Operaciones de validación: ExistsAsync, ExistsByNameAsync
}