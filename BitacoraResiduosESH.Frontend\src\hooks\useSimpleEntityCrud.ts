import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import type {PagedResponse} from '../config/api';
import type { SimpleEntity, CreateSimpleEntityDto, UpdateSimpleEntityDto } from '@/types/simpleEntity';

// Interfaz para el servicio de entidades SimpleEntity
export interface SimpleEntityService<T extends SimpleEntity, TCreate extends CreateSimpleEntityDto, TUpdate extends UpdateSimpleEntityDto> {
    create: (data: TCreate) => Promise<T>;
    update: (id: number, data: TUpdate) => Promise<T>;
    delete: (id: number) => Promise<boolean>;
    getById: (id: number, includeDeleted?: boolean) => Promise<T>;
    getAll: (pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<T>>;
    getActive: (pageNumber?: number, pageSize?: number) => Promise<PagedResponse<T>>;
    searchByName: (name: string, pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<T>>;
    searchByDescription: (description: string, pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<T>>;
    exists: (id: number, includeDeleted?: boolean) => Promise<boolean>;
    activate: (id: number) => Promise<boolean>;
    deactivate: (id: number) => Promise<boolean>;
}

// Función para crear query keys
export const createSimpleEntityQueryKeys = (entityName: string) => ({
    all: [entityName] as const,
    lists: () => [...createSimpleEntityQueryKeys(entityName).all, 'list'] as const,
    list: (filters: any) => [...createSimpleEntityQueryKeys(entityName).lists(), filters] as const,
    details: () => [...createSimpleEntityQueryKeys(entityName).all, 'detail'] as const,
    detail: (id: number) => [...createSimpleEntityQueryKeys(entityName).details(), id] as const,
});

// Hook genérico para entidades SimpleEntity
export const useSimpleEntityCrud = <
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto
>(
    entityName: string,
    service: SimpleEntityService<T, TCreate, TUpdate>
) => {
    const queryClient = useQueryClient();
    const queryKeys = createSimpleEntityQueryKeys(entityName);

    // Query para obtener todas las entidades
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: queryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => service.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener entidades activas
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: queryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => service.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener una entidad por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: queryKeys.detail(id),
            queryFn: () => service.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar entidades por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: queryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => service.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar entidades por descripción
    const useSearchByDescription = (
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: queryKeys.list({search: 'description', description, pageNumber, pageSize, includeDeleted}),
            queryFn: () => service.searchByDescription(description, pageNumber, pageSize, includeDeleted),
            enabled: !!description.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear una entidad
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: TCreate) => service.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: queryKeys.lists()});
                toast.success(`${entityName} creado exitosamente`);
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el ${entityName.toLowerCase()}: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar una entidad
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: TUpdate }) =>
                service.update(id, data),
            onSuccess: (updatedEntity) => {
                queryClient.invalidateQueries({queryKey: queryKeys.lists()});
                queryClient.setQueryData(queryKeys.detail(updatedEntity.id), updatedEntity);
                toast.success(`${entityName} actualizado exitosamente`);
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el ${entityName.toLowerCase()}: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar una entidad
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => service.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: queryKeys.lists()});
                queryClient.removeQueries({queryKey: queryKeys.detail(deletedId)});
                toast.success(`${entityName} eliminado exitosamente`);
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el ${entityName.toLowerCase()}: ${error.message}`);
            },
        });
    };

    // Mutation para activar una entidad
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => service.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: queryKeys.lists()});
                toast.success(`${entityName} activado exitosamente`);
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el ${entityName.toLowerCase()}: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar una entidad
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => service.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: queryKeys.lists()});
                toast.success(`${entityName} desactivado exitosamente`);
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el ${entityName.toLowerCase()}: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: queryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByDescription,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
}; 