using BCrypt.Net;

namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Services;

/// <summary>
///     Implementación del servicio de hash de contraseñas usando BCrypt
/// </summary>
public class PasswordService : IPasswordService
{
    /// <summary>
    ///     Genera un hash seguro de la contraseña usando BCrypt
    /// </summary>
    /// <param name="password">Contraseña en texto plano</param>
    /// <returns>Hash de la contraseña con salt incluido</returns>
    public string HashPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("La contraseña no puede estar vacía", nameof(password));

        // BCrypt genera automáticamente un salt y lo incluye en el hash
        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
    }

    /// <summary>
    ///     Verifica si una contraseña coincide con su hash usando BCrypt
    /// </summary>
    /// <param name="password">Contraseña en texto plano</param>
    /// <param name="hashedPassword">Hash de la contraseña</param>
    /// <returns>True si la contraseña coincide, False en caso contrario</returns>
    public bool VerifyPassword(string password, string hashedPassword)
    {
        if (string.IsNullOrWhiteSpace(password))
            return false;

        if (string.IsNullOrWhiteSpace(hashedPassword))
            return false;

        try
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
        catch (SaltParseException)
        {
            // Si el hash no es válido (formato incorrecto), retornar false
            return false;
        }
    }

    /// <summary>
    ///     Genera un salt aleatorio usando BCrypt
    /// </summary>
    /// <returns>Salt en formato string</returns>
    public string GenerateSalt()
    {
        return BCrypt.Net.BCrypt.GenerateSalt(12);
    }
}