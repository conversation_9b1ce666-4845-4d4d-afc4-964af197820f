# Entidad Item - Ejemplo de SimpleEntity Sin Propiedades Adicionales

## Descripción

La entidad `Item` es un ejemplo perfecto de cómo usar el sistema `SimpleEntity` sin agregar propiedades adicionales.
Esta entidad hereda completamente de `SimpleEntity` y obtiene toda la funcionalidad CRUD automáticamente.

## Estructura

### Entidad

```csharp
public class Item : SimpleEntity
{
    // No se agregan propiedades adicionales
    // Solo hereda Name, Description y todas las propiedades de BaseEntity
}
```

### Propiedades Heredadas

#### De SimpleEntity:

- `Name` (string, required, max 200 caracteres)
- `Description` (string?, optional, max 1000 caracteres)

#### De BaseEntity:

- `Id` (int, primary key, auto-increment)
- `Created` (DateTime, fecha de creación)
- `CreatedBy` (string, usuario que creó)
- `Updated` (DateTime?, fecha de última actualización)
- `UpdatedBy` (string?, usuario que actualizó)
- `IsDeleted` (bool, soft delete)
- `Deleted` (DateTime?, fecha de eliminación)
- `DeletedBy` (string?, usuario que eliminó)
- `Active` (bool, estado activo/inactivo)

## DTOs

### ItemDto

```csharp
public class ItemDto : SimpleEntityDto
{
    // No se agregan propiedades adicionales
    // Solo hereda todas las propiedades de SimpleEntityDto
}
```

### CreateItemDto

```csharp
public class CreateItemDto : CreateSimpleEntityDto
{
    // No se agregan propiedades adicionales
    // Solo hereda Name y Description con sus validaciones base
}
```

### UpdateItemDto

```csharp
public class UpdateItemDto : UpdateSimpleEntityDto
{
    // No se agregan propiedades adicionales
    // Solo hereda Name y Description con sus validaciones base
}
```

### ItemFilterDto

```csharp
public class ItemFilterDto : SimpleEntityFilterDto
{
    // No se agregan propiedades adicionales
    // Solo hereda Name, Description, Active, IncludeDeleted, PageNumber y PageSize
}
```

## Funcionalidades Automáticas

Al heredar completamente de `SimpleEntity`, la entidad `Item` obtiene automáticamente:

### ✅ Operaciones CRUD Completas

- **Crear**: `POST /api/item`
- **Leer por ID**: `GET /api/item/{id}`
- **Leer todos**: `GET /api/item`
- **Actualizar**: `PUT /api/item/{id}`
- **Eliminar (soft)**: `DELETE /api/item/{id}`
- **Eliminar (hard)**: `DELETE /api/item/{id}/permanent`

### ✅ Operaciones de Búsqueda

- **Por nombre**: `GET /api/item/search/name?name=valor`
- **Por descripción**: `GET /api/item/search/description?description=valor`
- **Paginación**: `GET /api/item/paged?pageNumber=1&pageSize=10`
- **Activos**: `GET /api/item/active`

### ✅ Operaciones de Estado

- **Activar**: `PATCH /api/item/{id}/activate`
- **Desactivar**: `PATCH /api/item/{id}/deactivate`
- **Verificar existencia**: `GET /api/item/{id}/exists`

### ✅ Validaciones Automáticas

- Nombre requerido
- Nombre único (no duplicados)
- Longitud máxima de nombre (200 caracteres)
- Longitud máxima de descripción (1000 caracteres)

### ✅ Auditoría Automática

- Fecha y usuario de creación
- Fecha y usuario de actualización
- Fecha y usuario de eliminación
- Soft delete automático

### ✅ Logging Completo

- Logging de todas las operaciones
- Logging de errores
- Logging de consultas SQL (en DEBUG)

## Implementación Mínima

### Repositorio

```csharp
public class ItemRepository : SimpleEntityRepository<Item>, IItemRepository
{
    // No se agregan métodos adicionales
    // Solo hereda toda la funcionalidad base
}
```

### Servicio

```csharp
public class ItemService : SimpleEntityService<Item, ItemDto, CreateItemDto, UpdateItemDto, ItemFilterDto>, IItemService
{
    // No se agregan métodos adicionales
    // Solo hereda toda la funcionalidad base
}
```

### Controlador

```csharp
public class ItemController : SimpleEntityController<Item, ItemDto, CreateItemDto, UpdateItemDto, ItemFilterDto>
{
    // No se agregan métodos adicionales
    // Solo hereda todos los endpoints base
}
```

### Configuración EF Core

```csharp
public class ItemConfiguration : SimpleEntityConfiguration<Item>
{
    public ItemConfiguration() : base("Items") { }
    
    public override void Configure(EntityTypeBuilder<Item> builder)
    {
        base.Configure(builder);
        // No se agregan configuraciones adicionales
    }
}
```

## Endpoints Disponibles

| Método | Endpoint                       | Descripción             |
|--------|--------------------------------|-------------------------|
| GET    | `/api/item`                    | Obtener todos los items |
| GET    | `/api/item/{id}`               | Obtener item por ID     |
| GET    | `/api/item/paged`              | Obtener items paginados |
| GET    | `/api/item/active`             | Obtener items activos   |
| GET    | `/api/item/search/name`        | Buscar por nombre       |
| GET    | `/api/item/search/description` | Buscar por descripción  |
| POST   | `/api/item`                    | Crear nuevo item        |
| PUT    | `/api/item/{id}`               | Actualizar item         |
| DELETE | `/api/item/{id}`               | Eliminar item (soft)    |
| DELETE | `/api/item/{id}/permanent`     | Eliminar item (hard)    |
| PATCH  | `/api/item/{id}/activate`      | Activar item            |
| PATCH  | `/api/item/{id}/deactivate`    | Desactivar item         |
| GET    | `/api/item/{id}/exists`        | Verificar existencia    |

## Ejemplo de Uso

### Crear un Item

```http
POST /api/item
Content-Type: application/json

{
  "name": "Documento Importante",
  "description": "Documento importante del sistema"
}
```

### Obtener Items Paginados

```http
GET /api/item/paged?pageNumber=1&pageSize=10&name=documento&active=true
```

### Buscar por Nombre

```http
GET /api/item/search/name?name=importante
```

## Ventajas de este Enfoque

1. **Código Mínimo**: Solo necesitas crear la entidad y heredar
2. **Funcionalidad Completa**: Obtienes CRUD completo automáticamente
3. **Consistencia**: Sigue el mismo patrón que otras entidades
4. **Mantenibilidad**: Cambios en la base se reflejan automáticamente
5. **Escalabilidad**: Fácil agregar funcionalidad específica después si es necesario

## Cuándo Usar este Patrón

Usa este patrón cuando:

- Tu entidad solo necesita `Name` y `Description`
- No necesitas propiedades adicionales específicas
- Quieres funcionalidad CRUD completa sin escribir código
- Necesitas consistencia con otras entidades del sistema

## Migración

Para crear la tabla en la base de datos:

```bash
dotnet ef migrations add AddItems
dotnet ef database update
```

## Pruebas

Usa el archivo `Item.http` para probar todos los endpoints disponibles.

---

**Nota**: Esta es la implementación más simple posible del sistema `SimpleEntity`. Si en el futuro necesitas agregar
propiedades específicas, puedes extender la entidad y los DTOs sin perder la funcionalidad base. 