using System.Net.Http.Headers;

namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Services;

/// <summary>
///     Implementación del servicio de OpenID Connect
/// </summary>
public class OpenIdConnectService : IOpenIdConnectService
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    private readonly string _authority;
    private readonly string _clientId;
    private readonly string _clientSecret;
    private readonly HttpClient _httpClient;
    private readonly string _redirectUri;

    public OpenIdConnectService(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _clientId = configuration["OpenIdConnect:ClientId"] ??
                    throw new ArgumentNullException("ClientId no configurado");
        _clientSecret = configuration["OpenIdConnect:ClientSecret"] ??
                        throw new ArgumentNullException("ClientSecret no configurado");
        _authority = configuration["OpenIdConnect:Authority"] ?? "https://login-qa.kochid.com";
        _redirectUri = configuration["OpenIdConnect:RedirectUri"] ??
                       throw new ArgumentNullException("RedirectUri no configurado");

        Logger.Debug("OpenIdConnectService inicializado con Authority: {Authority}", _authority);
    }

    /// <summary>
    ///     Obtiene la URL de autorización para iniciar el flujo de autenticación
    /// </summary>
    public string GetAuthorizationUrl(string state, string scope = "openid profile email")
    {
        Logger.Debug("Generando URL de autorización para estado: {State}", state);

        var queryParams = new Dictionary<string, string>
        {
            ["client_id"] = _clientId,
            ["response_type"] = "code",
            ["scope"] = scope,
            ["redirect_uri"] = _redirectUri,
            ["state"] = state
        };

        var queryString = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
        var authUrl = $"https://login-qa.kochid.com/as/authorize?{queryString}";

        Logger.Debug("URL de autorización generada: {AuthUrl}", authUrl);
        return authUrl;
    }

    /// <summary>
    ///     Intercambia el código de autorización por un token de acceso
    ///     Siguiendo la especificación OAuth 2.0 RFC6749 Section 4.1.3
    /// </summary>
    public async Task<TokenResponse?> ExchangeCodeForTokenAsync(string authorizationCode)
    {
        Logger.Debug("Intercambiando código de autorización por token");

        try
        {
            // Configurar autenticación Basic para cliente confidencial
            var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_clientId}:{_clientSecret}"));
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Basic", credentials);

            // Parámetros según RFC6749 Section 4.1.3
            var tokenRequest = new Dictionary<string, string>
            {
                ["grant_type"] = "authorization_code", // REQUIRED - debe ser "authorization_code"
                ["code"] = authorizationCode, // REQUIRED - código de autorización recibido
                ["redirect_uri"] = _redirectUri // REQUIRED - debe ser idéntico al usado en la solicitud de autorización
            };

            var content = new FormUrlEncodedContent(tokenRequest);

            // Configurar Content-Type correcto
            content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

            Logger.Debug("Enviando solicitud de token a: https://login-qa.kochid.com/as/token");
            var response = await _httpClient.PostAsync("https://login-qa.kochid.com/as/token", content);

            // Limpiar headers para futuras solicitudes
            _httpClient.DefaultRequestHeaders.Authorization = null;

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                Logger.Debug("Respuesta del servidor de tokens: {Response}", responseContent);

                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                Logger.Info("Token obtenido exitosamente");
                return tokenResponse;
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            Logger.Error("Error al obtener token. Status: {Status}, Content: {Content}",
                response.StatusCode, errorContent);
            return null;
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al intercambiar código por token");
            return null;
        }
    }

    /// <summary>
    ///     Obtiene información del usuario usando el token de acceso
    /// </summary>
    public async Task<UserInfo?> GetUserInfoAsync(string accessToken)
    {
        Logger.Debug("Obteniendo información del usuario");

        try
        {
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.GetAsync("https://login-qa.kochid.com/as/userinfo");

            // Limpiar headers para futuras solicitudes
            _httpClient.DefaultRequestHeaders.Authorization = null;

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                Logger.Debug("Respuesta del servidor userinfo: {Response}", responseContent);

                var userInfo = JsonSerializer.Deserialize<UserInfo>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                Logger.Info("Información del usuario obtenida exitosamente: {Email}", userInfo?.PreferedUserName);
                return userInfo;
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            Logger.Error("Error al obtener información del usuario. Status: {Status}, Content: {Content}",
                response.StatusCode, errorContent);
            return null;
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener información del usuario");
            return null;
        }
    }

    /// <summary>
    ///     Refresca el token de acceso usando el refresh token
    /// </summary>
    public async Task<TokenResponse?> RefreshTokenAsync(string refreshToken)
    {
        Logger.Debug("Refrescando token de acceso");

        try
        {
            // Configurar autenticación Basic para cliente confidencial
            var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_clientId}:{_clientSecret}"));
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Basic", credentials);

            var tokenRequest = new Dictionary<string, string>
            {
                ["grant_type"] = "refresh_token",
                ["refresh_token"] = refreshToken
            };

            var content = new FormUrlEncodedContent(tokenRequest);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await _httpClient.PostAsync("https://login-qa.kochid.com/as/token", content);

            // Limpiar headers para futuras solicitudes
            _httpClient.DefaultRequestHeaders.Authorization = null;

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                Logger.Debug("Respuesta del servidor de refresh token: {Response}", responseContent);

                var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                Logger.Info("Token refrescado exitosamente");
                return tokenResponse;
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            Logger.Error("Error al refrescar token. Status: {Status}, Content: {Content}",
                response.StatusCode, errorContent);
            return null;
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al refrescar token");
            return null;
        }
    }

    /// <summary>
    ///     Revoca el token de acceso
    /// </summary>
    public async Task<bool> RevokeTokenAsync(string token, string tokenTypeHint = "access_token")
    {
        Logger.Debug("Revocando token de acceso");

        try
        {
            // Configurar autenticación Basic para cliente confidencial
            var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_clientId}:{_clientSecret}"));
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Basic", credentials);

            var revokeRequest = new Dictionary<string, string>
            {
                ["token"] = token,
                ["token_type_hint"] = tokenTypeHint
            };

            var content = new FormUrlEncodedContent(revokeRequest);
            content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");

            var response = await _httpClient.PostAsync("https://login-qa.kochid.com/as/revoke", content);

            // Limpiar headers para futuras solicitudes
            _httpClient.DefaultRequestHeaders.Authorization = null;

            if (response.IsSuccessStatusCode)
            {
                Logger.Info("Token revocado exitosamente");
                return true;
            }

            var errorContent = await response.Content.ReadAsStringAsync();
            Logger.Error("Error al revocar token. Status: {Status}, Content: {Content}",
                response.StatusCode, errorContent);
            return false;
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al revocar token");
            return false;
        }
    }
}