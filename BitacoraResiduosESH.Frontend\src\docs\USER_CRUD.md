# CRUD de Usuarios - Documentación

## Descripción General

El CRUD de Usuarios es una interfaz completa para gestionar usuarios del sistema, incluyendo la asignación de roles.
Está construido con React, TypeScript, TailwindCSS y TanStack Query.

## Características Principales

### ✅ Funcionalidades Implementadas

- **Listado paginado** de usuarios con búsqueda por nombre
- **Creación** de nuevos usuarios con validación de campos
- **Edición** de usuarios existentes
- **Eliminación** (soft delete) de usuarios
- **Activación/Desactivación** de usuarios
- **Visualización detallada** de información del usuario
- **Selección de roles** desde una lista desplegable
- **Validación de formularios** en tiempo real
- **Búsqueda fuzzy** por nombre de usuario
- **Gestión de estados** (activo/inactivo, eliminado)
- **Integración con roles** existentes

### 🎯 Campos del Usuario

- **Nombre**: Campo obligatorio (2-200 caracteres)
- **Email**: Campo obligatorio con validación de formato
- **Número de Empleado**: Campo obligatorio (mínimo 3 caracteres)
- **Rol**: Selección obligatoria desde lista de roles activos
- **Estado**: Checkbox para activar/desactivar usuario

## Estructura de Archivos

```
src/
├── types/
│   └── user.ts                 # Tipos TypeScript para User
├── services/
│   └── userService.ts          # Servicio API para User
├── hooks/
│   └── useUserCrud.ts          # Hooks personalizados para User
├── components/
│   └── table/
│       ├── UserTable.tsx       # Tabla principal de usuarios
│       ├── UserForm.tsx        # Formulario de creación/edición
│       └── UserDetails.tsx     # Vista detallada del usuario
└── pages/
    └── Users.tsx               # Página principal de usuarios
```

## Componentes Principales

### UserTable

- Tabla principal con paginación
- Búsqueda por nombre
- Acciones: Ver, Editar, Activar/Desactivar, Eliminar
- Integración con modales

### UserForm

- Formulario reutilizable para crear y editar
- Validación en tiempo real
- Integración con roles existentes
- Manejo de estados de carga

### UserDetails

- Vista detallada del usuario
- Información del rol asignado
- Historial de cambios
- Estados visuales

## Integración con Backend

### Endpoints Utilizados

- `GET /api/User` - Listar usuarios paginados
- `POST /api/User` - Crear usuario
- `PUT /api/User/{id}` - Actualizar usuario
- `DELETE /api/User/{id}` - Eliminar usuario
- `PUT /api/User/{id}/activate` - Activar usuario
- `PUT /api/User/{id}/deactivate` - Desactivar usuario
- `GET /api/User/search/name` - Búsqueda por nombre
- `GET /api/Role/active` - Obtener roles activos

### Relación con Roles

El sistema requiere que cada usuario tenga un rol asignado. Los roles se obtienen desde el endpoint de roles activos y
se muestran en un selector desplegable.

## Uso del Sistema

### 1. Acceso

- Navegar a `/users` desde el sidebar
- Se encuentra en la sección "Empleados > Usuarios"

### 2. Crear Usuario

1. Hacer clic en "Crear Usuario"
2. Llenar todos los campos obligatorios
3. Seleccionar un rol de la lista
4. Hacer clic en "Crear Usuario"

### 3. Editar Usuario

1. Hacer clic en "Editar" en la fila del usuario
2. Modificar los campos necesarios
3. Hacer clic en "Guardar Cambios"

### 4. Gestionar Estado

- **Activar/Desactivar**: Botón en la fila del usuario
- **Eliminar**: Botón "Eliminar" (soft delete)

### 5. Ver Detalles

- Hacer clic en "Ver" para ver información completa
- Incluye información del rol asignado

## Validaciones

### Frontend

- **Nombre**: Requerido, 2-200 caracteres
- **Email**: Requerido, formato válido
- **Número de Empleado**: Requerido, mínimo 3 caracteres
- **Rol**: Requerido, debe seleccionarse uno

### Backend

- Validación de existencia de rol
- Validación de email único
- Validación de número de empleado único

## Estados y Feedback

### Estados de Carga

- Indicadores de carga en botones
- Skeleton loaders en tabla
- Mensajes de progreso

### Notificaciones

- Toast notifications para éxito/error
- Mensajes específicos por operación
- Feedback inmediato al usuario

## Responsive Design

- Tabla responsive con scroll horizontal
- Modales adaptables a diferentes tamaños
- Formularios optimizados para móvil
- Navegación táctil friendly

## Integración con el Sistema

### Query Client

- Cache automático de datos
- Invalidación inteligente
- Optimistic updates

### Router

- Navegación programática
- Estado de URL preservado
- Breadcrumbs automáticos

## Próximas Mejoras

### Funcionalidades Planificadas

- [ ] Filtros avanzados (por rol, estado, fecha)
- [ ] Exportación a Excel/PDF
- [ ] Importación masiva de usuarios
- [ ] Historial de cambios detallado
- [ ] Permisos granulares por usuario

### Mejoras de UX

- [ ] Drag & drop para reordenar
- [ ] Selección múltiple para acciones en lote
- [ ] Búsqueda avanzada con filtros
- [ ] Vista de calendario para eventos

## Troubleshooting

### Problemas Comunes

1. **Error al cargar roles**
    - Verificar que el servicio de roles esté funcionando
    - Revisar la conexión con el backend

2. **Validación de email falla**
    - Verificar formato del email
    - Comprobar que no exista otro usuario con el mismo email

3. **No se puede asignar rol**
    - Verificar que existan roles activos
    - Comprobar permisos del usuario actual

### Logs y Debugging

- Usar DevTools para ver requests/responses
- Revisar console para errores de JavaScript
- Verificar Network tab para problemas de API

## Dependencias

### Principales

- React 18+
- TypeScript 5+
- TanStack Query 5+
- TailwindCSS 3+
- Lucide React (iconos)

### Desarrollo

- Vite
- ESLint
- Prettier

## Contribución

Para contribuir al CRUD de Usuarios:

1. Seguir las convenciones de código existentes
2. Agregar tests para nuevas funcionalidades
3. Documentar cambios en este archivo
4. Verificar que no se rompan funcionalidades existentes 