namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth;

/// <summary>
///     DTO para autenticación local con email/número de empleado y contraseña
/// </summary>
public class LoginDto
{
    /// <summary>
    ///     Email del usuario (opcional si se proporciona EmployeeNumber)
    /// </summary>
    [EmailAddress(ErrorMessage = "El formato del email no es válido")]
    public string? Email { get; set; }

    /// <summary>
    ///     Número de empleado del usuario (opcional si se proporciona Email)
    /// </summary>
    public string? EmployeeNumber { get; set; }

    /// <summary>
    ///     Contraseña del usuario
    /// </summary>
    [Required(ErrorMessage = "La contraseña es requerida")]
    [MinLength(6, ErrorMessage = "La contraseña debe tener al menos 6 caracteres")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    ///     Valida que se proporcione al menos un identificador (email o número de empleado)
    /// </summary>
    /// <returns>True si es válido, False en caso contrario</returns>
    public bool IsValid()
    {
        return (!string.IsNullOrWhiteSpace(Email) || !string.IsNullOrWhiteSpace(EmployeeNumber))
               && !string.IsNullOrWhiteSpace(Password);
    }
}