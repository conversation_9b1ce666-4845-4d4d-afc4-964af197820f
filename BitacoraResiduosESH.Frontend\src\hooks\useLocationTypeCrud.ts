import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import {locationTypeService} from '../services/locationTypeService';
import type {CreateLocationTypeDto, UpdateLocationTypeDto} from '../types/locationType';

// Query keys para TanStack Query
export const locationTypeQueryKeys = {
    all: ['locationtypes'] as const,
    lists: () => [...locationTypeQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...locationTypeQueryKeys.lists(), filters] as const,
    details: () => [...locationTypeQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...locationTypeQueryKeys.details(), id] as const,
};

export const useLocationTypeCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todos los tipos de ubicación
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: locationTypeQueryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => locationTypeService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener tipos de ubicación activos
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: locationTypeQueryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => locationTypeService.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener un tipo de ubicación por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: locationTypeQueryKeys.detail(id),
            queryFn: () => locationTypeService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar tipos de ubicación por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: locationTypeQueryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => locationTypeService.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar tipos de ubicación por descripción
    const useSearchByDescription = (
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: locationTypeQueryKeys.list({search: 'description', description, pageNumber, pageSize, includeDeleted}),
            queryFn: () => locationTypeService.searchByDescription(description, pageNumber, pageSize, includeDeleted),
            enabled: !!description.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear un tipo de ubicación
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateLocationTypeDto) => locationTypeService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: locationTypeQueryKeys.lists()});
                toast.success('Tipo de ubicación creado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el tipo de ubicación: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar un tipo de ubicación
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: UpdateLocationTypeDto }) =>
                locationTypeService.update(id, data),
            onSuccess: (updatedLocationType) => {
                queryClient.invalidateQueries({queryKey: locationTypeQueryKeys.lists()});
                queryClient.setQueryData(locationTypeQueryKeys.detail(updatedLocationType.id), updatedLocationType);
                toast.success('Tipo de ubicación actualizado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el tipo de ubicación: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar un tipo de ubicación
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => locationTypeService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: locationTypeQueryKeys.lists()});
                queryClient.removeQueries({queryKey: locationTypeQueryKeys.detail(deletedId)});
                toast.success('Tipo de ubicación eliminado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el tipo de ubicación: ${error.message}`);
            },
        });
    };

    // Mutation para activar un tipo de ubicación
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => locationTypeService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: locationTypeQueryKeys.lists()});
                toast.success('Tipo de ubicación activado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el tipo de ubicación: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar un tipo de ubicación
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => locationTypeService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: locationTypeQueryKeys.lists()});
                toast.success('Tipo de ubicación desactivado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el tipo de ubicación: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: locationTypeQueryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByDescription,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
}; 