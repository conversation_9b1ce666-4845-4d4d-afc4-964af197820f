import React from 'react'
import {useThemeStore} from '../stores/themeStore'

interface ThemeToggleProps {
    className?: string
    size?: 'sm' | 'md' | 'lg'
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
                                                     className = '',
                                                     size = 'md'
                                                 }) => {
    const {theme, toggleTheme} = useThemeStore()

    const sizeClasses = {
        sm: 'w-8 h-8',
        md: 'w-10 h-10',
        lg: 'w-12 h-12'
    }

    const iconSizes = {
        sm: 'w-4 h-4',
        md: 'w-5 h-5',
        lg: 'w-6 h-6'
    }

    return (
        <button
            onClick={toggleTheme}
            className={`
        ${sizeClasses[size]}
        rounded-lg
        bg-gray-100 
        dark:bg-gray-800 
        hover:bg-gray-200 
        dark:hover:bg-gray-700
        text-gray-600 
        dark:text-gray-300
        hover:text-gray-900 
        dark:hover:text-white
        transition-all 
        duration-200 
        focus:outline-none 
        focus:ring-2 
        focus:ring-blue-500 
        focus:ring-offset-2 
        dark:focus:ring-offset-gray-800
        ${className}
      `}
            aria-label={`Cambiar a tema ${theme === 'light' ? 'oscuro' : 'claro'}`}
            title={`Cambiar a tema ${theme === 'light' ? 'oscuro' : 'claro'}`}
        >
            {theme === 'light' ? (
                // Icono de luna para modo oscuro
                <svg
                    className={`${iconSizes[size]} mx-auto`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                    />
                </svg>
            ) : (
                // Icono de sol para modo claro
                <svg
                    className={`${iconSizes[size]} mx-auto`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                </svg>
            )}
        </button>
    )
}

export default ThemeToggle 