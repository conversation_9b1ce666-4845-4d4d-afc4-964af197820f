namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    private readonly IAuthService _authService;
    private readonly IOpenIdConnectService _openIdService;

    public AuthController(IOpenIdConnectService openIdService, IAuthService authService)
    {
        _openIdService = openIdService;
        _authService = authService;
    }

    /// <summary>
    ///     Autenticación local con email/número de empleado y contraseña
    /// </summary>
    /// <param name="loginDto">Datos de login</param>
    /// <returns>Token JWT si la autenticación es exitosa</returns>
    [HttpPost("login")]
    [ProducesResponseType(typeof(AuthResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AuthResponseDto>> Login([FromBody] LoginDto loginDto)
    {
        Logger.Debug("Intento de login local - Email: {Email}, EmployeeNumber: {EmployeeNumber}",
            loginDto.Email, loginDto.EmployeeNumber);

        try
        {
            // Validar que se proporcione al menos un identificador
            if (!loginDto.IsValid())
            {
                Logger.Warn("Intento de login con datos inválidos - Email: {Email}, EmployeeNumber: {EmployeeNumber}",
                    loginDto.Email, loginDto.EmployeeNumber);
                return BadRequest(new { error = "Debe proporcionar email o número de empleado, y contraseña" });
            }

            // Autenticar usuario (puede ser por email o número de empleado)
            var token = await _authService.AuthenticateAsync(loginDto.Email, loginDto.EmployeeNumber,
                loginDto.Password);
            if (token == null)
            {
                Logger.Warn("Login fallido - Email: {Email}, EmployeeNumber: {EmployeeNumber}",
                    loginDto.Email, loginDto.EmployeeNumber);
                return Unauthorized(new { error = "Credenciales inválidas" });
            }

            // Obtener información del usuario
            var user = await _authService.GetUserFromTokenAsync(token);
            if (user == null)
            {
                Logger.Error(
                    "No se pudo obtener información del usuario después del login - Email: {Email}, EmployeeNumber: {EmployeeNumber}",
                    loginDto.Email, loginDto.EmployeeNumber);
                return Unauthorized(new { error = "Error al obtener información del usuario" });
            }

            var response = new AuthResponseDto
            {
                Token = token,
                TokenType = "Bearer",
                ExpiresIn = 60, // Configurar según la configuración
                User = new UserAuthInfoDto
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email ?? "",
                    EmployeeNumber = user.EmployeeNumber ?? "",
                    Role = user.Role?.Name ?? "User"
                },
                Message = "Autenticación exitosa"
            };

            Logger.Info("Login exitoso para usuario: {Email}/{EmployeeNumber}", user.Email, user.EmployeeNumber);
            return Ok(response);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error durante el login local - Email: {Email}, EmployeeNumber: {EmployeeNumber}",
                loginDto.Email, loginDto.EmployeeNumber);
            return BadRequest(new { error = "Error interno durante la autenticación" });
        }
    }

    /// <summary>
    ///     Refresca el token JWT
    /// </summary>
    /// <param name="request">Solicitud con el token actual</param>
    /// <returns>Nuevo token JWT</returns>
    [HttpPost("refresh")]
    [ProducesResponseType(typeof(AuthResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<AuthResponseDto>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        Logger.Debug("Solicitud de refresh token");

        try
        {
            if (string.IsNullOrEmpty(request.RefreshToken))
            {
                Logger.Warn("Refresh token no proporcionado");
                return BadRequest(new { error = "Refresh token es requerido" });
            }

            var newToken = await _authService.RefreshTokenAsync(request.RefreshToken);
            if (newToken == null)
            {
                Logger.Error("No se pudo refrescar el token");
                return Unauthorized(new { error = "Token inválido o expirado" });
            }

            // Obtener información del usuario
            var user = await _authService.GetUserFromTokenAsync(newToken);
            if (user == null)
            {
                Logger.Error("No se pudo obtener información del usuario después del refresh");
                return Unauthorized(new { error = "Error al obtener información del usuario" });
            }

            var response = new AuthResponseDto
            {
                Token = newToken,
                TokenType = "Bearer",
                ExpiresIn = 60,
                User = new UserAuthInfoDto
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email ?? "",
                    EmployeeNumber = user.EmployeeNumber ?? "",
                    Role = user.Role?.Name ?? "User"
                },
                Message = "Token refrescado exitosamente"
            };

            Logger.Info("Token refrescado exitosamente para usuario: {Email}", user.Email);
            return Ok(response);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al refrescar token");
            return BadRequest(new { error = "Error al refrescar el token" });
        }
    }

    /// <summary>
    ///     Valida un token JWT
    /// </summary>
    /// <param name="request">Solicitud con el token a validar</param>
    /// <returns>Información del usuario si el token es válido</returns>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(UserAuthInfoDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<UserAuthInfoDto>> ValidateToken([FromBody] ValidateTokenRequest request)
    {
        Logger.Debug("Validación de token JWT");

        try
        {
            if (string.IsNullOrEmpty(request.Token))
            {
                Logger.Warn("Token no proporcionado para validación");
                return Unauthorized(new { error = "Token es requerido" });
            }

            var user = await _authService.GetUserFromTokenAsync(request.Token);
            if (user == null)
            {
                Logger.Warn("Token inválido o expirado");
                return Unauthorized(new { error = "Token inválido o expirado" });
            }

            var userInfo = new UserAuthInfoDto
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email ?? "",
                EmployeeNumber = user.EmployeeNumber ?? "",
                Role = user.Role?.Name ?? "User"
            };

            Logger.Info("Token validado exitosamente para usuario: {Email}", user.Email);
            return Ok(userInfo);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al validar token");
            return Unauthorized(new { error = "Error al validar el token" });
        }
    }

    /// <summary>
    ///     Inicia el flujo de autenticación OAuth2/OpenID Connect
    /// </summary>
    /// <param name="state">Estado opcional para mantener el contexto</param>
    /// <param name="scope">Scope de permisos solicitados</param>
    /// <returns>URL de autorización para redirigir al usuario</returns>
    [HttpGet("oidc/login")]
    [ProducesResponseType(typeof(AuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public ActionResult<AuthResponse> OpenIdLogin([FromQuery] string? state = null,
        [FromQuery] string scope = "openid profile email")
    {
        Logger.Debug("Solicitud de inicio de sesión con OpenID Connect. State: {State}, Scope: {Scope}", state, scope);

        try
        {
            // Generar un estado aleatorio si no se proporciona
            var authState = state ?? Guid.NewGuid().ToString();

            var authUrl = _openIdService.GetAuthorizationUrl(authState, scope);

            Logger.Info("URL de autorización generada exitosamente para estado: {State}", authState);

            return Ok(new AuthResponse
            {
                AuthorizationUrl = authUrl,
                State = authState,
                Message = "Redirige al usuario a la URL de autorización"
            });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al generar URL de autorización");
            return BadRequest(new { error = "Error al iniciar el proceso de autenticación" });
        }
    }

    /// <summary>
    ///     Callback para procesar la respuesta de autorización
    /// </summary>
    /// <param name="code">Código de autorización</param>
    /// <param name="state">Estado devuelto por el proveedor</param>
    /// <param name="error">Error si ocurrió alguno</param>
    /// <returns>Información del usuario autenticado</returns>
    [HttpGet("oidc/callback")]
    [ProducesResponseType(typeof(UserAuthResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<UserAuthResponse>> OpenIdCallback(
        [FromQuery] string? code = null,
        [FromQuery] string? state = null,
        [FromQuery] string? error = null)
    {
        Logger.Debug("Callback recibido. Code: {HasCode}, State: {State}, Error: {Error}",
            !string.IsNullOrEmpty(code), state, error);

        try
        {
            // Verificar si hay un error
            if (!string.IsNullOrEmpty(error))
            {
                Logger.Warn("Error en callback de autenticación: {Error}", error);
                return BadRequest(new { error = $"Error de autenticación: {error}" });
            }

            // Verificar que se recibió el código de autorización
            if (string.IsNullOrEmpty(code))
            {
                Logger.Warn("Callback recibido sin código de autorización");
                return BadRequest(new { error = "Código de autorización no recibido" });
            }

            // Intercambiar el código por un token
            var tokenResponse = await _openIdService.ExchangeCodeForTokenAsync(code);
            if (tokenResponse == null)
            {
                Logger.Error("No se pudo obtener el token de acceso");
                return Unauthorized(new { error = "No se pudo obtener el token de acceso" });
            }

            // Obtener información del usuario
            var userInfo = await _openIdService.GetUserInfoAsync(tokenResponse.AccessToken);
            if (userInfo == null)
            {
                Logger.Error("No se pudo obtener la información del usuario");
                return Unauthorized(new { error = "No se pudo obtener la información del usuario" });
            }

            Logger.Info("Usuario autenticado exitosamente: {Email}", userInfo.PreferedUserName);

            return Ok(new UserAuthResponse
            {
                User = userInfo,
                AccessToken = tokenResponse.AccessToken,
                TokenType = tokenResponse.TokenType,
                ExpiresIn = tokenResponse.ExpiresIn,
                RefreshToken = tokenResponse.RefreshToken,
                Scope = tokenResponse.Scope,
                Message = "Autenticación exitosa"
            });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error en el callback de autenticación");
            return BadRequest(new { error = "Error interno en el proceso de autenticación" });
        }
    }

    /// <summary>
    ///     Refresca el token de acceso OpenID Connect
    /// </summary>
    /// <param name="request">Solicitud con el refresh token</param>
    /// <returns>Nuevo token de acceso</returns>
    [HttpPost("oidc/refresh")]
    [ProducesResponseType(typeof(TokenResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<TokenResponse>> OpenIdRefreshToken([FromBody] RefreshTokenRequest request)
    {
        Logger.Debug("Solicitud de refresh token OpenID Connect");

        try
        {
            if (string.IsNullOrEmpty(request.RefreshToken))
            {
                Logger.Warn("Refresh token no proporcionado");
                return BadRequest(new { error = "Refresh token es requerido" });
            }

            var tokenResponse = await _openIdService.RefreshTokenAsync(request.RefreshToken);
            if (tokenResponse == null)
            {
                Logger.Error("No se pudo refrescar el token");
                return Unauthorized(new { error = "No se pudo refrescar el token" });
            }

            Logger.Info("Token refrescado exitosamente");
            return Ok(tokenResponse);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al refrescar token");
            return BadRequest(new { error = "Error al refrescar el token" });
        }
    }

    /// <summary>
    ///     Revoca el token de acceso
    /// </summary>
    /// <param name="request">Solicitud con el token a revocar</param>
    /// <returns>Resultado de la revocación</returns>
    [HttpPost("revoke")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> RevokeToken([FromBody] RevokeTokenRequest request)
    {
        Logger.Debug("Solicitud de revocación de token");

        try
        {
            if (string.IsNullOrEmpty(request.Token))
            {
                Logger.Warn("Token no proporcionado para revocación");
                return BadRequest(new { error = "Token es requerido" });
            }

            var success = await _openIdService.RevokeTokenAsync(request.Token, request.TokenTypeHint);
            if (!success)
            {
                Logger.Error("No se pudo revocar el token");
                return BadRequest(new { error = "No se pudo revocar el token" });
            }

            Logger.Info("Token revocado exitosamente");
            return Ok(new { message = "Token revocado exitosamente" });
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al revocar token");
            return BadRequest(new { error = "Error al revocar el token" });
        }
    }

    /// <summary>
    ///     Obtiene información del usuario usando el token de acceso
    /// </summary>
    /// <param name="authorization">Header de autorización con el token Bearer</param>
    /// <returns>Información del usuario</returns>
    [HttpGet("userinfo")]
    [ProducesResponseType(typeof(UserInfo), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<UserInfo>> GetUserInfo([FromHeader(Name = "Authorization")] string? authorization)
    {
        Logger.Debug("Solicitud de información del usuario");

        try
        {
            if (string.IsNullOrEmpty(authorization) || !authorization.StartsWith("Bearer "))
            {
                Logger.Warn("Token de autorización no proporcionado o inválido");
                return Unauthorized(new { error = "Token de autorización requerido" });
            }

            var token = authorization.Substring("Bearer ".Length);
            var userInfo = await _openIdService.GetUserInfoAsync(token);

            if (userInfo == null)
            {
                Logger.Error("No se pudo obtener la información del usuario");
                return Unauthorized(new { error = "Token inválido o expirado" });
            }

            Logger.Info("Información del usuario obtenida exitosamente: {Email}", userInfo.PreferedUserName);
            return Ok(userInfo);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener información del usuario");
            return Unauthorized(new { error = "Error al obtener información del usuario" });
        }
    }
}

// DTOs para las respuestas de la API
public class AuthResponse
{
    public string AuthorizationUrl { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}

public class UserAuthResponse
{
    public UserInfo User { get; set; } = new();
    public string AccessToken { get; set; } = string.Empty;
    public string TokenType { get; set; } = string.Empty;
    public int ExpiresIn { get; set; }
    public string? RefreshToken { get; set; }
    public string Scope { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}

public class RefreshTokenRequest
{
    public string RefreshToken { get; set; } = string.Empty;
}

public class ValidateTokenRequest
{
    public string Token { get; set; } = string.Empty;
}

public class RevokeTokenRequest
{
    public string Token { get; set; } = string.Empty;
    public string TokenTypeHint { get; set; } = "access_token";
}