# Sistema de Autenticación

## Descripción General

El sistema de autenticación implementa dos mecanismos de autenticación:

1. **Autenticación Local con JWT**: Sistema interno que usa email/número de empleado y contraseña
2. **OpenID Connect**: Sistema externo para integración con proveedores de identidad

## Autenticación Local con JWT

### Características

- **Hash de Contraseñas**: Usa BCrypt para hashear contraseñas con salt automático
- **Tokens JWT**: Genera tokens seguros con claims de usuario
- **Validación**: Verifica tokens y extrae información del usuario
- **Refresh**: Permite renovar tokens expirados
- **Múltiples Identificadores**: Soporta login con email o número de empleado

### Endpoints

#### POST /api/auth/login

Autentica un usuario con email/número de empleado y contraseña.

**Request con Email:**

```json
{
  "email": "<EMAIL>",
  "password": "contraseña123"
}
```

**Request con Número de Empleado:**

```json
{
  "employeeNumber": "EMP001",
  "password": "contraseña123"
}
```

**Request con Ambos (prioridad: email):**

```json
{
  "email": "<EMAIL>",
  "employeeNumber": "EMP001",
  "password": "contraseña123"
}
```

**Response:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tokenType": "Bearer",
  "expiresIn": 60,
  "user": {
    "id": 1,
    "name": "Usuario Ejemplo",
    "email": "<EMAIL>",
    "employeeNumber": "EMP001",
    "role": "Admin"
  },
  "message": "Autenticación exitosa"
}
```

#### POST /api/auth/refresh

Renueva un token JWT expirado.

**Request:**

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### POST /api/auth/validate

Valida un token JWT y retorna información del usuario.

**Request:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Configuración JWT

En `appsettings.json`:

```json
{
  "Jwt": {
    "SecretKey": "TuClaveSecretaSuperSeguraParaJWT2024!@#$%^&*()_+",
    "Issuer": "ToolsMS",
    "Audience": "ToolsMS",
    "ExpirationMinutes": 60
  }
}
```

### Servicios Implementados

#### IPasswordService / PasswordService

- `HashPassword(string password)`: Genera hash con BCrypt
- `VerifyPassword(string password, string hashedPassword)`: Verifica contraseña
- `GenerateSalt()`: Genera salt aleatorio

#### IJwtService / JwtService

- `GenerateToken(int userId, string email, string role)`: Genera token JWT
- `ValidateToken(string token)`: Valida token y retorna claims
- `GetUserIdFromToken(string token)`: Extrae ID del usuario
- `GetUserEmailFromToken(string token)`: Extrae email del usuario
- `GetUserRoleFromToken(string token)`: Extrae rol del usuario

#### IAuthService / AuthService

- `AuthenticateAsync(string email, string password)`: Autentica por email
- `AuthenticateByEmployeeNumberAsync(string employeeNumber, string password)`: Autentica por número de empleado
- `AuthenticateAsync(string? email, string? employeeNumber, string password)`: Autentica flexible
- `GetUserFromTokenAsync(string token)`: Obtiene usuario del token
- `IsValidToken(string token)`: Valida si token es válido
- `RefreshTokenAsync(string token)`: Renueva token

### Seguridad

#### Hash de Contraseñas

- **Algoritmo**: BCrypt con factor de trabajo 12
- **Salt**: Generado automáticamente por BCrypt
- **Formato**: `$2a$12$...` (incluye salt en el hash)

#### Tokens JWT

- **Algoritmo**: HMAC-SHA256
- **Claims**: UserId, Email, Role, JTI, IAT
- **Expiración**: Configurable (por defecto 60 minutos)
- **Validación**: Issuer, Audience, Lifetime, Signature

### Lógica de Autenticación

#### Prioridad de Identificadores

1. **Email**: Si se proporciona, se usa primero
2. **Número de Empleado**: Si no se encuentra por email, se intenta por número de empleado
3. **Validación**: Debe proporcionarse al menos un identificador

#### Flujo de Autenticación

1. **Validación de entrada**: Verificar que se proporcione al menos un identificador
2. **Búsqueda por email**: Si se proporciona email, buscar usuario
3. **Búsqueda por número de empleado**: Si no se encuentra por email, buscar por número de empleado
4. **Verificación de estado**: Confirmar que el usuario esté activo
5. **Verificación de contraseña**: Validar contraseña con BCrypt
6. **Generación de token**: Crear JWT con información del usuario

### Uso en el Frontend

#### Login Flexible

```typescript
const login = async (email?: string, employeeNumber?: string, password: string) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ 
      email, 
      employeeNumber, 
      password 
    })
  });
  
  if (response.ok) {
    const data = await response.json();
    localStorage.setItem('token', data.token);
    return data;
  }
};

// Ejemplos de uso:
await login('<EMAIL>', undefined, 'password123'); // Por email
await login(undefined, 'EMP001', 'password123'); // Por número de empleado
await login('<EMAIL>', 'EMP001', 'password123'); // Ambos (prioridad email)
```

#### Validación de Token

```typescript
const validateToken = async (token: string) => {
  const response = await fetch('/api/auth/validate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token })
  });
  
  return response.ok ? await response.json() : null;
};
```

#### Refresh de Token

```typescript
const refreshToken = async (token: string) => {
  const response = await fetch('/api/auth/refresh', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refreshToken: token })
  });
  
  if (response.ok) {
    const data = await response.json();
    localStorage.setItem('token', data.token);
    return data;
  }
};
```

## OpenID Connect

### Endpoints

- `GET /api/auth/oidc/login`: Inicia flujo OIDC
- `GET /api/auth/oidc/callback`: Callback de OIDC
- `POST /api/auth/oidc/refresh`: Refresh token OIDC
- `POST /api/auth/revoke`: Revoca token
- `GET /api/auth/userinfo`: Obtiene información del usuario

### Configuración

En `appsettings.json`:

```json
{
  "OpenIdConnect": {
    "Authority": "https://login-qa.kochid.com",
    "ClientId": "9177ab59-a2a9-4441-85a6-edd73321d8ea",
    "ClientSecret": "9XE.-QsU1kDj6Oeb18eCUhtE5MkjSgxws~tdDMpoLdS5ZxpkQeYok.dpAVltjzAy",
    "RedirectUri": "http://localhost:5001/api/auth/callback"
  }
}
```

## Migración de Contraseñas

### Para Usuarios Existentes

Si tienes usuarios con contraseñas sin hash, necesitas migrarlos:

1. **Crear migración de base de datos** para actualizar contraseñas existentes
2. **Implementar lógica de migración** que hashee las contraseñas al primer login
3. **Marcar usuarios migrados** para evitar re-hashear

### Ejemplo de Migración

```csharp
public async Task<bool> MigratePasswordAsync(int userId, string plainPassword)
{
    var user = await _userRepository.GetByIdAsync(userId);
    if (user == null) return false;
    
    // Verificar si ya está hasheada (formato BCrypt)
    if (user.Password.StartsWith("$2a$") || user.Password.StartsWith("$2b$"))
        return true; // Ya migrada
    
    // Hashear y actualizar
    user.Password = _passwordService.HashPassword(plainPassword);
    await _userRepository.UpdateAsync(user);
    
    return true;
}
```

## Consideraciones de Seguridad

1. **Clave Secreta**: Cambiar la clave secreta en producción
2. **HTTPS**: Usar siempre HTTPS en producción
3. **Rate Limiting**: Implementar límites de intentos de login
4. **Logging**: Registrar intentos de autenticación fallidos
5. **Expiración**: Configurar tiempos de expiración apropiados
6. **Refresh Tokens**: Implementar revocación de refresh tokens
7. **Identificadores Únicos**: Asegurar que email y número de empleado sean únicos

## Próximos Pasos

1. **Implementar middleware de autenticación** para proteger endpoints
2. **Agregar autorización basada en roles**
3. **Implementar rate limiting**
4. **Agregar auditoría de autenticación**
5. **Configurar CORS apropiadamente**
6. **Implementar logout y revocación de tokens**
7. **Agregar validación de formato de número de empleado** 