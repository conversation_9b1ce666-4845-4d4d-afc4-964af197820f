# Sistema BaseEntity - Guía de Uso

## Descripción

El sistema `BaseEntity` es una implementación genérica que proporciona funcionalidad CRUD completa para cualquier
entidad que herede de `BaseEntity`. Es el nivel más básico de abstracción y puede ser utilizado para entidades que no
necesitan propiedades específicas como `Name` o `Description`.

## Arquitectura

### Componentes Base

1. **BaseEntity** - Entidad base con propiedades de auditoría
2. **BaseEntityDto** - DTO base para transferencia de datos (LECTURA)
3. **BaseEntityFilterDto** - DTO para filtros de búsqueda
4. **CreateBaseEntityDto** - DTO base para CREAR entidades
5. **UpdateBaseEntityDto** - DTO base para ACTUALIZAR entidades

### Interfaces Genéricas

1. **IGenericRepository<T>** - Repositorio genérico para BaseEntity
2. **IGenericService<TE<PERSON>ty, <PERSON><PERSON>, TFilterDto, TCreateDto, TUpdateDto>** - Servicio genérico para BaseEntity

### Implementaciones Base

1. **GenericRepository<T>** - Implementación del repositorio genérico
2. **GenericService<TEntity, TDto, TFilterDto, TCreateDto, TUpdateDto>** - Implementación del servicio genérico

## Propiedades de BaseEntity

### Propiedades de Auditoría

- `Id` (int, primary key, auto-increment)
- `Created` (DateTime, fecha de creación)
- `CreatedBy` (string, usuario que creó)
- `Updated` (DateTime?, fecha de última actualización)
- `UpdatedBy` (string?, usuario que actualizó)

### Propiedades de Estado

- `IsDeleted` (bool, soft delete)
- `Deleted` (DateTime?, fecha de eliminación)
- `DeletedBy` (string?, usuario que eliminó)
- `Active` (bool, estado activo/inactivo)

## DTOs

### BaseEntityDto (LECTURA)

```csharp
public class BaseEntityDto
{
    public int Id { get; set; }
    public DateTime Created { get; set; }
    public string CreatedString { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsDeleted { get; set; }
    public DateTime? Deleted { get; set; }
    public string? DeletedString { get; set; }
    public string? DeletedBy { get; set; }
    public DateTime? Updated { get; set; }
    public string? UpdatedString { get; set; }
    public string? UpdatedBy { get; set; }
    public bool Active { get; set; }
    
    // Propiedades para fechas locales (opcional)
    public DateTime CreatedLocal { get; set; }
    public string CreatedLocalString { get; set; } = string.Empty;
    public DateTime? UpdatedLocal { get; set; }
    public string? UpdatedLocalString { get; set; }
    public DateTime? DeletedLocal { get; set; }
    public string? DeletedLocalString { get; set; }
}
```

### CreateBaseEntityDto (CREACIÓN)

```csharp
public class CreateBaseEntityDto
{
    public bool Active { get; set; } = true;
    
    // Las entidades que hereden de BaseEntity pueden agregar sus propiedades específicas
    // heredando de este DTO
}
```

### UpdateBaseEntityDto (ACTUALIZACIÓN)

```csharp
public class UpdateBaseEntityDto
{
    public int Id { get; set; }
    public bool Active { get; set; }
    
    // Las entidades que hereden de BaseEntity pueden agregar sus propiedades específicas
    // heredando de este DTO
}
```

### BaseEntityFilterDto (FILTROS)

```csharp
public class BaseEntityFilterDto
{
    public bool? Active { get; set; }
    public bool IncludeDeleted { get; set; } = false;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? UpdatedFrom { get; set; }
    public DateTime? UpdatedTo { get; set; }
    public string? UpdatedBy { get; set; }
}
```

## Manejo de Fechas UTC y Locales

### Problema

Las fechas se almacenan en UTC en la base de datos usando `getutcdate()`, pero a menudo necesitas mostrarlas en la fecha
local del usuario.

### Solución Implementada

El sistema proporciona **ambas versiones** de las fechas:

#### Fechas UTC (Originales)

- `Created` - Fecha de creación en UTC
- `Updated` - Fecha de actualización en UTC
- `Deleted` - Fecha de eliminación en UTC
- `CreatedString` - Fecha UTC formateada como string
- `UpdatedString` - Fecha UTC formateada como string
- `DeletedString` - Fecha UTC formateada como string

#### Fechas Locales (Convertidas)

- `CreatedLocal` - Fecha de creación convertida a hora local
- `UpdatedLocal` - Fecha de actualización convertida a hora local
- `DeletedLocal` - Fecha de eliminación convertida a hora local
- `CreatedLocalString` - Fecha local formateada como string
- `UpdatedLocalString` - Fecha local formateada como string
- `DeletedLocalString` - Fecha local formateada como string

### Ejemplo de Uso

```csharp
// Obtener un usuario
var user = await userService.GetByIdAsync(1);

// Fechas UTC (para cálculos y almacenamiento)
Console.WriteLine($"Creado en UTC: {user.Created}"); // 2024-01-15T10:30:00Z
Console.WriteLine($"Creado en UTC (string): {user.CreatedString}"); // 2024-01-15 10:30:00

// Fechas Locales (para mostrar al usuario)
Console.WriteLine($"Creado en Local: {user.CreatedLocal}"); // 2024-01-15T12:30:00+02:00
Console.WriteLine($"Creado en Local (string): {user.CreatedLocalString}"); // 2024-01-15 12:30:00

// Comparación
var timeZoneOffset = user.CreatedLocal - user.Created; // +02:00:00
```

### Conversión Automática

La conversión se realiza automáticamente en el método `MapToDto`:

```csharp
protected virtual TDto MapToDto(TEntity entity)
{
    // Convertir fechas UTC a local
    var createdLocal = entity.Created.ToLocalTime();
    var updatedLocal = entity.Updated?.ToLocalTime();
    var deletedLocal = entity.Deleted?.ToLocalTime();

    var dto = new TDto
    {
        // ... propiedades básicas ...
        
        // Fechas UTC
        Created = entity.Created,
        CreatedString = entity.Created.ToString(DateFormats.SHORT_DATE_TIME),
        Updated = entity.Updated,
        UpdatedString = entity.Updated?.ToString(DateFormats.SHORT_DATE_TIME),
        
        // Fechas Locales
        CreatedLocal = createdLocal,
        CreatedLocalString = createdLocal.ToString(DateFormats.SHORT_DATE_TIME),
        UpdatedLocal = updatedLocal,
        UpdatedLocalString = updatedLocal?.ToString(DateFormats.SHORT_DATE_TIME),
    };

    return dto;
}
```

### Cuándo Usar Cada Versión

#### Usar Fechas UTC para:

- **Cálculos de tiempo**: Diferencia entre fechas, duraciones
- **Almacenamiento**: Mantener consistencia en la base de datos
- **APIs internacionales**: Comunicación con servicios externos
- **Logs del sistema**: Auditoría y debugging

#### Usar Fechas Locales para:

- **Interfaz de usuario**: Mostrar fechas al usuario final
- **Reportes**: Informes que se muestran en la zona horaria del usuario
- **Notificaciones**: Mensajes que mencionan fechas
- **Filtros de búsqueda**: Cuando el usuario busca por fecha local

### Configuración de Zona Horaria

La conversión usa la zona horaria del servidor donde se ejecuta la aplicación. Para aplicaciones web, considera:

1. **Detectar zona horaria del usuario** (JavaScript)
2. **Pasar zona horaria en las peticiones** (headers o parámetros)
3. **Convertir usando la zona horaria específica** del usuario

```csharp
// Ejemplo: Convertir usando zona horaria específica
var timeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
var localTime = TimeZoneInfo.ConvertTimeFromUtc(entity.Created, timeZone);
```

## Funcionalidades del Servicio Genérico

### Operaciones de Lectura

- `GetByIdAsync(int id, bool includeDeleted = false)` - Obtener por ID
- `GetAllAsync(bool includeDeleted = false)` - Obtener todas
- `GetPagedAsync(TFilterDto filter)` - Obtener paginadas con filtros
- `GetActiveAsync()` - Obtener solo activas
- `CountAsync(bool includeDeleted = false)` - Contar elementos

### Operaciones de Escritura (SOLO con DTOs)

- `CreateAsync(TCreateDto dto, string createdBy)` - Crear con DTO específico
- `CreateRangeAsync(IEnumerable<TCreateDto> dtos, string createdBy)` - Crear múltiples con DTOs
- `UpdateAsync(int id, TUpdateDto dto, string updatedBy)` - Actualizar con DTO específico
- `UpdateRangeAsync(IEnumerable<TUpdateDto> dtos, string updatedBy)` - Actualizar múltiples con DTOs

### Operaciones de Eliminación

- `DeleteAsync(int id, string deletedBy)` - Soft delete por ID
- `DeleteAsync(TEntity entity, string deletedBy)` - Soft delete por entidad
- `DeleteRangeAsync(IEnumerable<int> ids, string deletedBy)` - Soft delete múltiples por IDs
- `DeleteRangeAsync(IEnumerable<TEntity> entities, string deletedBy)` - Soft delete múltiples entidades
- `HardDeleteAsync(int id)` - Eliminación permanente por ID
- `HardDeleteAsync(TEntity entity)` - Eliminación permanente por entidad
- `HardDeleteRangeAsync(IEnumerable<int> ids)` - Eliminación permanente múltiples por IDs
- `HardDeleteRangeAsync(IEnumerable<TEntity> entities)` - Eliminación permanente múltiples entidades

### Operaciones de Activación/Desactivación

- `ActivateAsync(int id, string updatedBy)` - Activar por ID
- `DeactivateAsync(int id, string updatedBy)` - Desactivar por ID
- `ActivateAsync(TEntity entity, string updatedBy)` - Activar por entidad
- `DeactivateAsync(TEntity entity, string updatedBy)` - Desactivar por entidad

### Operaciones de Validación

- `ExistsAsync(int id, bool includeDeleted = false)` - Verificar existencia

### Operaciones de Búsqueda

- `FindAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false)` - Buscar con predicado
- `FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false)` - Primera coincidencia o
  null
- `FirstAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false)` - Primera coincidencia

## Uso Correcto de DTOs

### ✅ Uso Correcto

```csharp
// Para crear una nueva entidad
var createDto = new CreateUserDto
{
    Name = "Juan Pérez",
    Email = "<EMAIL>",
    Password = "password123",
    Active = true
};

var newUser = await userService.CreateAsync(createDto, "admin");

// Para actualizar una entidad existente
var updateDto = new UpdateUserDto
{
    Id = 1,
    Name = "Juan Pérez Actualizado",
    Email = "<EMAIL>",
    Active = false
};

var updatedUser = await userService.UpdateAsync(1, updateDto, "admin");

// Para mostrar fechas al usuario
Console.WriteLine($"Usuario creado: {updatedUser.CreatedLocalString}");
Console.WriteLine($"Última actualización: {updatedUser.UpdatedLocalString}");
```

### ❌ Uso Incorrecto

```csharp
// ❌ NO usar BaseEntityDto para crear
var wrongCreateDto = new UserDto
{
    Name = "Juan Pérez",
    Email = "<EMAIL>"
};
// Esto NO funcionará - BaseEntityDto no tiene propiedades de creación

// ❌ NO usar BaseEntityDto para actualizar
var wrongUpdateDto = new UserDto
{
    Id = 1,
    Name = "Juan Pérez"
};
// Esto NO funcionará - BaseEntityDto no está diseñado para actualizaciones

// ❌ NO usar entidades directamente en el servicio
var entity = new User { Name = "Juan" };
// await userService.AddAsync(entity, "admin"); // ❌ Método eliminado
```

## Cómo Crear una Nueva Entidad

### Paso 1: Crear la Entidad

```csharp
namespace ToolsMS.Backend.Domain.Entities;

public class MiEntidad : BaseEntity
{
    // Propiedades específicas de tu entidad
    public string PropiedadEspecifica { get; set; }
    public int ValorNumerico { get; set; }
    public DateTime? FechaOpcional { get; set; }
}
```

### Paso 2: Crear los DTOs

```csharp
using ToolsMS.Backend.Application.DTOs.BaseEntity;

namespace ToolsMS.Backend.Application.DTOs.MiEntidad;

public class MiEntidadDto : BaseEntityDto
{
    public string PropiedadEspecifica { get; set; } = string.Empty;
    public int ValorNumerico { get; set; }
    public DateTime? FechaOpcional { get; set; }
}

public class CreateMiEntidadDto : CreateBaseEntityDto
{
    [Required]
    [StringLength(200)]
    public string PropiedadEspecifica { get; set; } = string.Empty;
    
    [Range(1, 1000)]
    public int ValorNumerico { get; set; }
    
    public DateTime? FechaOpcional { get; set; }
}

public class UpdateMiEntidadDto : UpdateBaseEntityDto
{
    [Required]
    [StringLength(200)]
    public string PropiedadEspecifica { get; set; } = string.Empty;
    
    [Range(1, 1000)]
    public int ValorNumerico { get; set; }
    
    public DateTime? FechaOpcional { get; set; }
}

public class MiEntidadFilterDto : BaseEntityFilterDto
{
    public string? PropiedadEspecifica { get; set; }
    public int? ValorNumericoMin { get; set; }
    public int? ValorNumericoMax { get; set; }
}
```

### Paso 3: Crear el Servicio

```csharp
namespace ToolsMS.Backend.Application.Services;

public class MiEntidadService : GenericService<MiEntidad, MiEntidadDto, MiEntidadFilterDto, CreateMiEntidadDto, UpdateMiEntidadDto>, IMiEntidadService
{
    public MiEntidadService(IGenericRepository<MiEntidad> repository) : base(repository)
    {
    }

    protected override MiEntidad MapToEntity(CreateMiEntidadDto dto)
    {
        return new MiEntidad
        {
            PropiedadEspecifica = dto.PropiedadEspecifica,
            ValorNumerico = dto.ValorNumerico,
            FechaOpcional = dto.FechaOpcional,
            Active = dto.Active
        };
    }

    protected override MiEntidad MapToEntity(UpdateMiEntidadDto dto)
    {
        return new MiEntidad
        {
            Id = dto.Id,
            PropiedadEspecifica = dto.PropiedadEspecifica,
            ValorNumerico = dto.ValorNumerico,
            FechaOpcional = dto.FechaOpcional,
            Active = dto.Active
        };
    }

    protected override MiEntidadDto MapToDto(MiEntidad entity)
    {
        // Convertir fechas UTC a local
        var createdLocal = entity.Created.ToLocalTime();
        var updatedLocal = entity.Updated?.ToLocalTime();
        var deletedLocal = entity.Deleted?.ToLocalTime();

        return new MiEntidadDto
        {
            Id = entity.Id,
            PropiedadEspecifica = entity.PropiedadEspecifica,
            ValorNumerico = entity.ValorNumerico,
            FechaOpcional = entity.FechaOpcional,
            Created = entity.Created,
            CreatedString = entity.Created.ToString(DateFormats.SHORT_DATE_TIME),
            CreatedBy = entity.CreatedBy,
            IsDeleted = entity.IsDeleted,
            Deleted = entity.Deleted,
            DeletedString = entity.Deleted?.ToString(DateFormats.SHORT_DATE_TIME),
            DeletedBy = entity.DeletedBy,
            Updated = entity.Updated,
            UpdatedString = entity.Updated?.ToString(DateFormats.SHORT_DATE_TIME),
            UpdatedBy = entity.UpdatedBy,
            Active = entity.Active,
            
            // Propiedades para fechas locales
            CreatedLocal = createdLocal,
            CreatedLocalString = createdLocal.ToString(DateFormats.SHORT_DATE_TIME),
            UpdatedLocal = updatedLocal,
            UpdatedLocalString = updatedLocal?.ToString(DateFormats.SHORT_DATE_TIME),
            DeletedLocal = deletedLocal,
            DeletedLocalString = deletedLocal?.ToString(DateFormats.SHORT_DATE_TIME)
        };
    }
}
```

### Paso 4: Crear el Controlador

```csharp
namespace ToolsMS.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MiEntidadController : GenericController<MiEntidad, MiEntidadDto, MiEntidadFilterDto, CreateMiEntidadDto, UpdateMiEntidadDto>
{
    public MiEntidadController(IMiEntidadService service) : base(service)
    {
    }
}
```

## Endpoints Disponibles

### Creación

- `POST /api/[controller]` - Crear una entidad
- `POST /api/[controller]/batch` - Crear múltiples entidades

### Actualización

- `PUT /api/[controller]/update/{id}` - Actualizar entidad específica
- `PUT /api/[controller]/batch` - Actualizar múltiples entidades

### Lectura

- `GET /api/[controller]/{id}` - Obtener por ID
- `GET /api/[controller]` - Obtener todas
- `GET /api/[controller]/paged` - Obtener paginadas
- `GET /api/[controller]/active` - Obtener activas

### Eliminación

- `DELETE /api/[controller]/{id}` - Soft delete
- `DELETE /api/[controller]/{id}/permanent` - Eliminación permanente

### Activación/Desactivación

- `PATCH /api/[controller]/{id}/activate` - Activar
- `PATCH /api/[controller]/{id}/deactivate` - Desactivar

## Beneficios del Sistema

1. **Separación Clara de Responsabilidades**: Cada DTO tiene un propósito específico
2. **Validación Automática**: Los DTOs de creación/actualización solo contienen campos editables
3. **Seguridad**: Previene la modificación accidental de campos del sistema
4. **Consistencia**: Todas las entidades siguen el mismo patrón
5. **Mantenibilidad**: Cambios en un DTO base se reflejan en todas las entidades
6. **Claridad**: Es obvio qué campos se pueden modificar y cuáles no
7. **Simplicidad**: SOLO se usan DTOs específicos, eliminando confusión
8. **Manejo de Fechas**: Soporte completo para fechas UTC y locales

## Diferencias con SimpleEntity

### BaseEntity

- **Propósito**: Entidades básicas con solo auditoría
- **Propiedades**: Id, Created, CreatedBy, Updated, UpdatedBy, IsDeleted, Deleted, DeletedBy, Active
- **Uso**: Entidades que no necesitan propiedades específicas como Name/Description

### SimpleEntity

- **Propósito**: Entidades con Name y Description
- **Propiedades**: Todas las de BaseEntity + Name, Description
- **Uso**: Entidades que requieren un nombre y descripción

## Consideraciones de Seguridad

### Validación de Datos

- Siempre validar DTOs de entrada con Data Annotations
- Usar FluentValidation para validaciones complejas
- Sanitizar datos antes de almacenar

### Auditoría

- Los campos de auditoría se llenan automáticamente
- Usar `suser_name()` para obtener el usuario actual
- Mantener logs de todas las operaciones críticas

### Soft Delete

- Siempre usar soft delete para datos importantes
- Implementar restauración de datos eliminados
- Mantener historial de eliminaciones 