### OpenID Connect - Iniciar sesión
# Obtiene la URL de autorización para iniciar el flujo de autenticación
# Esta URL redirigirá al usuario a https://login-qa.kochid.com/as/authorize
GET {{baseUrl}}/api/auth/login?state=test123&scope=openid profile email
Content-Type: application/json

###

### OpenID Connect - Callback (simulado)
# Este endpoint se llama automáticamente por el proveedor de identidad
# Los parámetros code y state vienen en la URL de redirección
# Ejemplo de URL de callback: http://localhost:5001/api/auth/callback?code=abc123&state=test123
GET {{baseUrl}}/api/auth/callback?code=authorization_code_here&state=test123
Content-Type: application/json

###

### OpenID Connect - Obtener información del usuario
# Requiere un token de acceso válido en el header Authorization
# El token se obtiene del endpoint callback
GET {{baseUrl}}/api/auth/userinfo
Authorization: Bearer your_access_token_here
Content-Type: application/json

###

### OpenID Connect - Refrescar token
# Usa el refresh_token obtenido del endpoint callback
POST {{baseUrl}}/api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your_refresh_token_here"
}

###

### OpenID Connect - Revocar token
# Revoca el token de acceso o refresh token
POST {{baseUrl}}/api/auth/revoke
Content-Type: application/json

{
  "token": "your_access_token_here",
  "tokenTypeHint": "access_token"
}

###

### Prueba del endpoint de ejemplo
GET {{baseUrl}}/api/example/test
Content-Type: application/json

###

### Prueba de configuración
# Verifica que la configuración esté cargada correctamente
GET {{baseUrl}}/api/example/test
Content-Type: application/json

###

# Variables de entorno para las pruebas
# @baseUrl = https://localhost:7001
# @baseUrl = http://localhost:5001

# Notas importantes:
# 1. El flujo completo requiere que el usuario sea redirigido a https://login-qa.kochid.com/as/authorize
# 2. Después de la autorización, KochID redirigirá a http://localhost:5001/api/auth/callback
# 3. El callback procesará el código de autorización y obtendrá el token
# 4. Con el token se puede acceder a /api/auth/userinfo
# 5. La autenticación usa Basic Auth con client_id:client_secret en base64 