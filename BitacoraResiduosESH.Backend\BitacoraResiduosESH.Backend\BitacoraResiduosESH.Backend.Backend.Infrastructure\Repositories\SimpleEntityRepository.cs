﻿namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories;

public class SimpleEntityRepository<TEntity>(AppDbContext context)
    : GenericRepository<TEntity>(context), ISimpleEntityRepository<TEntity>
    where TEntity : SimpleEntity
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public async Task<PagedResponse<TEntity>> GetByNameAsync(string name, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando {EntityType} por nombre paginadas - Nombre: {Name}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, name, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Aplicar filtro por nombre
        query = query.Where(e => e.Name.ToLower().Contains(name.ToLower()));

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Búsqueda por nombre paginada completada: {Count} resultados de {TotalRecords} totales para '{Name}' en {EntityType}",
            data.Count, totalRecords, name, typeof(TEntity).Name);

        return new PagedResponse<TEntity>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<PagedResponse<TEntity>> GetByDescriptionAsync(string description, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando {EntityType} por descripción paginadas - Descripción: {Description}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, description, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Aplicar filtro por descripción
        query = query.Where(e => e.Description != null && e.Description.ToLower().Contains(description.ToLower()));

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Búsqueda por descripción paginada completada: {Count} resultados de {TotalRecords} totales para '{Description}' en {EntityType}",
            data.Count, totalRecords, description, typeof(TEntity).Name);

        return new PagedResponse<TEntity>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<PagedResponse<TEntity>> FuzzySearchAsync(string searchTerm, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Búsqueda fuzzy en {EntityType} - Término: {SearchTerm}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(TEntity).Name, searchTerm, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Búsqueda fuzzy: buscar en nombre y descripción
        var searchTermLower = searchTerm.ToLower();
        query = query.Where(e =>
            e.Name.ToLower().Contains(searchTermLower) ||
            (e.Description != null && e.Description.ToLower().Contains(searchTermLower))
        );

        var totalRecords = await query.CountAsync();

        // Ordenar por relevancia: primero por coincidencias exactas en nombre, luego por contiene en nombre, finalmente por descripción
        var data = await query
            .OrderBy(e => e.Name.ToLower() == searchTermLower ? 0 : 1) // Coincidencia exacta en nombre primero
            .ThenBy(e => e.Name.ToLower().StartsWith(searchTermLower) ? 0 : 1) // Comienza con el término
            .ThenBy(e => e.Name.ToLower().Contains(searchTermLower) ? 0 : 1) // Contiene el término en nombre
            .ThenBy(e => e.Name) // Orden alfabético como último criterio
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Búsqueda fuzzy completada: {Count} resultados de {TotalRecords} totales para '{SearchTerm}' en {EntityType}",
            data.Count, totalRecords, searchTerm, typeof(TEntity).Name);

        return new PagedResponse<TEntity>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }
}