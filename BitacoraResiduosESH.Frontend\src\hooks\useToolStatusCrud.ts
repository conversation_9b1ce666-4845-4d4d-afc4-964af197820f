import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import {toolStatusService} from '../services/toolStatusService';
import type {CreateToolStatusDto, UpdateToolStatusDto} from '../types/toolStatus';

// Query keys para TanStack Query
export const toolStatusQueryKeys = {
    all: ['toolstatuses'] as const,
    lists: () => [...toolStatusQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...toolStatusQueryKeys.lists(), filters] as const,
    details: () => [...toolStatusQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...toolStatusQueryKeys.details(), id] as const,
};

export const useToolStatusCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todos los estados de herramientas
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: toolStatusQueryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => toolStatusService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener estados de herramientas activos
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: toolStatusQueryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => toolStatusService.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener un estado de herramienta por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: toolStatusQueryKeys.detail(id),
            queryFn: () => toolStatusService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar estados de herramientas por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: toolStatusQueryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => toolStatusService.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar estados de herramientas por descripción
    const useSearchByDescription = (
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: toolStatusQueryKeys.list({search: 'description', description, pageNumber, pageSize, includeDeleted}),
            queryFn: () => toolStatusService.searchByDescription(description, pageNumber, pageSize, includeDeleted),
            enabled: !!description.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear un estado de herramienta
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateToolStatusDto) => toolStatusService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: toolStatusQueryKeys.lists()});
                toast.success('Estado de herramienta creado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el estado de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar un estado de herramienta
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: UpdateToolStatusDto }) =>
                toolStatusService.update(id, data),
            onSuccess: (updatedToolStatus) => {
                queryClient.invalidateQueries({queryKey: toolStatusQueryKeys.lists()});
                queryClient.setQueryData(toolStatusQueryKeys.detail(updatedToolStatus.id), updatedToolStatus);
                toast.success('Estado de herramienta actualizado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el estado de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar un estado de herramienta
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => toolStatusService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: toolStatusQueryKeys.lists()});
                queryClient.removeQueries({queryKey: toolStatusQueryKeys.detail(deletedId)});
                toast.success('Estado de herramienta eliminado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el estado de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para activar un estado de herramienta
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => toolStatusService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: toolStatusQueryKeys.lists()});
                toast.success('Estado de herramienta activado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el estado de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar un estado de herramienta
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => toolStatusService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: toolStatusQueryKeys.lists()});
                toast.success('Estado de herramienta desactivado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el estado de herramienta: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: toolStatusQueryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByDescription,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
}; 