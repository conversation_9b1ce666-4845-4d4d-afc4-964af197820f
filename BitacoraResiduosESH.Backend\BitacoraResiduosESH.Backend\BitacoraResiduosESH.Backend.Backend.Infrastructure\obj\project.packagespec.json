﻿"restore":{"projectUniqueName":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Infrastructure\\BitacoraResiduosESH.Backend.Backend.Infrastructure.csproj","projectName":"BitacoraResiduosESH.Backend.Backend.Infrastructure","projectPath":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Infrastructure\\BitacoraResiduosESH.Backend.Backend.Infrastructure.csproj","outputPath":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Infrastructure\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Application\\BitacoraResiduosESH.Backend.Backend.Application.csproj":{"projectPath":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Application\\BitacoraResiduosESH.Backend.Backend.Application.csproj"},"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Domain\\BitacoraResiduosESH.Backend.Backend.Domain.csproj":{"projectPath":"C:\\Repositories\\WebProjects\\BitacoraResiduosESH\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend\\BitacoraResiduosESH.Backend.Backend.Domain\\BitacoraResiduosESH.Backend.Backend.Domain.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"BCrypt.Net-Next":{"target":"Package","version":"[4.0.3, )"},"EntityFrameworkCore.Triggers":{"target":"Package","version":"[1.2.3, )"},"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Abstractions":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Design":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Relational":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Sqlite":{"target":"Package","version":"[9.0.6, )"},"Microsoft.EntityFrameworkCore.Tools":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[9.0.6, )"},"Microsoft.IdentityModel.Tokens":{"target":"Package","version":"[7.4.0, )"},"NLog":{"target":"Package","version":"[5.5.0, )"},"NLog.Extensions.Logging":{"target":"Package","version":"[5.3.8, )"},"NLog.Web.AspNetCore":{"target":"Package","version":"[5.3.8, )"},"System.DirectoryServices":{"target":"Package","version":"[9.0.6, )"},"System.IdentityModel.Tokens.Jwt":{"target":"Package","version":"[7.4.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}