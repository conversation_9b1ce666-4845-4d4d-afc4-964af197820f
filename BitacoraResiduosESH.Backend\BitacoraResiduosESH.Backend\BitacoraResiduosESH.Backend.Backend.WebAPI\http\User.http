### Controlador de Usuario - Ejemplos de Uso

# =============================================================================
# OPERACIONES DE LECTURA (HEREDADAS DEL CONTROLADOR GENÉRICO)
# =============================================================================

### Obtener usuario por ID
GET https://localhost:7001/api/user/1
Accept: application/json

### Obtener usuario por ID (incluir eliminados)
GET https://localhost:7001/api/user/1?includeDeleted=true
Accept: application/json

### Obtener todos los usuarios
GET https://localhost:7001/api/user
Accept: application/json

### Obtener todos los usuarios (incluir eliminados)
GET https://localhost:7001/api/user?includeDeleted=true
Accept: application/json

### Obtener usuarios paginados
GET https://localhost:7001/api/user/paged?pageNumber=1&pageSize=10
Accept: application/json

### Obtener usuarios paginados con filtros
GET https://localhost:7001/api/user/paged?pageNumber=1&pageSize=10&active=true&name=admin&email=<EMAIL>
Accept: application/json

### Obtener solo usuarios activos
GET https://localhost:7001/api/user/active
Accept: application/json

### Contar usuarios
GET https://localhost:7001/api/user/count
Accept: application/json

### Contar usuarios (incluir eliminados)
GET https://localhost:7001/api/user/count?includeDeleted=true
Accept: application/json

# =============================================================================
# OPERACIONES DE ESCRITURA (HEREDADAS DEL CONTROLADOR GENÉRICO)
# =============================================================================

### Agregar un nuevo usuario
POST https://localhost:7001/api/user
Content-Type: application/json

{
  "name": "Nuevo Usuario",
  "email": "<EMAIL>",
  "password": "password123"
}

### Agregar múltiples usuarios
POST https://localhost:7001/api/user/batch
Content-Type: application/json

[
  {
    "name": "Usuario 1",
    "email": "<EMAIL>",
    "password": "password123"
  },
  {
    "name": "Usuario 2",
    "email": "<EMAIL>",
    "password": "password456"
  }
]

### Actualizar un usuario
PUT https://localhost:7001/api/user
Content-Type: application/json

{
  "id": 1,
  "name": "Usuario Actualizado",
  "email": "<EMAIL>",
  "password": "nuevapassword123"
}

### Actualizar múltiples usuarios
PUT https://localhost:7001/api/user/batch
Content-Type: application/json

[
  {
    "id": 1,
    "name": "Usuario 1 Actualizado",
    "email": "<EMAIL>",
    "password": "password123"
  },
  {
    "id": 2,
    "name": "Usuario 2 Actualizado",
    "email": "<EMAIL>",
    "password": "password456"
  }
]

# =============================================================================
# OPERACIONES DE ELIMINACIÓN (HEREDADAS DEL CONTROLADOR GENÉRICO)
# =============================================================================

### Eliminar usuario (soft delete)
DELETE https://localhost:7001/api/user/1

### Eliminar múltiples usuarios (soft delete)
DELETE https://localhost:7001/api/user/batch
Content-Type: application/json

[
  1,
  2,
  3
]

### Eliminar usuario permanentemente (hard delete)
DELETE https://localhost:7001/api/user/1/permanent

### Eliminar múltiples usuarios permanentemente (hard delete)
DELETE https://localhost:7001/api/user/batch/permanent
Content-Type: application/json

[
  1,
  2,
  3
]

# =============================================================================
# OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN (HEREDADAS DEL CONTROLADOR GENÉRICO)
# =============================================================================

### Activar usuario
PATCH https://localhost:7001/api/user/1/activate

### Desactivar usuario
PATCH https://localhost:7001/api/user/1/deactivate

# =============================================================================
# OPERACIONES DE VALIDACIÓN (HEREDADAS DEL CONTROLADOR GENÉRICO)
# =============================================================================

### Verificar existencia de usuario
GET https://localhost:7001/api/user/1/exists
Accept: application/json

### Verificar existencia de usuario (incluir eliminados)
GET https://localhost:7001/api/user/1/exists?includeDeleted=true
Accept: application/json

# =============================================================================
# OPERACIONES ESPECÍFICAS DEL CONTROLADOR DE USUARIO
# =============================================================================

### Buscar usuarios por nombre
GET https://localhost:7001/api/user/search/name?name=admin
Accept: application/json

### Buscar usuarios por nombre (incluir eliminados)
GET https://localhost:7001/api/user/search/name?name=admin&includeDeleted=true
Accept: application/json

### Buscar usuarios por email
GET https://localhost:7001/api/user/search/email?email=<EMAIL>
Accept: application/json

### Buscar usuarios por email (incluir eliminados)
GET https://localhost:7001/api/user/search/email?email=<EMAIL>&includeDeleted=true
Accept: application/json

### Obtener usuario por email exacto
GET https://localhost:7001/api/user/by-email/<EMAIL>
Accept: application/json

### Obtener usuario por email exacto (incluir eliminados)
GET https://localhost:7001/api/user/by-email/<EMAIL>?includeDeleted=true
Accept: application/json

### Verificar existencia por email
GET https://localhost:7001/api/user/exists/email?email=<EMAIL>
Accept: application/json

### Verificar existencia por email (excluir ID específico)
GET https://localhost:7001/api/user/exists/email?email=<EMAIL>&excludeId=1
Accept: application/json

### Verificar existencia por email (incluir eliminados)
GET https://localhost:7001/api/user/exists/email?email=<EMAIL>&includeDeleted=true
Accept: application/json

### Búsqueda personalizada
GET https://localhost:7001/api/user/search?predicate=admin&includeDeleted=false
Accept: application/json

# =============================================================================
# OPERACIONES DE AUTENTICACIÓN
# =============================================================================

### Obtener perfil del usuario autenticado
GET https://localhost:7001/api/user/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

### Verificar estado de autenticación
GET https://localhost:7001/api/user/authenticated
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

# =============================================================================
# EJEMPLOS CON HEADERS DE AUTORIZACIÓN
# =============================================================================

### Obtener usuario con autorización
GET https://localhost:7001/api/user/1
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

### Agregar usuario con autorización
POST https://localhost:7001/api/user
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "name": "Usuario con Autorización",
  "email": "<EMAIL>",
  "password": "password123"
}

### Actualizar usuario con autorización
PUT https://localhost:7001/api/user
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "id": 1,
  "name": "Usuario Actualizado con Autorización",
  "email": "<EMAIL>",
  "password": "nuevapassword123"
}

# =============================================================================
# EJEMPLOS DE FILTROS AVANZADOS
# =============================================================================

### Filtros por fecha de creación
GET https://localhost:7001/api/user/paged?createdFrom=2024-01-01T00:00:00Z&createdTo=2024-12-31T23:59:59Z
Accept: application/json

### Filtros por usuario que creó
GET https://localhost:7001/api/user/paged?createdBy=admin
Accept: application/json

### Filtros por fecha de actualización
GET https://localhost:7001/api/user/paged?updatedFrom=2024-06-01T00:00:00Z&updatedTo=2024-06-30T23:59:59Z
Accept: application/json

### Filtros por usuario que actualizó
GET https://localhost:7001/api/user/paged?updatedBy=user1
Accept: application/json

### Filtros combinados
GET https://localhost:7001/api/user/paged?active=true&name=admin&createdBy=system&pageNumber=1&pageSize=20
Accept: application/json

# =============================================================================
# EJEMPLOS DE CASOS DE USO COMUNES
# =============================================================================

### Buscar usuarios administradores
GET https://localhost:7001/api/user/search/name?name=admin
Accept: application/json

### Verificar si un email ya está registrado
GET https://localhost:7001/api/user/exists/email?email=<EMAIL>
Accept: application/json

### Verificar si un email ya está registrado (excluyendo el usuario actual)
GET https://localhost:7001/api/user/exists/email?email=<EMAIL>&excludeId=1
Accept: application/json

### Obtener usuario por email para login
GET https://localhost:7001/api/user/by-email/<EMAIL>
Accept: application/json

### Buscar usuarios por dominio de email
GET https://localhost:7001/api/user/search/email?email=@example.com
Accept: application/json

### Obtener usuarios activos paginados
GET https://localhost:7001/api/user/paged?active=true&pageNumber=1&pageSize=50
Accept: application/json

### Contar usuarios activos
GET https://localhost:7001/api/user/count?active=true
Accept: application/json

# =============================================================================
# NOTAS IMPORTANTES
# =============================================================================

# 1. Reemplaza 'YOUR_JWT_TOKEN_HERE' con un token válido para operaciones autenticadas
# 2. Las fechas deben estar en formato ISO 8601
# 3. Los filtros de fecha son opcionales y pueden usarse individualmente
# 4. El parámetro 'includeDeleted' es opcional y por defecto es 'false'
# 5. La paginación usa 'pageNumber' (base 1) y 'pageSize' (mínimo 1)
# 6. Los endpoints de batch requieren arrays en el body
# 7. Los endpoints de eliminación permanente son irreversibles
# 8. El campo 'password' se expone en los DTOs (en producción, considera no exponerlo)
# 9. Los endpoints de búsqueda por nombre y email son case-insensitive
# 10. El endpoint 'by-email' busca coincidencia exacta del email
# 11. Los endpoints de autenticación requieren un token JWT válido
# 12. El endpoint 'exists/email' es útil para validaciones de formularios
# 13. Los filtros específicos de User (name, email) se aplican en memoria
# 14. El método Search personalizado busca por nombre que contenga el predicado 

### Variables de entorno
@baseUrl = https://localhost:7001
@contentType = application/json

### ========================================
### ENDPOINTS GENÉRICOS (heredados de GenericController)
### ========================================

### Obtener todos los usuarios (paginados)
GET {{baseUrl}}/api/User?page=1&pageSize=10
Content-Type: {{contentType}}

### Obtener usuario por ID
GET {{baseUrl}}/api/User/1
Content-Type: {{contentType}}

### Crear usuario (método genérico)
POST {{baseUrl}}/api/User
Content-Type: {{contentType}}

{
  "name": "Usuario Genérico",
  "email": "<EMAIL>",
  "password": "password123"
}

### Actualizar usuario (método genérico)
PUT {{baseUrl}}/api/User/1
Content-Type: {{contentType}}

{
  "id": 1,
  "name": "Usuario Actualizado",
  "email": "<EMAIL>",
  "password": "nuevaPassword123"
}

### Eliminar usuario (soft delete)
DELETE {{baseUrl}}/api/User/1
Content-Type: {{contentType}}

### Activar usuario
PATCH {{baseUrl}}/api/User/1/activate
Content-Type: {{contentType}}

### Desactivar usuario
PATCH {{baseUrl}}/api/User/1/deactivate
Content-Type: {{contentType}}

### Búsqueda genérica
GET {{baseUrl}}/api/User/search?predicate=admin&includeDeleted=false
Content-Type: {{contentType}}

### ========================================
### ENDPOINTS ESPECÍFICOS DE USER
### ========================================

### Crear usuario usando DTO específico
POST {{baseUrl}}/api/User/create
Content-Type: {{contentType}}

{
  "name": "Juan Pérez",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}

### Actualizar usuario usando DTO específico
PUT {{baseUrl}}/api/User/update/1
Content-Type: {{contentType}}

{
  "id": 1,
  "name": "Juan Carlos Pérez",
  "email": "<EMAIL>",
  "password": "NewSecurePassword456!"
}

### Buscar usuarios por nombre (búsqueda parcial)
GET {{baseUrl}}/api/User/search/name?name=juan&includeDeleted=false
Content-Type: {{contentType}}

### Buscar usuarios por email (búsqueda parcial)
GET {{baseUrl}}/api/User/search/email?email=example.com&includeDeleted=false
Content-Type: {{contentType}}

### Buscar usuario por email exacto
GET {{baseUrl}}/api/User/search/email/exact?email=<EMAIL>&includeDeleted=false
Content-Type: {{contentType}}

### Verificar si existe un usuario con email específico
GET {{baseUrl}}/api/User/exists/email?email=<EMAIL>
Content-Type: {{contentType}}

### Verificar existencia excluyendo un ID específico (útil para validaciones de actualización)
GET {{baseUrl}}/api/User/exists/email?email=<EMAIL>&excludeId=1
Content-Type: {{contentType}}

### Búsqueda personalizada
GET {{baseUrl}}/api/User/search/custom?predicate=admin&includeDeleted=false
Content-Type: {{contentType}}

### ========================================
### EJEMPLOS DE CASOS DE USO
### ========================================

### 1. Registro de nuevo usuario
POST {{baseUrl}}/api/User/create
Content-Type: {{contentType}}

{
  "name": "María García",
  "email": "<EMAIL>",
  "password": "MiContraseñaSegura2024!"
}

### 2. Actualización de perfil de usuario
PUT {{baseUrl}}/api/User/update/2
Content-Type: {{contentType}}

{
  "id": 2,
  "name": "María Elena García",
  "email": "<EMAIL>",
  "password": "NuevaContraseñaSegura2024!"
}

### 3. Búsqueda de usuarios para administración
GET {{baseUrl}}/api/User/search/name?name=garcia&includeDeleted=false
Content-Type: {{contentType}}

### 4. Verificación de disponibilidad de email para registro
GET {{baseUrl}}/api/User/exists/email?email=<EMAIL>
Content-Type: {{contentType}}

### 5. Verificación de disponibilidad de email para actualización
GET {{baseUrl}}/api/User/exists/email?email=<EMAIL>&excludeId=2
Content-Type: {{contentType}}

### ========================================
### EJEMPLOS DE RESPUESTAS ESPERADAS
### ========================================

### Respuesta exitosa de creación (201 Created)
# {
#   "id": 3,
#   "name": "María García",
#   "email": "<EMAIL>",
#   "password": "MiContraseñaSegura2024!",
#   "created": "2024-01-15T10:30:00Z",
#   "createdBy": "system",
#   "isDeleted": false,
#   "deleted": null,
#   "deletedBy": null,
#   "updated": null,
#   "updatedBy": null,
#   "active": true
# }

### Respuesta de error de validación (400 Bad Request)
# {
#   "name": ["El nombre es requerido"],
#   "email": ["El formato del email no es válido"],
#   "password": ["La contraseña debe tener entre 6 y 100 caracteres"]
# }

### Respuesta de conflicto por email duplicado (409 Conflict)
# "Ya existe un usuario con el email '<EMAIL>'"

### Respuesta de verificación de existencia (200 OK)
# true

### Respuesta de búsqueda por nombre (200 OK)
# [
#   {
#     "id": 1,
#     "name": "Juan Pérez",
#     "email": "<EMAIL>",
#     "password": "SecurePassword123!",
#     "created": "2024-01-15T09:00:00Z",
#     "createdBy": "system",
#     "isDeleted": false,
#     "deleted": null,
#     "deletedBy": null,
#     "updated": null,
#     "updatedBy": null,
#     "active": true
#   }
# ] 