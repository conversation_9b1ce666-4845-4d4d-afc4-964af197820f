import React from 'react';

export interface FormContainerProps {
    children: React.ReactNode;
    title?: string;
    subtitle?: string;
    onSubmit?: (e: React.FormEvent) => void;
    className?: string;
    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
    centered?: boolean;
}

const FormContainer: React.FC<FormContainerProps> = ({
                                                         children,
                                                         title,
                                                         subtitle,
                                                         onSubmit,
                                                         className = '',
                                                         maxWidth = 'md',
                                                         centered = true
                                                     }) => {
    const maxWidthClasses = {
        sm: 'max-w-sm',
        md: 'max-w-md',
        lg: 'max-w-lg',
        xl: 'max-w-xl',
        '2xl': 'max-w-2xl'
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit?.(e);
    };

    return (
        <div className={`${centered ? 'flex items-center justify-center' : ''} p-4`}>
            <div className={`w-full ${maxWidthClasses[maxWidth]} ${className}`}>
                <div
                    className="p-8 rounded-xl shadow-lg border transition-all duration-200"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)'
                    }}
                >
                    {/* Header */}
                    {(title || subtitle) && (
                        <div className="text-center mb-8">
                            {title && (
                                <h1
                                    className="text-2xl font-bold mb-2"
                                    style={{color: 'var(--text-primary)'}}
                                >
                                    {title}
                                </h1>
                            )}
                            {subtitle && (
                                <p
                                    className="text-sm"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    {subtitle}
                                </p>
                            )}
                        </div>
                    )}

                    {/* Form */}
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {children}
                    </form>
                </div>
            </div>
        </div>
    );
};

export default FormContainer; 