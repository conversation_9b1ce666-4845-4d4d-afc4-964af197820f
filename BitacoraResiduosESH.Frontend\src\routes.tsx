import {createRootRoute, createRoute, Outlet} from "@tanstack/react-router";
import DashboardLayout from "./layouts/DashboardLayout";
import ProtectedRoute from "./components/ProtectedRoute";
import Home from "./pages/Home";
import Login from "./pages/Login";
import CrudTable from "./pages/CrudTable";
import Roles from "./pages/Roles";
import Users from "./pages/Users";
import Areas from "./pages/Areas";
import WasteTypes from "./pages/WasteTypes";
import ContainerTypes from "./pages/ContainerTypes";
import BitacoraEntries from "./pages/BitacoraEntries";

// Ruta raíz para el dashboard (con layout)
const dashboardRoute = createRootRoute({
    component: () => <Outlet/>,
});

// Ruta de login (sin layout)
const loginRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/login",
    component: Login,
});

// Ruta de inicio (con layout protegido)
const indexRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/",
    component: () => (
        <ProtectedRoute>
            <DashboardLayout>
                <Home/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de roles (solo Admin)
const rolesRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/roles",
    component: () => (
        <ProtectedRoute requiredRoles="Admin">
            <DashboardLayout>
                <Roles/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de usuarios (Admin y Manager)
const usersRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/users",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <Users/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de tabla CRUD (Admin, Manager, Operator)
const crudTableRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/crud-table",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <CrudTable/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de producción (Admin, Manager, Operator)
const productionRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/production",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Producción</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de producción</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de órdenes de trabajo
const productionOrdersRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/production/orders",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Órdenes de Trabajo</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de órdenes de trabajo</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de programación
const productionScheduleRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/production/schedule",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Programación</h1>
                    <p className="text-gray-600 dark:text-gray-300">Programación de producción</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de control de calidad
const productionQualityRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/production/quality",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Control de Calidad</h1>
                    <p className="text-gray-600 dark:text-gray-300">Control de calidad de productos</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de inventario (Admin, Manager, Operator)
const inventoryRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/inventory",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Inventario</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de inventario</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de materiales
const inventoryMaterialsRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/inventory/materials",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Materiales</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de materiales</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de productos
const inventoryProductsRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/inventory/products",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Productos</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de productos</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de almacén
const inventoryWarehouseRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/inventory/warehouse",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Almacén</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de almacén</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de empleados (Admin, Manager)
const employeesRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/employees",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Empleados</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de empleados</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de personal
const employeesStaffRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/employees/staff",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Personal</h1>
                    <p className="text-gray-600 dark:text-gray-300">Gestión de personal</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de reportes (Admin, Manager)
const reportsRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/reports",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Reportes</h1>
                    <p className="text-gray-600 dark:text-gray-300">Reportes del sistema</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de reportes de producción
const reportsProductionRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/reports/production",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Reportes de Producción</h1>
                    <p className="text-gray-600 dark:text-gray-300">Reportes de producción</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de reportes financieros
const reportsFinancialRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/reports/financial",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Reportes Financieros</h1>
                    <p className="text-gray-600 dark:text-gray-300">Reportes financieros</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de reportes de rendimiento
const reportsPerformanceRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/reports/performance",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Reportes de Rendimiento</h1>
                    <p className="text-gray-600 dark:text-gray-300">Reportes de rendimiento</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de configuración (solo Admin)
const settingsRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/settings",
    component: () => (
        <ProtectedRoute requiredRoles="Admin">
            <DashboardLayout>
                <div className="p-6 bg-white dark:bg-gray-800 min-h-full">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Configuración</h1>
                    <p className="text-gray-600 dark:text-gray-300">Configuración del sistema</p>
                </div>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de áreas (Admin y Manager)
const areasRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/areas",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <Areas/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de tipos de residuos
const wasteTypesRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/waste-types",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <WasteTypes/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de tipos de contenedores
const containerTypesRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/container-types",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager"]}>
            <DashboardLayout>
                <ContainerTypes/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Ruta de bitácora de residuos
const bitacoraEntriesRoute = createRoute({
    getParentRoute: () => dashboardRoute,
    path: "/bitacora-entries",
    component: () => (
        <ProtectedRoute requiredRoles={["Admin", "Manager", "Operator"]}>
            <DashboardLayout>
                <BitacoraEntries/>
            </DashboardLayout>
        </ProtectedRoute>
    ),
});

// Exportar el árbol de rutas
export const routeTree = dashboardRoute.addChildren([
    loginRoute,
    indexRoute,
    rolesRoute,
    usersRoute,
    areasRoute,
    wasteTypesRoute,
    containerTypesRoute,
    bitacoraEntriesRoute,
    crudTableRoute,
    productionRoute,
    productionOrdersRoute,
    productionScheduleRoute,
    productionQualityRoute,
    inventoryRoute,
    inventoryMaterialsRoute,
    inventoryProductsRoute,
    inventoryWarehouseRoute,
    employeesRoute,
    employeesStaffRoute,
    reportsRoute,
    reportsProductionRoute,
    reportsFinancialRoute,
    reportsPerformanceRoute,
    settingsRoute,
]);