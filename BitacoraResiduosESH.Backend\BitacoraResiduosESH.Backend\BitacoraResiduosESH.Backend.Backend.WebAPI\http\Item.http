### Variables de entorno
@baseUrl = https://localhost:7001
@contentType = application/json

### ========================================
### OPERACIONES BÁSICAS CRUD (HEREDADAS DE SIMPLEENTITY)
### ========================================

### 1. Crear un item
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item de Prueba 1",
  "description": "Descripción del item de prueba número 1"
}

### 2. Crear otro item
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item de Prueba 2",
  "description": "Descripción del item de prueba número 2"
}

### 3. Crear item sin descripción
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item Sin Descripción"
}

### 4. Obtener todos los items
GET {{baseUrl}}/api/item

### 5. Obtener item por ID
GET {{baseUrl}}/api/item/1

### 6. Obtener item por ID (incluyendo eliminados)
GET {{baseUrl}}/api/item/1?includeDeleted=true

### 7. Actualizar item
PUT {{baseUrl}}/api/item/1
Content-Type: {{contentType}}

{
  "name": "Item de Prueba 1 - Actualizado",
  "description": "Descripción actualizada del item de prueba número 1"
}

### 8. Eliminar item (soft delete)
DELETE {{baseUrl}}/api/item/1

### 9. Eliminar item permanentemente
DELETE {{baseUrl}}/api/item/1/permanent

### ========================================
### OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN (HEREDADAS)
### ========================================

### 10. Desactivar item
PATCH {{baseUrl}}/api/item/1/deactivate

### 11. Activar item
PATCH {{baseUrl}}/api/item/1/activate

### ========================================
### OPERACIONES DE BÚSQUEDA (HEREDADAS)
### ========================================

### 12. Buscar items por nombre
GET {{baseUrl}}/api/item/search/name?name=prueba

### 13. Buscar items por descripción
GET {{baseUrl}}/api/item/search/description?description=descripción

### 14. Obtener items activos
GET {{baseUrl}}/api/item/active

### 15. Obtener items paginados
GET {{baseUrl}}/api/item/paged?pageNumber=1&pageSize=10

### 16. Obtener items paginados con filtros
GET {{baseUrl}}/api/item/paged?pageNumber=1&pageSize=10&name=prueba&active=true

### ========================================
### OPERACIONES DE VALIDACIÓN (HEREDADAS)
### ========================================

### 17. Verificar si existe un item
GET {{baseUrl}}/api/item/1/exists

### ========================================
### EJEMPLOS DE DATOS PARA PRUEBAS
### ========================================

### Crear items de ejemplo para pruebas
### Item 1
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Documento Importante",
  "description": "Documento importante del sistema"
}

### Item 2
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Configuración del Sistema",
  "description": "Configuración general del sistema"
}

### Item 3
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Plantilla de Email",
  "description": "Plantilla para envío de emails"
}

### Item 4
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Reporte Mensual",
  "description": "Reporte mensual de actividades"
}

### Item 5
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Manual de Usuario",
  "description": "Manual de usuario del sistema"
}

### ========================================
### PRUEBAS DE CASOS ESPECIALES
### ========================================

### Crear item con nombre muy largo
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Este es un nombre muy largo para probar la validación de longitud máxima del campo name que tiene un límite de 200 caracteres",
  "description": "Descripción de prueba"
}

### Crear item con descripción muy larga
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item con descripción larga",
  "description": "Esta es una descripción muy larga para probar la validación de longitud máxima del campo description que tiene un límite de 1000 caracteres. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."
}

### Intentar crear item sin nombre (debe fallar)
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "description": "Item sin nombre"
}

### Intentar crear item con nombre vacío (debe fallar)
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "",
  "description": "Item con nombre vacío"
}

### Intentar crear item con nombre que solo tiene espacios (debe fallar)
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "   ",
  "description": "Item con nombre que solo tiene espacios"
}

### ========================================
### PRUEBAS DE FILTROS AVANZADOS
### ========================================

### Filtrar por nombre específico
GET {{baseUrl}}/api/item/search/name?name=Documento

### Filtrar por descripción específica
GET {{baseUrl}}/api/item/search/description?description=sistema

### Filtrar por múltiples criterios usando paginación
GET {{baseUrl}}/api/item/paged?pageNumber=1&pageSize=5&name=prueba&active=true

### Obtener items eliminados
GET {{baseUrl}}/api/item?includeDeleted=true

### ========================================
### PRUEBAS DE VALIDACIONES DE NEGOCIO
### ========================================

### Intentar crear item con nombre duplicado (debe fallar)
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item de Prueba 1",
  "description": "Intento de crear item con nombre duplicado"
}

### Intentar actualizar item con nombre que ya existe en otro item (debe fallar)
PUT {{baseUrl}}/api/item/2
Content-Type: {{contentType}}

{
  "name": "Item de Prueba 1",
  "description": "Intento de actualizar con nombre duplicado"
}

### ========================================
### PRUEBAS DE OPERACIONES EN LOTE
### ========================================

### Crear múltiples items para probar paginación
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item Lote 1",
  "description": "Item del lote 1"
}

###
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item Lote 2",
  "description": "Item del lote 2"
}

###
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item Lote 3",
  "description": "Item del lote 3"
}

###
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item Lote 4",
  "description": "Item del lote 4"
}

###
POST {{baseUrl}}/api/item
Content-Type: {{contentType}}

{
  "name": "Item Lote 5",
  "description": "Item del lote 5"
}

### Probar paginación con los items creados
GET {{baseUrl}}/api/item/paged?pageNumber=1&pageSize=3

###
GET {{baseUrl}}/api/item/paged?pageNumber=2&pageSize=3 