namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

/// <summary>
///     Configuración para la entidad Item.
///     Hereda de SimpleEntityConfiguration para obtener la configuración estándar.
/// </summary>
public class RoleConfiguration : SimpleEntityConfiguration<Role>
{
    protected override string TableName => "Role";

    public override void Configure(EntityTypeBuilder<Role> builder)
    {
        // Configuración base de SimpleEntity
        base.Configure(builder);

        // No se agregan configuraciones específicas adicionales
        // Solo usa la configuración base que incluye:
        // - Tabla "Item" (singular, Pascal case)
        // - Propiedades Name (required, nvarchar(200)) y Description (optional, nvarchar(1000))
        // - Índices: IX_Item_Name, IX_Item_Name_Active, IX_Item_Name_Created
        // - Restricción única: UQ_Item_Name_NotDeleted
        // - Todas las configuraciones de BaseEntity (auditoría, soft delete, etc.)
    }
}