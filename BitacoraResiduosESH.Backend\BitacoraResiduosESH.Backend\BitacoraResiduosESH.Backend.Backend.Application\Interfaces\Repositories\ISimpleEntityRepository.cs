﻿namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;

public interface ISimpleEntityRepository<TEntity> : IGenericRepository<TEntity> where TEntity : SimpleEntity
{
    public Task<PagedResponse<TEntity>> GetByNameAsync(string name, PaginationFilter filter,
        bool includeDeleted = false);

    public Task<PagedResponse<TEntity>> GetByDescriptionAsync(string description, PaginationFilter filter,
        bool includeDeleted = false);

    public Task<PagedResponse<TEntity>> FuzzySearchAsync(string searchTerm, PaginationFilter filter,
        bool includeDeleted = false);
}