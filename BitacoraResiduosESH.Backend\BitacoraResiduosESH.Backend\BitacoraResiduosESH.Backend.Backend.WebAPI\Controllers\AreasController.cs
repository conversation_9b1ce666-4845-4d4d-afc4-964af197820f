﻿namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AreasController(IAreaService service)
    : SimpleEntityController<Area, AreaDto, CreateAreaDto, UpdateAreaDto, AreaFilterDto>(service)
{
    // No se agregan métodos adicionales
    // Solo hereda todos los endpoints de SimpleEntityController:
    // - GET /api/area/{id} - Obtener por ID
    // - GET /api/area - Obtener todos
    // - GET /api/area/paged - Obtener paginados
    // - GET /api/area/active - Obtener activos
    // - GET /api/area/search/name - Buscar por nombre
    // - GET /api/area/search/description - Buscar por descripción
    // - POST /api/area - Crear
    // - PUT /api/area/{id} - Actualizar
    // - DELETE /api/area/{id} - Eliminar (soft delete)
    // - DELETE /api/area/{id}/permanent - Eliminar permanentemente
    // - PATCH /api/area/{id}/activate - Activar
    // - PATCH /api/area/{id}/deactivate - Desactivar
    // - GET /api/area/{id}/exists - Verificar existencia
    // - Manejo de errores automático
    // - Logging automático
    // - Validaciones automáticas
}