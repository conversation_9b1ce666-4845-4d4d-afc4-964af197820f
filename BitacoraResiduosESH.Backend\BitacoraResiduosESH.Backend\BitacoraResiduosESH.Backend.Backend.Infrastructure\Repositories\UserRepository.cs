using BitacoraResiduosESH.Backend.Backend.Infrastructure.Data;

namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories;

public class UserRepository(AppDbContext context) : GenericRepository<User>(context), IUserRepository
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public async Task<PagedResponse<User>> GetByNameAsync(string name, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando usuarios por nombre paginados - Nombre: {Name}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            name, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Incluir la relación con Role
        query = query.Include(e => e.Role);

        // Aplicar filtro por nombre
        query = query.Where(e => e.Name.ToLower().Contains(name.ToLower()));

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Búsqueda por nombre paginada completada: {Count} resultados de {TotalRecords} totales para '{Name}'",
            data.Count, totalRecords, name);

        return new PagedResponse<User>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<PagedResponse<User>> GetByEmailAsync(string email, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando usuarios por email paginados - Email: {Email}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            email, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Incluir la relación con Role
        query = query.Include(e => e.Role);

        // Aplicar filtro por email
        query = query.Where(e => e.Email != null && e.Email.ToLower().Contains(email.ToLower()));

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Búsqueda por email paginada completada: {Count} resultados de {TotalRecords} totales para '{Email}'",
            data.Count, totalRecords, email);

        return new PagedResponse<User>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<User?> GetByEmailExactAsync(string email, bool includeDeleted = false)
    {
        Logger.Debug("Buscando usuario por email exacto: {Email}, IncludeDeleted: {IncludeDeleted}", email,
            includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Incluir la relación con Role
        query = query.Include(e => e.Role);

        var result = await query
            .FirstOrDefaultAsync(e => e.Email != null && e.Email.ToLower() == email.ToLower());

        Logger.Debug("Búsqueda por email exacto completada: {Found} para '{Email}'", result != null, email);

        return result;
    }

    public async Task<User?> GetByEmployeeNumberExactAsync(string employeeNumber, bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando usuario por numero de empleado exacto: {EmployeeNumber}, IncludeDeleted: {IncludeDeleted}",
            employeeNumber, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Incluir la relación con Role
        query = query.Include(e => e.Role);

        var result = await query
            .FirstOrDefaultAsync(e => e.EmployeeNumber.ToLower() == employeeNumber.ToLower());

        Logger.Debug("Búsqueda por numero de empleado exacto completada: {Found} para '{EmployeeNumber}'",
            result != null, employeeNumber);

        return result;
    }

    public async Task<bool> ExistsByEmailAsync(string email, int? excludeId = null, bool includeDeleted = false)
    {
        Logger.Debug(
            "Verificando existencia de usuario por email: {Email}, ExcludeId: {ExcludeId}, IncludeDeleted: {IncludeDeleted}",
            email, excludeId, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Filtrar por email exacto (case-insensitive)
        query = query.Where(e => e.Email != null && e.Email.ToLower() == email.ToLower());

        // Excluir ID específico si se proporciona
        if (excludeId.HasValue)
            query = query.Where(e => e.Id != excludeId.Value);

        var exists = await query.AnyAsync();

        Logger.Debug("Verificación de existencia por email completada para '{Email}': {Exists}", email, exists);

        return exists;
    }

    public async Task<bool> ExistsByEmployeeNumberAsync(string employeeNumber, int? excludeId = null,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Verificando existencia de usuario por numero de empleado: {EmployeeNumber}, ExcludeId: {ExcludeId}, IncludeDeleted: {IncludeDeleted}",
            employeeNumber, excludeId, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Filtrar por numero de empleado exacto (case-insensitive)
        query = query.Where(e => e.EmployeeNumber.ToLower() == employeeNumber.ToLower());

        // Excluir ID específico si se proporciona
        if (excludeId.HasValue)
            query = query.Where(e => e.Id != excludeId.Value);

        var exists = await query.AnyAsync();

        Logger.Debug("Verificación de existencia por numero de empleado completada para '{EmployeeNumber}': {Exists}",
            employeeNumber, exists);

        return exists;
    }

    // Sobrescribir el método GetByIdAsync para incluir la relación con Role
    public new async Task<User?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo usuario por ID: {Id}, IncludeDeleted: {IncludeDeleted}", id, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Incluir la relación con Role
        query = query.Include(e => e.Role);

        var result = await query.FirstOrDefaultAsync(e => e.Id == id);

        Logger.Debug("Usuario por ID obtenido: {Found} para ID {Id}", result != null, id);

        return result;
    }

    // Sobrescribir el método GetAllAsync para incluir la relación con Role
    public new async Task<PagedResponse<User>> GetAllAsync(PaginationFilter filter)
    {
        Logger.Debug(
            "Obteniendo todos los usuarios paginados - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            filter.PageNumber, filter.PageSize, filter.IncludeDeleted);

        var query = DbSet.AsQueryable();

        if (!filter.IncludeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Incluir la relación con Role
        query = query.Include(e => e.Role);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug("Todos los usuarios obtenidos paginados: {Count} elementos de {TotalRecords} totales",
            data.Count, totalRecords);

        return new PagedResponse<User>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }
}