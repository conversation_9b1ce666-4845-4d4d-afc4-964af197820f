import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import {areaService} from '../services/areaService';
import type {CreateAreaDto, UpdateAreaDto} from '../types/area';

// Query keys para TanStack Query
export const areaQueryKeys = {
    all: ['areas'] as const,
    lists: () => [...areaQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...areaQueryKeys.lists(), filters] as const,
    details: () => [...areaQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...areaQueryKeys.details(), id] as const,
};

export const useAreaCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todos los areas
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: areaQueryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => areaService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener areas activos
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: areaQueryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => areaService.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener un area por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: areaQueryKeys.detail(id),
            queryFn: () => areaService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar areas por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: areaQueryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => areaService.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar areas por descripción
    const useSearchByDescription = (
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: areaQueryKeys.list({search: 'description', description, pageNumber, pageSize, includeDeleted}),
            queryFn: () => areaService.searchByDescription(description, pageNumber, pageSize, includeDeleted),
            enabled: !!description.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear un area
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateAreaDto) => areaService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: areaQueryKeys.lists()});
                toast.success('Area creado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el area: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar un area
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: UpdateAreaDto }) =>
                areaService.update(id, data),
            onSuccess: (updatedArea) => {
                queryClient.invalidateQueries({queryKey: areaQueryKeys.lists()});
                queryClient.setQueryData(areaQueryKeys.detail(updatedArea.id), updatedArea);
                toast.success('Area actualizado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el area: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar un area
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => areaService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: areaQueryKeys.lists()});
                queryClient.removeQueries({queryKey: areaQueryKeys.detail(deletedId)});
                toast.success('Area eliminado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el area: ${error.message}`);
            },
        });
    };

    // Mutation para activar un area
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => areaService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: areaQueryKeys.lists()});
                toast.success('Area activado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el area: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar un area
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => areaService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: areaQueryKeys.lists()});
                toast.success('Area desactivado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el area: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: areaQueryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByDescription,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
}; 