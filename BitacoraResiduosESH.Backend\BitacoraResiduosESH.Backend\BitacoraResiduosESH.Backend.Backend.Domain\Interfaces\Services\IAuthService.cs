using BitacoraResiduosESH.Backend.Backend.Domain.Entities;

namespace BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;

/// <summary>
///     Servicio para autenticación local con JWT
/// </summary>
public interface IAuthService
{
    /// <summary>
    ///     Autentica un usuario con email y contraseña
    /// </summary>
    /// <param name="email">Email del usuario</param>
    /// <param name="password">Contraseña del usuario</param>
    /// <returns>Token JWT si la autenticación es exitosa, null en caso contrario</returns>
    Task<string?> AuthenticateAsync(string email, string password);

    /// <summary>
    ///     Autentica un usuario con número de empleado y contraseña
    /// </summary>
    /// <param name="employeeNumber">Número de empleado del usuario</param>
    /// <param name="password">Contraseña del usuario</param>
    /// <returns>Token JWT si la autenticación es exitosa, null en caso contrario</returns>
    Task<string?> AuthenticateByEmployeeNumberAsync(string employeeNumber, string password);

    /// <summary>
    ///     Autentica un usuario con email o número de empleado y contraseña
    /// </summary>
    /// <param name="email">Email del usuario (opcional)</param>
    /// <param name="employeeNumber">Número de empleado del usuario (opcional)</param>
    /// <param name="password">Contraseña del usuario</param>
    /// <returns>Token JWT si la autenticación es exitosa, null en caso contrario</returns>
    Task<string?> AuthenticateAsync(string? email, string? employeeNumber, string password);

    /// <summary>
    ///     Obtiene información del usuario autenticado
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>Información del usuario si el token es válido, null en caso contrario</returns>
    Task<User?> GetUserFromTokenAsync(string token);

    /// <summary>
    ///     Valida si un token JWT es válido
    /// </summary>
    /// <param name="token">Token JWT a validar</param>
    /// <returns>True si el token es válido, False en caso contrario</returns>
    bool IsValidToken(string token);

    /// <summary>
    ///     Refresca un token JWT
    /// </summary>
    /// <param name="token">Token JWT actual</param>
    /// <returns>Nuevo token JWT si el token actual es válido, null en caso contrario</returns>
    Task<string?> RefreshTokenAsync(string token);
}