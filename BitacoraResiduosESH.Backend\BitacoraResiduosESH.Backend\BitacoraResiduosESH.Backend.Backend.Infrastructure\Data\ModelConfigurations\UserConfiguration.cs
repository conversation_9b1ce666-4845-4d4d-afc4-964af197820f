namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

/// <summary>
///     Configuración para la entidad User.
///     Hereda de BaseEntityConfiguration para obtener la configuración estándar.
/// </summary>
public class UserConfiguration : BaseEntityConfiguration<User>
{
    protected override string TableName => "User";

    public override void Configure(EntityTypeBuilder<User> builder)
    {
        // Configuración base de BaseEntity
        base.Configure(builder);

        // Configuración específica de User
        ConfigureName(builder);
        ConfigureEmployeeNumber(builder);
        ConfigureEmail(builder);
        ConfigurePassword(builder);
        ConfigureRoleRelation(builder);
        ConfigureUserIndexes(builder);
        ConfigureUserConstraints(builder);
    }

    /// <summary>
    ///     Configura la propiedad Name
    /// </summary>
    protected virtual void ConfigureName(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(200)")
            .IsRequired();
    }

    /// <summary>
    ///     Configura la propiedad Email
    /// </summary>
    protected virtual void ConfigureEmail(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.Email)
            .HasColumnName("Email")
            .HasColumnType("nvarchar(255)");
    }

    protected virtual void ConfigureEmployeeNumber(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.EmployeeNumber)
            .HasColumnName("EmployeeNumber")
            .HasColumnType("nvarchar(30)")
            .IsRequired();
    }

    /// <summary>
    ///     Configura la propiedad Password
    /// </summary>
    protected virtual void ConfigurePassword(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.Password)
            .HasColumnName("Password")
            .HasColumnType("nvarchar(255)")
            .IsRequired();
    }

    /// <summary>
    ///     Configura la relación con Role
    /// </summary>
    protected virtual void ConfigureRoleRelation(EntityTypeBuilder<User> builder)
    {
        // Configurar la propiedad RoleId
        builder.Property(e => e.RoleId)
            .HasColumnName("RoleId")
            .IsRequired();

        // Configurar la relación con Role
        builder.HasOne(e => e.Role)
            .WithMany()
            .HasForeignKey(e => e.RoleId)
            .OnDelete(DeleteBehavior.Restrict) // Evitar eliminación en cascada
            .HasConstraintName($"FK_{TableName}_RoleId_Role_Id");

        // Índice para la relación con Role
        builder.HasIndex(e => e.RoleId)
            .HasDatabaseName($"IX_{TableName}_RoleId");
    }

    /// <summary>
    ///     Configura índices específicos para User
    /// </summary>
    protected virtual void ConfigureUserIndexes(EntityTypeBuilder<User> builder)
    {
        // Índice para Name (usado en búsquedas)
        builder.HasIndex(e => e.Name)
            .HasDatabaseName($"IX_{TableName}_Name");

        // Índice para Email (usado en búsquedas y autenticación)
        builder.HasIndex(e => e.Email)
            .HasDatabaseName($"IX_{TableName}_Email");

        // Índice para EmployeeNumber (usado en búsquedas)
        builder.HasIndex(e => e.EmployeeNumber)
            .HasDatabaseName($"IX_{TableName}_EmployeeNumber");

        // Índice compuesto para consultas comunes (Email + Active)
        builder.HasIndex(e => new { e.Email, e.Active })
            .HasDatabaseName($"IX_{TableName}_Email_Active");

        // Índice compuesto para consultas de auditoría (Name + Created)
        builder.HasIndex(e => new { e.Name, e.Created })
            .HasDatabaseName($"IX_{TableName}_Name_Created");

        // Índice compuesto para consultas por rol (RoleId + Active)
        builder.HasIndex(e => new { e.RoleId, e.Active })
            .HasDatabaseName($"IX_{TableName}_RoleId_Active");
    }

    /// <summary>
    ///     Configura restricciones específicas para User
    /// </summary>
    protected virtual void ConfigureUserConstraints(EntityTypeBuilder<User> builder)
    {
        // Restricción única: Email debe ser único entre usuarios no eliminados
        builder.HasIndex(e => e.Email)
            .IsUnique()
            .HasFilter("[IsDeleted] = 0")
            .HasDatabaseName($"UQ_{TableName}_Email_NotDeleted");

        // Restricción única: EmployeeNumber debe ser único entre usuarios no eliminados
        builder.HasIndex(e => e.EmployeeNumber)
            .IsUnique()
            .HasFilter("[IsDeleted] = 0")
            .HasDatabaseName($"UQ_{TableName}_EmployeeNumber_NotDeleted");
    }
}