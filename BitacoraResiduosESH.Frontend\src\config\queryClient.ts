import {QueryClient} from '@tanstack/react-query';

// Función para manejar errores de autorización globalmente
const handleAuthError = (error: any) => {
    // Si es un error 401 (No autorizado), redirigir al login
    if (error?.status === 401 || error?.response?.status === 401) {
        // Limpiar el store de autenticación usando localStorage
        try {
            localStorage.removeItem('auth-token');
            localStorage.removeItem('auth-user');
            localStorage.removeItem('auth-store');
        } catch (e) {
            console.warn('No se pudo limpiar el store de autenticación:', e);
        }

        // Redirigir al login
        window.location.href = '/login';
    }
};

// Configuración del QueryClient
export const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            // Tiempo que los datos se consideran frescos
            staleTime: 5 * 60 * 1000, // 5 minutos

            // Tiempo que los datos se mantienen en cache
            gcTime: 10 * 60 * 1000, // 10 minutos

            // Reintentos automáticos en caso de error
            retry: (failureCount, error: any) => {
                // No reintentar en errores 4xx (errores del cliente)
                if (error?.status >= 400 && error?.status < 500) {
                    return false;
                }
                // Reintentar hasta 3 veces para otros errores
                return failureCount < 3;
            },

            // Reintentar en segundo plano cuando la ventana recupera el foco
            refetchOnWindowFocus: false,

            // Reintentar cuando se reconecta la red
            refetchOnReconnect: true,

            // Manejar errores de autorización
            onError: (error) => {
                handleAuthError(error);
            },
        },
        mutations: {
            // Reintentos para mutaciones
            retry: (failureCount, error: any) => {
                // No reintentar mutaciones en errores 4xx
                if (error?.status >= 400 && error?.status < 500) {
                    return false;
                }
                // Reintentar hasta 2 veces para otros errores
                return failureCount < 2;
            },

            // Manejar errores de autorización
            onError: (error) => {
                handleAuthError(error);
            },
        },
    },
}); 