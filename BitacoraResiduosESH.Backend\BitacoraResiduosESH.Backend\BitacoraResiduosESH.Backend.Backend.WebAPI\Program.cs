using BitacoraResiduosESH.Backend.Backend.WebAPI;

var logger = LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
logger.Debug("Iniciando aplicación...");

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Configurar NLog
    builder.Services.AddNLog(builder.Configuration);
    builder.Host.UseNLog();

    // Configurar Entity Framework con SQLite
    builder.Services.AddDatabase(builder.Configuration);

    // Registrar repositorios y servicios
    builder.Services.AddRepositories();
    builder.Services.AddApplicationServices();
    builder.Services.AddInfrastructureServices();

    // Configurar autenticación JWT
    builder.Services.AddAuthentication(options =>
        {
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = builder.Configuration["Jwt:Issuer"],
                ValidAudience = builder.Configuration["Jwt:Audience"],
                IssuerSigningKey = new SymmetricSecurityKey(
                    Encoding.ASCII.GetBytes(builder.Configuration["Jwt:SecretKey"] ??
                                            "your-super-secret-key-with-at-least-32-characters")
                ),
                ClockSkew = TimeSpan.Zero,
                // Configuraciones adicionales para asegurar compatibilidad
                RequireExpirationTime = true,
                ValidateTokenReplay = false
            };

            options.Events = new JwtBearerEvents
            {
                OnAuthenticationFailed = context =>
                {
                    logger.Warn("Autenticación JWT fallida: {Error}", context.Exception.Message);
                    return Task.CompletedTask;
                },
                OnTokenValidated = context =>
                {
                    logger.Debug("Token JWT validado correctamente");
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    logger.Warn("Desafío de autenticación JWT: {Error}", context.Error);
                    return Task.CompletedTask;
                }
            };
        });

    builder.Services.AddAuthorization();

    // Configurar OpenAPI con autenticación JWT para Scalar
    builder.Services.AddEndpointsApiExplorer();
    builder.Services.AddOpenApi("v1",
        options => { options.AddDocumentTransformer<BearerSecuritySchemeTransformer>(); });

    builder.Services.AddControllers();
    builder.Services.AddCors(options =>
    {
        options.AddPolicy("AllowAll", policy =>
        {
            policy.AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader();
        });
    });

    var app = builder.Build();

    // Asegurar que la base de datos se cree y se apliquen las migraciones
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        context.Database.EnsureCreated();
        logger.Info("Base de datos inicializada correctamente");
    }

    // Inicializar la base de datos con datos de prueba
    await app.Services.InitializeDatabaseAsync();
    logger.Info("Datos de prueba inicializados correctamente");

    app.UseAuthentication();
    app.UseAuthorization();

    app.MapControllers();

    app.MapOpenApi();
    app.MapScalarApiReference();

    app.UseCors("AllowAll");
    app.UseDefaultFiles();
    app.UseStaticFiles();

    // Middleware de validación de OpenID Connect (opcional)
    // Comentar esta línea si no quieres validación automática de tokens
    //app.UseOpenIdConnectValidation();

    logger.Info("Aplicación iniciada correctamente");
    app.Run();
}
catch (Exception ex)
{
    logger.Error(ex, "Error al iniciar la aplicación");
    throw;
}
finally
{
    LogManager.Shutdown();
}


namespace BitacoraResiduosESH.Backend.Backend.WebAPI
{
    internal sealed class BearerSecuritySchemeTransformer(IAuthenticationSchemeProvider authenticationSchemeProvider)
        : IOpenApiDocumentTransformer
    {
        public async Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context,
            CancellationToken cancellationToken)
        {
            var authenticationSchemes = await authenticationSchemeProvider.GetAllSchemesAsync();
            if (authenticationSchemes.Any(authScheme => authScheme.Name == "Bearer"))
            {
                var requirements = new Dictionary<string, OpenApiSecurityScheme>
                {
                    ["Bearer"] = new()
                    {
                        Type = SecuritySchemeType.Http,
                        Scheme = "bearer",
                        In = ParameterLocation.Header,
                        BearerFormat = "Json Web Token"
                    }
                };
                document.Components ??= new OpenApiComponents();
                document.Components.SecuritySchemes = requirements;

                foreach (var operation in document.Paths.Values.SelectMany(path => path.Operations))
                    operation.Value.Security.Add(new OpenApiSecurityRequirement
                    {
                        [new OpenApiSecurityScheme { Reference = new OpenApiReference { Id = "Bearer", Type = ReferenceType.SecurityScheme } }] =
                            Array.Empty<string>()
                    });
            }
        }
    }
}