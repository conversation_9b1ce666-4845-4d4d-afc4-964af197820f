### Variables de entorno
@baseUrl = https://localhost:7001
@contentType = application/json

### ========================================
### OPERACIONES BÁSICAS CRUD
### ========================================

### 1. Crear una categoría raíz
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Tecnología",
  "description": "Categoría para productos y servicios tecnológicos",
  "icon": "fas fa-microchip"
}

### 2. Crear una subcategoría
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Computadoras",
  "description": "Computadoras de escritorio y portátiles",
  "icon": "fas fa-laptop",
  "parentCategoryId": 1
}

### 3. Obtener todas las categorías
GET {{baseUrl}}/api/category

### 4. Obtener categoría por ID
GET {{baseUrl}}/api/category/1

### 5. Obtener categoría por ID (incluyendo eliminadas)
GET {{baseUrl}}/api/category/1?includeDeleted=true

### 6. Actualizar categoría
PUT {{baseUrl}}/api/category/1
Content-Type: {{contentType}}

{
  "name": "Tecnología Avanzada",
  "description": "Categoría actualizada para productos y servicios tecnológicos avanzados",
  "icon": "fas fa-rocket",
  "parentCategoryId": null
}

### 7. Eliminar categoría (soft delete)
DELETE {{baseUrl}}/api/category/1

### 8. Eliminar categoría permanentemente
DELETE {{baseUrl}}/api/category/1/permanent

### ========================================
### OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN
### ========================================

### 9. Desactivar categoría
PATCH {{baseUrl}}/api/category/1/deactivate

### 10. Activar categoría
PATCH {{baseUrl}}/api/category/1/activate

### ========================================
### OPERACIONES DE BÚSQUEDA
### ========================================

### 11. Buscar categorías por nombre
GET {{baseUrl}}/api/category/search/name?name=tecnología

### 12. Buscar categorías por descripción
GET {{baseUrl}}/api/category/search/description?description=computadora

### 13. Obtener categorías activas
GET {{baseUrl}}/api/category/active

### 14. Obtener categorías paginadas
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10

### 15. Obtener categorías paginadas con filtros
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&name=tec&active=true

### ========================================
### OPERACIONES JERÁRQUICAS
### ========================================

### 16. Obtener categorías raíz
GET {{baseUrl}}/api/category/root

### 17. Obtener subcategorías de una categoría padre
GET {{baseUrl}}/api/category/subcategories/1

### 18. Obtener árbol completo de categorías
GET {{baseUrl}}/api/category/tree

### 19. Obtener categoría con sus subcategorías
GET {{baseUrl}}/api/category/1/with-subcategories

### 20. Obtener categorías por padre (null para raíz)
GET {{baseUrl}}/api/category/by-parent/

### 21. Obtener categorías por padre específico
GET {{baseUrl}}/api/category/by-parent/1

### ========================================
### OPERACIONES POR CARACTERÍSTICAS
### ========================================

### 22. Obtener categorías con icono
GET {{baseUrl}}/api/category/with-icon

### ========================================
### OPERACIONES DE VALIDACIÓN
### ========================================

### 23. Verificar si existe una categoría
GET {{baseUrl}}/api/category/1/exists

### 24. Verificar si una categoría tiene subcategorías
GET {{baseUrl}}/api/category/1/has-subcategories

### 25. Verificar si una categoría tiene ejemplos
GET {{baseUrl}}/api/category/1/has-examples

### 26. Obtener número de subcategorías
GET {{baseUrl}}/api/category/1/subcategories-count

### 27. Obtener número de ejemplos
GET {{baseUrl}}/api/category/1/examples-count

### ========================================
### EJEMPLOS DE DATOS PARA PRUEBAS
### ========================================

### Crear estructura de categorías para pruebas
### Categoría raíz: Electrónicos
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Electrónicos",
  "description": "Productos electrónicos de consumo",
  "icon": "fas fa-tv"
}

### Subcategoría: Smartphones
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Smartphones",
  "description": "Teléfonos inteligentes y accesorios",
  "icon": "fas fa-mobile-alt",
  "parentCategoryId": 1
}

### Subcategoría: Tablets
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Tablets",
  "description": "Tablets y iPads",
  "icon": "fas fa-tablet-alt",
  "parentCategoryId": 1
}

### Categoría raíz: Ropa
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Ropa",
  "description": "Vestimenta y accesorios",
  "icon": "fas fa-tshirt"
}

### Subcategoría: Ropa de Hombre
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Ropa de Hombre",
  "description": "Vestimenta para hombres",
  "icon": "fas fa-male",
  "parentCategoryId": 4
}

### Subcategoría: Ropa de Mujer
POST {{baseUrl}}/api/category
Content-Type: {{contentType}}

{
  "name": "Ropa de Mujer",
  "description": "Vestimenta para mujeres",
  "icon": "fas fa-female",
  "parentCategoryId": 4
}

### ========================================
### PRUEBAS DE FILTROS AVANZADOS
### ========================================

### Filtrar categorías raíz
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&isRootCategory=true

### Filtrar categorías con subcategorías
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&hasSubCategories=true

### Filtrar categorías sin subcategorías
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&hasSubCategories=false

### Filtrar categorías con ejemplos
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&hasExamples=true

### Filtrar categorías sin ejemplos
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&hasExamples=false

### Filtrar por múltiples criterios
GET {{baseUrl}}/api/category/paged?pageNumber=1&pageSize=10&active=true&hasSubCategories=true&name=ropa 