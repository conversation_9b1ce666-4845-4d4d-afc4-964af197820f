import React, {useMemo, useState} from 'react';
import {
    type ColumnDef,
    type ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    type PaginationState,
    type SortingState,
    useReactTable,
} from '@tanstack/react-table';
import {ArrowDown, ArrowUp, ArrowUpDown, CheckCircle, Edit, Eye, Plus, Trash2, XCircle} from 'lucide-react';
import FuzzySearch from './FuzzySearch';
import TablePagination from './TablePagination';
import ConfirmModal from './ConfirmModal';
import DetailsModal from './DetailsModal';
import FormModal from '../form/FormModal';

// Interfaz base para entidades que extienden BaseEntity
export interface BaseEntity {
    id: number;
    name: string;
    created: string;
    createdString: string;
    createdBy: string;
    updated?: string;
    updatedString?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deleted?: string;
    deletedString?: string;
    deletedBy?: string;
    active: boolean;

    // Propiedades para fechas locales
    createdLocal: string;
    createdLocalString: string;
    updatedLocal?: string;
    updatedLocalString?: string;
    deletedLocal?: string;
    deletedLocalString?: string;
}

export interface CreateBaseEntityDto {
    name: string;
    active?: boolean;
}

export interface UpdateBaseEntityDto {
    id: number;
    name: string;
    active?: boolean;
}

// Props para el componente de tabla genérico de BaseEntity
interface BaseEntityTableProps<
    T extends BaseEntity,
    TCreate extends CreateBaseEntityDto,
    TUpdate extends UpdateBaseEntityDto
> {
    // Datos y estado
    data: T[];
    isLoading: boolean;
    totalRecords: number;
    pageNumber: number;
    pageSize: number;

    // Callbacks
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
    onSearch: (searchTerm: string) => void;
    onCreate: (data: TCreate) => Promise<void>;
    onUpdate: (id: number, data: TUpdate) => Promise<void>;
    onDelete: (id: number) => Promise<void>;
    onActivate?: (id: number) => Promise<void>;
    onDeactivate?: (id: number) => Promise<void>;

    // Configuración
    entityName: string;
    entityNamePlural: string;
    searchPlaceholder?: string;
    showActiveToggle?: boolean;

    // Configuración de columnas personalizadas
    customColumns?: ColumnDef<T>[];

    // Componentes personalizados
    renderFormFields?: (data?: T) => React.ReactNode;
    renderFields?: (data: T) => React.ReactNode;
    renderActions?: (data: T) => React.ReactNode;
    additionalActions?: (data: T) => React.ReactNode;
}

export function BaseEntityTable<
    T extends BaseEntity,
    TCreate extends CreateBaseEntityDto,
    TUpdate extends UpdateBaseEntityDto
>({
      data,
      isLoading,
      totalRecords,
      pageNumber,
      pageSize,
      onPageChange,
      onPageSizeChange,
      onSearch,
      onCreate,
      onUpdate,
      onDelete,
      onActivate,
      onDeactivate,
      entityName,
      entityNamePlural,
      searchPlaceholder = `Buscar ${entityNamePlural.toLowerCase()}...`,
      customColumns = [],
      renderFormFields,
      renderFields,
      renderActions,
      additionalActions,
  }: BaseEntityTableProps<T, TCreate, TUpdate>) {
    // Estados locales
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    const [globalFilter, setGlobalFilter] = useState('');
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: pageNumber - 1,
        pageSize,
    });

    // Estados de modales
    const [isFormModalOpen, setIsFormModalOpen] = useState(false);
    const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState<T | null>(null);
    const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
    const [loading, setLoading] = useState(false);

    // Función de búsqueda fuzzy
    const fuzzyFilter = (row: any, columnId: string, value: string) => {
        const itemValue = row.getValue(columnId);
        if (!value) return true;

        const searchValue = value.toLowerCase();
        const itemString = String(itemValue).toLowerCase();

        return itemString.includes(searchValue);
    };

    // Columnas base que siempre se incluyen
    const baseColumns: ColumnDef<T>[] = [
        {
            accessorKey: 'id',
            header: ({column}) => (
                <button
                    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                    style={{color: 'var(--text-primary)'}}
                >
                    <span>ID</span>
                    {column.getIsSorted() === 'asc' ? (
                        <ArrowUp className="h-4 w-4"/>
                    ) : column.getIsSorted() === 'desc' ? (
                        <ArrowDown className="h-4 w-4"/>
                    ) : (
                        <ArrowUpDown className="h-4 w-4"/>
                    )}
                </button>
            ),
            cell: ({row}) => (
                <span style={{color: 'var(--text-primary)'}}>
          {row.getValue('id')}
        </span>
            ),
            size: 80,
        },
        {
            accessorKey: 'name',
            header: ({column}) => (
                <button
                    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
                    style={{color: 'var(--text-primary)'}}
                >
                    <span>Nombre</span>
                    {column.getIsSorted() === 'asc' ? (
                        <ArrowUp className="h-4 w-4"/>
                    ) : column.getIsSorted() === 'desc' ? (
                        <ArrowDown className="h-4 w-4"/>
                    ) : (
                        <ArrowUpDown className="h-4 w-4"/>
                    )}
                </button>
            ),
            cell: ({row}) => (
                <span style={{color: 'var(--text-primary)'}}>
          {row.getValue('name')}
        </span>
            ),
        },
        {
            accessorKey: 'active',
            header: 'Estado',
            cell: ({row}) => {
                const isActive = row.getValue('active') as boolean;
                return (
                    <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
            }`}>
              {isActive ? 'Activo' : 'Inactivo'}
            </span>
                    </div>
                );
            },
            size: 100,
        },
    ];

    // Definición de columnas combinando base y personalizadas
    const columns = useMemo<ColumnDef<T>[]>(
        () => [
            ...baseColumns,
            ...customColumns,
            {
                id: 'actions',
                header: 'Acciones',
                cell: ({row}) => {
                    const item = row.original;
                    return renderActions ? (
                        renderActions(item)
                    ) : (
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => handleViewDetails(item)}
                                className="p-1 text-blue-600 hover:text-blue-800 transition-colors duration-200"
                                title="Ver detalles"
                            >
                                <Eye className="h-4 w-4"/>
                            </button>
                            <button
                                onClick={() => handleEdit(item)}
                                className="p-1 text-green-600 hover:text-green-800 transition-colors duration-200"
                                title="Editar"
                            >
                                <Edit className="h-4 w-4"/>
                            </button>
                            {/* Acciones adicionales */}
                            {additionalActions && additionalActions(item)}
                            {onActivate && item.active && (
                                <button
                                    onClick={() => handleDeactivate(item)}
                                    className="p-1 text-yellow-600 hover:text-yellow-800 transition-colors duration-200"
                                    title="Desactivar"
                                >
                                    <XCircle className="h-4 w-4"/>
                                </button>
                            )}
                            {onDeactivate && !item.active && (
                                <button
                                    onClick={() => handleActivate(item)}
                                    className="p-1 text-green-600 hover:text-green-800 transition-colors duration-200"
                                    title="Activar"
                                >
                                    <CheckCircle className="h-4 w-4"/>
                                </button>
                            )}
                            <button
                                onClick={() => handleDelete(item)}
                                className="p-1 text-red-600 hover:text-red-800 transition-colors duration-200"
                                title="Eliminar"
                            >
                                <Trash2 className="h-4 w-4"/>
                            </button>
                        </div>
                    );
                },
                size: 150,
            },
        ],
        [customColumns, renderActions, onActivate, onDeactivate, additionalActions]
    );

    // Configuración de la tabla
    const table = useReactTable({
        data,
        columns,
        state: {
            sorting,
            columnFilters,
            globalFilter,
            pagination,
        },
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        onPaginationChange: setPagination,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        globalFilterFn: fuzzyFilter,
        enableSorting: true,
        enableColumnFilters: true,
        enableGlobalFilter: true,
    });

    // Handlers
    const handleCreate = () => {
        setFormMode('create');
        setSelectedItem(null);
        setIsFormModalOpen(true);
    };

    const handleEdit = (item: T) => {
        setFormMode('edit');
        setSelectedItem(item);
        setIsFormModalOpen(true);
    };

    const handleViewDetails = (item: T) => {
        setSelectedItem(item);
        setIsDetailsModalOpen(true);
    };

    const handleDelete = (item: T) => {
        setSelectedItem(item);
        setIsDeleteModalOpen(true);
    };

    const handleActivate = async (item: T) => {
        if (!onActivate) return;

        setLoading(true);
        try {
            await onActivate(item.id);
        } catch (error) {
            console.error('Error al activar:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleDeactivate = async (item: T) => {
        if (!onDeactivate) return;

        setLoading(true);
        try {
            await onDeactivate(item.id);
        } catch (error) {
            console.error('Error al desactivar:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleFormSubmit = async (formData: any) => {
        setLoading(true);
        try {
            if (formMode === 'create') {
                await onCreate(formData);
            } else if (selectedItem) {
                await onUpdate(selectedItem.id, formData);
            }
            setIsFormModalOpen(false);
        } catch (error) {
            console.error('Error en formulario:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleConfirmDelete = async () => {
        if (!selectedItem) return;

        setLoading(true);
        try {
            await onDelete(selectedItem.id);
            setIsDeleteModalOpen(false);
        } catch (error) {
            console.error('Error al eliminar:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (value: string) => {
        setGlobalFilter(value);
        onSearch(value);
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                            {entityNamePlural}
                        </h2>
                        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Gestiona los {entityNamePlural.toLowerCase()} del sistema
                        </p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3">
                        <FuzzySearch
                            value={globalFilter}
                            onChange={handleSearch}
                            placeholder={searchPlaceholder}
                        />

                        <button
                            onClick={handleCreate}
                            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                        >
                            <Plus className="h-4 w-4 mr-2"/>
                            Crear {entityName}
                        </button>
                    </div>
                </div>
            </div>

            {/* Tabla */}
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                    {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map(header => (
                                <th
                                    key={header.id}
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                    style={{width: header.getSize()}}
                                >
                                    {header.isPlaceholder
                                        ? null
                                        : flexRender(
                                            header.column.columnDef.header,
                                            header.getContext()
                                        )}
                                </th>
                            ))}
                        </tr>
                    ))}
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {isLoading ? (
                        <tr>
                            <td colSpan={columns.length}
                                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                Cargando...
                            </td>
                        </tr>
                    ) : table.getRowModel().rows.length === 0 ? (
                        <tr>
                            <td colSpan={columns.length}
                                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                No se encontraron {entityNamePlural.toLowerCase()}
                            </td>
                        </tr>
                    ) : (
                        table.getRowModel().rows.map(row => (
                            <tr key={row.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                {row.getVisibleCells().map(cell => (
                                    <td key={cell.id} className="px-6 py-4 whitespace-nowrap text-sm">
                                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                    </td>
                                ))}
                            </tr>
                        ))
                    )}
                    </tbody>
                </table>
            </div>

            {/* Paginación */}
            <TablePagination
                currentPage={pageNumber}
                totalPages={Math.ceil(totalRecords / pageSize)}
                pageSize={pageSize}
                onPageChange={onPageChange}
                onPageSizeChange={onPageSizeChange}
                totalItems={totalRecords}
            />

            {/* Modales */}
            <FormModal
                isOpen={isFormModalOpen}
                onClose={() => setIsFormModalOpen(false)}
                title={formMode === 'create' ? `Crear ${entityName}` : `Editar ${entityName}`}
                onSubmit={handleFormSubmit}
                loading={loading}
                renderFields={renderFormFields}
                data={selectedItem}
                mode={formMode}
            />

            <DetailsModal
                isOpen={isDetailsModalOpen}
                onClose={() => setIsDetailsModalOpen(false)}
                title={`Detalles del ${entityName}`}
                data={selectedItem}
                renderContent={renderFields}
            />

            <ConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                title={`Eliminar ${entityName}`}
                message={`¿Estás seguro de que quieres eliminar este ${entityName.toLowerCase()}? Esta acción no se puede deshacer.`}
                loading={loading}
            />
        </div>
    );
} 