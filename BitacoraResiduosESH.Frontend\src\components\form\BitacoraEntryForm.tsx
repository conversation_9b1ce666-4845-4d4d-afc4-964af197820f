import React from 'react';
import { SearchableSelect, type SearchableSelectOption } from '../ui';
import type { BitacoraEntry } from '@/types/bitacoraEntry';

interface BitacoraEntryFormProps {
    data?: BitacoraEntry;
    areaOptions: SearchableSelectOption[];
    wasteTypeOptions: SearchableSelectOption[];
    containerTypeOptions: SearchableSelectOption[];
    onAreaSearch: (searchTerm: string) => void;
    onWasteTypeSearch: (searchTerm: string) => void;
    onContainerTypeSearch: (searchTerm: string) => void;
}

export const BitacoraEntryForm: React.FC<BitacoraEntryFormProps> = ({
    data,
    areaOptions,
    wasteTypeOptions,
    containerTypeOptions,
    onAreaSearch,
    onWasteTypeSearch,
    onContainerTypeSearch,
}) => {
    // Estados locales para los selects
    let selectedAreaId = data?.areaId;
    let selectedWasteTypeId = data?.wasteTypeId;
    let selectedContainerTypeId = data?.containerTypeId;

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Comentarios */}
                <div>
                    <label htmlFor="comments" className="block text-sm font-medium mb-1">
                        Comentarios
                    </label>
                    <input
                        type="text"
                        id="comments"
                        name="comments"
                        defaultValue={data?.comments || ''}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Comentarios opcionales"
                    />
                </div>

                {/* Tipo de Residuo */}
                <div>
                    <SearchableSelect
                        label="Tipo de Residuo"
                        required
                        options={wasteTypeOptions}
                        value={selectedWasteTypeId}
                        onChange={(value) => {
                            selectedWasteTypeId = value;
                            const input = document.querySelector('input[name="wasteTypeId"]') as HTMLInputElement;
                            if (input) input.value = value?.toString() || '';
                        }}
                        onSearch={onWasteTypeSearch}
                        placeholder="Seleccionar tipo de residuo..."
                    />
                    <input type="hidden" name="wasteTypeId" defaultValue={data?.wasteTypeId || ''} required />
                </div>

                {/* Área */}
                <div>
                    <SearchableSelect
                        label="Área"
                        required
                        options={areaOptions}
                        value={selectedAreaId}
                        onChange={(value) => {
                            selectedAreaId = value;
                            const input = document.querySelector('input[name="areaId"]') as HTMLInputElement;
                            if (input) input.value = value?.toString() || '';
                        }}
                        onSearch={onAreaSearch}
                        placeholder="Seleccionar área..."
                    />
                    <input type="hidden" name="areaId" defaultValue={data?.areaId || ''} required />
                </div>

                {/* Tipo de Contenedor */}
                <div>
                    <SearchableSelect
                        label="Tipo de Contenedor"
                        required
                        options={containerTypeOptions}
                        value={selectedContainerTypeId}
                        onChange={(value) => {
                            selectedContainerTypeId = value;
                            const input = document.querySelector('input[name="containerTypeId"]') as HTMLInputElement;
                            if (input) input.value = value?.toString() || '';
                        }}
                        onSearch={onContainerTypeSearch}
                        placeholder="Seleccionar tipo de contenedor..."
                    />
                    <input type="hidden" name="containerTypeId" defaultValue={data?.containerTypeId || ''} required />
                </div>

                {/* Peso Bruto */}
                <div>
                    <label htmlFor="grossWeight" className="block text-sm font-medium mb-1">
                        Peso Bruto (kg) *
                    </label>
                    <input
                        type="number"
                        id="grossWeight"
                        name="grossWeight"
                        step="0.01"
                        min="0"
                        defaultValue={data?.grossWeight || ''}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0.00"
                    />
                </div>

                {/* Tara */}
                <div>
                    <label htmlFor="tare" className="block text-sm font-medium mb-1">
                        Tara (kg) *
                    </label>
                    <input
                        type="number"
                        id="tare"
                        name="tare"
                        step="0.01"
                        min="0"
                        defaultValue={data?.tare || ''}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0.00"
                    />
                </div>

                {/* Peso Neto LB */}
                <div>
                    <label htmlFor="netWeightLB" className="block text-sm font-medium mb-1">
                        Peso Neto (lb) *
                    </label>
                    <input
                        type="number"
                        id="netWeightLB"
                        name="netWeightLB"
                        step="0.01"
                        min="0"
                        defaultValue={data?.netWeightLB || ''}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0.00"
                    />
                </div>

                {/* Peso Neto KG */}
                <div>
                    <label htmlFor="netWeightKG" className="block text-sm font-medium mb-1">
                        Peso Neto (kg) *
                    </label>
                    <input
                        type="number"
                        id="netWeightKG"
                        name="netWeightKG"
                        step="0.01"
                        min="0"
                        defaultValue={data?.netWeightKG || ''}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="0.00"
                    />
                </div>
            </div>
        </div>
    );
};
