﻿namespace BitacoraResiduosESH.Backend.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ContainerTypesController(IContainerTypeService service)
    : SimpleEntityController<ContainerType, ContainerTypeDto, CreateContainerTypeDto, 
        UpdateContainerTypeDto, ContainerTypeFilterDto>(service)
{
    // No se agregan métodos adicionales
    // Solo hereda todos los endpoints de SimpleEntityController:
    // - GET /api/containerType/{id} - Obtener por ID
    // - GET /api/containerType - Obtener todos
    // - GET /api/containerType/paged - Obtener paginados
    // - GET /api/containerType/active - Obtener activos
    // - GET /api/containerType/search/name - Buscar por nombre
    // - GET /api/containerType/search/description - Buscar por descripción
    // - POST /api/containerType - Crear
    // - PUT /api/containerType/{id} - Actualizar
    // - DELETE /api/containerType/{id} - Eliminar (soft delete)
    // - DELETE /api/containerType/{id}/permanent - Eliminar permanentemente
    // - PATCH /api/containerType/{id}/activate - Activar
    // - PATCH /api/containerType/{id}/deactivate - Desactivar
    // - GET /api/containerType/{id}/exists - Verificar existencia
    // - Manejo de errores automático
    // - Logging automático
    // - Validaciones automáticas
}