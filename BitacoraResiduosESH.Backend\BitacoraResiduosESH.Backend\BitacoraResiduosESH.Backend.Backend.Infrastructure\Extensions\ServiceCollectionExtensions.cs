using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<IRoleRepository, RoleRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IAreaRepository, AreaRepository>();
        services.AddScoped<IContainerTypeRepository, ContainerTypeRepository>();
        services.AddScoped<IWasteTypeRepository, WasteTypeRepository>();
        services.AddScoped<IBitacoraEntryRepository, BitacoraEntryRepository>();

        return services;
    }

    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Registrar servicios de aplicación
        services.AddScoped<IRoleService, RoleService>();
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IAreaService, AreaService>();
        services.AddScoped<IContainerTypeService, ContainerTypeService>();
        services.AddScoped<IWasteTypeService, WasteTypeService>();
        services.AddScoped<IBitacoraEntryService, BitacoraEntryService>();

        return services;
    }

    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services)
    {
        // Registrar servicios de infraestructura
        services.AddScoped<IOpenIdConnectService, OpenIdConnectService>();
        services.AddScoped<IPasswordService, PasswordService>();
        services.AddScoped<IJwtService, JwtService>();
        services.AddScoped<IAuthService, AuthService>();

        // Configurar HttpClient para OpenID Connect
        services.AddHttpClient<OpenIdConnectService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
            client.DefaultRequestHeaders.Add("User-Agent", "ToolsMS.Backend/1.0");
        });

        // Aquí puedes agregar más servicios de infraestructura según los necesites
        // services.AddScoped<IEmailService, EmailService>();
        // services.AddScoped<IFileStorageService, FileStorageService>();
        // services.AddScoped<ICacheService, CacheService>();
        // etc.

        return services;
    }

    public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        // Obtener la cadena de conexión
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        if (string.IsNullOrEmpty(connectionString))
            // Fallback a una cadena de conexión por defecto
            connectionString = "Data Source=app.db";

        // Configurar Entity Framework con SQLite
        services.AddDbContext<AppDbContext>(options =>
        {
            options.UseSqlite(connectionString, sqliteOptions =>
            {
                // Configuraciones específicas de SQLite
                sqliteOptions.MigrationsAssembly(typeof(AppDbContext).Assembly.GetName().Name);
            });

            // Configuraciones adicionales para desarrollo
        });

        return services;
    }

    public static IServiceCollection AddNLog(this IServiceCollection services, IConfiguration configuration)
    {
        // Configurar NLog
        LogManager.Setup().LoadConfigurationFromFile("nlog.config");

        // Configurar el logging de ASP.NET Core para usar NLog
        services.AddLogging(loggingBuilder =>
        {
            loggingBuilder.ClearProviders();
            loggingBuilder.SetMinimumLevel(LogLevel.Trace);
            loggingBuilder.AddNLog();
        });

        return services;
    }

    /// <summary>
    /// Inicializa la base de datos con datos de prueba
    /// </summary>
    public static async Task InitializeDatabaseAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
        var passwordService = scope.ServiceProvider.GetRequiredService<IPasswordService>();

        try
        {
            logger.LogInformation("Inicializando base de datos con datos de prueba...");

            // Verificar si ya existen datos
            if (await context.Roles.AnyAsync())
            {
                logger.LogInformation("La base de datos ya contiene datos. Saltando inicialización.");
                return;
            }

            // Crear rol Admin
            var adminRole = new Role
            {
                Name = "Admin",
                Description = "Administrador del sistema con acceso completo",
                CreatedBy = "system",
                Active = true
            };

            context.Roles.Add(adminRole);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("Rol Admin creado exitosamente");

            // Crear usuario administrador
            var adminUser = new User
            {
                Name = "Maximiliano Ponce",
                EmployeeNumber = "123456",
                Email = "<EMAIL>",
                Password = passwordService.HashPassword("123456"),
                RoleId = adminRole.Id,
                CreatedBy = "system",
                Active = true
            };

            context.User.Add(adminUser);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("Usuario administrador creado exitosamente");

            // Crear Areas
            var areas = new List<Area>
            {
                new Area
                {
                    Name = "PSBU",
                    Description = "Precision Solutions Business Unit",
                    CreatedBy = "system",
                    Active = true
                },
                new Area
                {
                    Name = "Medico",
                    Description = "Área Médica",
                    CreatedBy = "system",
                    Active = true
                },
                new Area
                {
                    Name = "Industrial",
                    Description = "Área Industrial",
                    CreatedBy = "system",
                    Active = true
                }
            };

            context.Areas.AddRange(areas);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("Areas creadas exitosamente");

            // Crear WasteTypes
            var wasteTypes = new List<WasteType>
            {
                new WasteType
                {
                    Name = "Cobre",
                    Description = "Residuos de cobre",
                    CreatedBy = "system",
                    Active = true
                },
                new WasteType
                {
                    Name = "Cuin 26",
                    Description = "Residuos de Cuin 26",
                    CreatedBy = "system",
                    Active = true
                },
                new WasteType
                {
                    Name = "Cuin 23",
                    Description = "Residuos de Cuin 23",
                    CreatedBy = "system",
                    Active = true
                },
                new WasteType
                {
                    Name = "Bronce",
                    Description = "Residuos de bronce",
                    CreatedBy = "system",
                    Active = true
                }
            };

            context.WasteTypes.AddRange(wasteTypes);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("WasteTypes creados exitosamente");

            // Crear ContainerTypes
            var containerTypes = new List<ContainerType>
            {
                new ContainerType
                {
                    Name = "Gaylor",
                    Description = "Contenedor tipo Gaylor",
                    CreatedBy = "system",
                    Active = true
                }
            };

            context.ContainerTypes.AddRange(containerTypes);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("ContainerTypes creados exitosamente");

            // Crear BitacoraEntries de prueba
            var bitacoraEntries = new List<BitacoraEntry>
            {
                new BitacoraEntry
                {
                    Comments = "Primera entrada de residuos de cobre en PSBU",
                    WasteTypeId = wasteTypes.First(w => w.Name == "Cobre").Id,
                    GrossWeight = 150.50m,
                    Tare = 25.00m,
                    NetWeightLB = 276.68m,
                    NetWeightKG = 125.50m,
                    UnitPrice = 8.75m,
                    ContainerTypeId = containerTypes.First(c => c.Name == "Gaylor").Id,
                    AreaId = areas.First(a => a.Name == "PSBU").Id,
                    EntryDate = DateTime.UtcNow.AddDays(-10),
                    DepartureDate = DateTime.UtcNow.AddDays(-5),
                    EnteredBy = "Maximiliano Ponce",
                    CreatedBy = "system",
                    Active = true
                },
                new BitacoraEntry
                {
                    Comments = "Primera entrada de residuos de Cuin 26 en área médica",
                    WasteTypeId = wasteTypes.First(w => w.Name == "Cuin 26").Id,
                    GrossWeight = 89.25m,
                    Tare = 15.00m,
                    NetWeightLB = 163.80m,
                    NetWeightKG = 74.25m,
                    UnitPrice = 12.50m,
                    ContainerTypeId = containerTypes.First(c => c.Name == "Gaylor").Id,
                    AreaId = areas.First(a => a.Name == "Medico").Id,
                    EntryDate = DateTime.UtcNow.AddDays(-8),
                    DepartureDate = null, // Aún no ha salido
                    EnteredBy = "Maximiliano Ponce",
                    CreatedBy = "system",
                    Active = true
                },
                new BitacoraEntry
                {
                    Comments = "Primera entrada de residuos de bronce en área industrial",
                    WasteTypeId = wasteTypes.First(w => w.Name == "Bronce").Id,
                    GrossWeight = 200.75m,
                    Tare = 30.00m,
                    NetWeightLB = 376.65m,
                    NetWeightKG = 170.75m,
                    UnitPrice = 6.25m,
                    ContainerTypeId = containerTypes.First(c => c.Name == "Gaylor").Id,
                    AreaId = areas.First(a => a.Name == "Industrial").Id,
                    EntryDate = DateTime.UtcNow.AddDays(-6),
                    DepartureDate = DateTime.UtcNow.AddDays(-2),
                    EnteredBy = "Maximiliano Ponce",
                    CreatedBy = "system",
                    Active = true
                },
                new BitacoraEntry
                {
                    Comments = "Primera entrada de residuos de Cuin 23 en PSBU",
                    WasteTypeId = wasteTypes.First(w => w.Name == "Cuin 23").Id,
                    GrossWeight = 95.80m,
                    Tare = 20.00m,
                    NetWeightLB = 167.11m,
                    NetWeightKG = 75.80m,
                    UnitPrice = 15.75m,
                    ContainerTypeId = containerTypes.First(c => c.Name == "Gaylor").Id,
                    AreaId = areas.First(a => a.Name == "PSBU").Id,
                    EntryDate = DateTime.UtcNow.AddDays(-3),
                    DepartureDate = null, // Aún no ha salido
                    EnteredBy = "Maximiliano Ponce",
                    CreatedBy = "system",
                    Active = true
                }
            };

            context.BitacoraEntries.AddRange(bitacoraEntries);
            await context.SaveChangesAsync(true, "system");

            logger.LogInformation("BitacoraEntries de prueba creadas exitosamente");
            logger.LogInformation("Inicialización de base de datos completada exitosamente");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error al inicializar la base de datos con datos de prueba");
            throw;
        }
    }
}