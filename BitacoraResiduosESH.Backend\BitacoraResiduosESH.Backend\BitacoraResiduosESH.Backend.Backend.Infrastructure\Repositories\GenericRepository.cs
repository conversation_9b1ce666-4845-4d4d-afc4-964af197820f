﻿using BitacoraResiduosESH.Backend.Backend.Infrastructure.Data;

namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories;

public class GenericRepository<T>(AppDbContext context) : IGenericRepository<T>
    where T : BaseEntity
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    protected readonly DbSet<T> DbSet = context.Set<T>();

    #region Operaciones de lectura

    public async Task<T?> GetByIdAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Buscando entidad {EntityType} con ID: {Id}, IncludeDeleted: {IncludeDeleted}", typeof(T).Name, id,
            includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var result = await query.FirstOrDefaultAsync(e => e.Id == id);

        Logger.Debug("Búsqueda completada para {EntityType} con ID {Id}: {Found}", typeof(T).Name, id, result != null);

        return result;
    }

    public async Task<PagedResponse<T>> GetAllAsync(PaginationFilter filter)
    {
        Logger.Debug(
            "Obteniendo entidades {EntityType} paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(T).Name, filter.PageNumber, filter.PageSize, filter.IncludeDeleted);

        var query = DbSet.AsQueryable();

        if (!filter.IncludeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug("Recuperadas {Count} entidades {EntityType} de {TotalRecords} totales",
            data.Count, typeof(T).Name, totalRecords);

        return new PagedResponse<T>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<PagedResponse<T>> GetPagedAsync(PaginationFilter filter)
    {
        Logger.Debug(
            "Obteniendo entidades {EntityType} paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(T).Name, filter.PageNumber, filter.PageSize, filter.IncludeDeleted);

        var query = DbSet.AsQueryable();

        if (!filter.IncludeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Paginación completada para {EntityType} - Total: {TotalRecords}, Página: {PageNumber}, Elementos: {Count}",
            typeof(T).Name, totalRecords, filter.PageNumber, data.Count);

        return new PagedResponse<T>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<PagedResponse<T>> GetActiveAsync(PaginationFilter filter)
    {
        Logger.Debug("Obteniendo entidades {EntityType} activas paginadas - Página: {PageNumber}, Tamaño: {PageSize}",
            typeof(T).Name, filter.PageNumber, filter.PageSize);

        var query = DbSet
            .Where(e => e.Active && !e.IsDeleted);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug("Recuperadas {Count} entidades {EntityType} activas de {TotalRecords} totales",
            data.Count, typeof(T).Name, totalRecords);

        return new PagedResponse<T>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<bool> ExistsAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Verificando existencia de entidad {EntityType} con ID: {Id}, IncludeDeleted: {IncludeDeleted}",
            typeof(T).Name, id, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var exists = await query.AnyAsync(e => e.Id == id);

        Logger.Debug("Verificación de existencia completada para {EntityType} con ID {Id}: {Exists}", typeof(T).Name,
            id, exists);

        return exists;
    }

    public async Task<int> CountAsync(bool includeDeleted = false)
    {
        Logger.Debug("Contando entidades {EntityType}, IncludeDeleted: {IncludeDeleted}", typeof(T).Name,
            includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var count = await query.CountAsync();

        Logger.Debug("Conteo completado para {EntityType}: {Count} entidades", typeof(T).Name, count);

        return count;
    }

    #endregion

    #region Operaciones de escritura

    public async Task<T> AddAsync(T entity)
    {
        Logger.Debug("Agregando nueva entidad {EntityType}", typeof(T).Name);

        entity.Created = DateTime.Now;
        entity.Active = true;
        entity.IsDeleted = false;

        await DbSet.AddAsync(entity);
        await context.SaveChangesAsync(true, entity.CreatedBy ?? "system");

        Logger.Info("Entidad {EntityType} agregada exitosamente con ID: {Id}", typeof(T).Name, entity.Id);

        return entity;
    }

    public async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
    {
        var entityList = entities.ToList();
        Logger.Debug("Agregando {Count} entidades {EntityType}", entityList.Count, typeof(T).Name);

        var now = DateTime.Now;

        foreach (var entity in entityList)
        {
            entity.Created = now;
            entity.Active = true;
            entity.IsDeleted = false;
        }

        await DbSet.AddRangeAsync(entityList);
        await context.SaveChangesAsync(true, entityList.FirstOrDefault()?.CreatedBy ?? "system");

        Logger.Info("{Count} entidades {EntityType} agregadas exitosamente", entityList.Count, typeof(T).Name);

        return entityList;
    }

    public async Task<T> UpdateAsync(T entity)
    {
        Logger.Debug("Actualizando entidad {EntityType} con ID: {Id}", typeof(T).Name, entity.Id);

        entity.Updated = DateTime.Now;

        DbSet.Update(entity);
        await context.SaveChangesAsync(true, entity.UpdatedBy ?? "system");

        Logger.Info("Entidad {EntityType} con ID {Id} actualizada exitosamente", typeof(T).Name, entity.Id);

        return entity;
    }

    public async Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities)
    {
        var entityList = entities.ToList();
        Logger.Debug("Actualizando {Count} entidades {EntityType}", entityList.Count, typeof(T).Name);

        var now = DateTime.Now;

        foreach (var entity in entityList) entity.Updated = now;

        DbSet.UpdateRange(entityList);
        await context.SaveChangesAsync(true, entityList.FirstOrDefault()?.UpdatedBy ?? "system");

        Logger.Info("{Count} entidades {EntityType} actualizadas exitosamente", entityList.Count, typeof(T).Name);

        return entityList;
    }

    #endregion

    #region Operaciones de eliminación

    public async Task<bool> DeleteAsync(int id, string deletedBy)
    {
        Logger.Debug("Eliminando entidad {EntityType} con ID: {Id} por usuario: {User}", typeof(T).Name, id, deletedBy);

        var entity = await GetByIdAsync(id, true);
        if (entity == null)
        {
            Logger.Warn("Entidad {EntityType} con ID {Id} no encontrada para eliminar", typeof(T).Name, id);
            return false;
        }

        return await DeleteAsync(entity, deletedBy);
    }

    public async Task<bool> DeleteAsync(T entity, string deletedBy)
    {
        Logger.Debug("Eliminando entidad {EntityType} con ID: {Id} por usuario: {User}", typeof(T).Name, entity.Id,
            deletedBy);

        entity.IsDeleted = true;
        entity.Deleted = DateTime.Now;
        entity.DeletedBy = deletedBy;
        entity.Active = false;

        DbSet.Update(entity);
        await context.SaveChangesAsync(true, deletedBy);

        Logger.Info("Entidad {EntityType} con ID {Id} eliminada exitosamente por usuario: {User}", typeof(T).Name,
            entity.Id, deletedBy);

        return true;
    }

    public async Task<bool> DeleteRangeAsync(IEnumerable<int> ids, string deletedBy)
    {
        var idList = ids.ToList();
        Logger.Debug("Eliminando {Count} entidades {EntityType} por usuario: {User}", idList.Count, typeof(T).Name,
            deletedBy);

        var entities = new List<T>();

        foreach (var id in idList)
        {
            var entity = await GetByIdAsync(id, true);
            if (entity != null)
                entities.Add(entity);
        }

        return await DeleteRangeAsync(entities, deletedBy);
    }

    public async Task<bool> DeleteRangeAsync(IEnumerable<T> entities, string deletedBy)
    {
        var entityList = entities.ToList();
        if (!entityList.Any())
        {
            Logger.Warn("No hay entidades {EntityType} para eliminar", typeof(T).Name);
            return false;
        }

        Logger.Debug("Eliminando {Count} entidades {EntityType} por usuario: {User}", entityList.Count, typeof(T).Name,
            deletedBy);

        var now = DateTime.Now;

        foreach (var entity in entityList)
        {
            entity.IsDeleted = true;
            entity.Deleted = now;
            entity.DeletedBy = deletedBy;
            entity.Active = false;
        }

        DbSet.UpdateRange(entityList);
        await context.SaveChangesAsync(true, deletedBy);

        Logger.Info("{Count} entidades {EntityType} eliminadas exitosamente por usuario: {User}", entityList.Count,
            typeof(T).Name, deletedBy);

        return true;
    }

    public async Task<bool> HardDeleteAsync(int id)
    {
        Logger.Debug("Eliminación permanente de entidad {EntityType} con ID: {Id}", typeof(T).Name, id);

        var entity = await GetByIdAsync(id, true);
        if (entity == null)
        {
            Logger.Warn("Entidad {EntityType} con ID {Id} no encontrada para eliminación permanente", typeof(T).Name,
                id);
            return false;
        }

        return await HardDeleteAsync(entity);
    }

    public async Task<bool> HardDeleteAsync(T entity)
    {
        Logger.Debug("Eliminación permanente de entidad {EntityType} con ID: {Id}", typeof(T).Name, entity.Id);

        DbSet.Remove(entity);
        await context.SaveChangesAsync();

        Logger.Info("Entidad {EntityType} con ID {Id} eliminada permanentemente", typeof(T).Name, entity.Id);

        return true;
    }

    public async Task<bool> HardDeleteRangeAsync(IEnumerable<int> ids)
    {
        var idList = ids.ToList();
        Logger.Debug("Eliminación permanente de {Count} entidades {EntityType}", idList.Count, typeof(T).Name);

        var entities = new List<T>();

        foreach (var id in idList)
        {
            var entity = await GetByIdAsync(id, true);
            if (entity != null)
                entities.Add(entity);
        }

        return await HardDeleteRangeAsync(entities);
    }

    public async Task<bool> HardDeleteRangeAsync(IEnumerable<T> entities)
    {
        var entityList = entities.ToList();
        if (!entityList.Any())
        {
            Logger.Warn("No hay entidades {EntityType} para eliminación permanente", typeof(T).Name);
            return false;
        }

        Logger.Debug("Eliminación permanente de {Count} entidades {EntityType}", entityList.Count, typeof(T).Name);

        DbSet.RemoveRange(entityList);
        await context.SaveChangesAsync();

        Logger.Info("{Count} entidades {EntityType} eliminadas permanentemente", entityList.Count, typeof(T).Name);

        return true;
    }

    #endregion

    #region Operaciones de activación/desactivación

    public async Task<bool> ActivateAsync(int id, string updatedBy)
    {
        Logger.Debug("Activando entidad {EntityType} con ID: {Id} por usuario: {User}", typeof(T).Name, id, updatedBy);

        var entity = await GetByIdAsync(id, true);
        if (entity == null)
        {
            Logger.Warn("Entidad {EntityType} con ID {Id} no encontrada para activar", typeof(T).Name, id);
            return false;
        }

        return await ActivateAsync(entity, updatedBy);
    }

    public async Task<bool> DeactivateAsync(int id, string updatedBy)
    {
        Logger.Debug("Desactivando entidad {EntityType} con ID: {Id} por usuario: {User}", typeof(T).Name, id,
            updatedBy);

        var entity = await GetByIdAsync(id, true);
        if (entity == null)
        {
            Logger.Warn("Entidad {EntityType} con ID {Id} no encontrada para desactivar", typeof(T).Name, id);
            return false;
        }

        return await DeactivateAsync(entity, updatedBy);
    }

    public async Task<bool> ActivateAsync(T entity, string updatedBy)
    {
        Logger.Debug("Activando entidad {EntityType} con ID: {Id} por usuario: {User}", typeof(T).Name, entity.Id,
            updatedBy);

        entity.Active = true;
        entity.Updated = DateTime.Now;
        entity.UpdatedBy = updatedBy;

        DbSet.Update(entity);
        await context.SaveChangesAsync(true, updatedBy);

        Logger.Info("Entidad {EntityType} con ID {Id} activada exitosamente por usuario: {User}", typeof(T).Name,
            entity.Id, updatedBy);

        return true;
    }

    public async Task<bool> DeactivateAsync(T entity, string updatedBy)
    {
        Logger.Debug("Desactivando entidad {EntityType} con ID: {Id} por usuario: {User}", typeof(T).Name, entity.Id,
            updatedBy);

        entity.Active = false;
        entity.Updated = DateTime.Now;
        entity.UpdatedBy = updatedBy;

        DbSet.Update(entity);
        await context.SaveChangesAsync(true, updatedBy);

        Logger.Info("Entidad {EntityType} con ID {Id} desactivada exitosamente por usuario: {User}", typeof(T).Name,
            entity.Id, updatedBy);

        return true;
    }

    #endregion

    #region Operaciones de búsqueda

    public async Task<PagedResponse<T>> FindAsync(Expression<Func<T, bool>> predicate, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Buscando entidades {EntityType} con predicado paginadas - Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            typeof(T).Name, filter.PageNumber, filter.PageSize, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        // Aplicar el predicado
        query = query.Where(predicate);

        var totalRecords = await query.CountAsync();

        var data = await query
            .OrderBy(e => e.Id)
            .Skip((filter.PageNumber - 1) * filter.PageSize)
            .Take(filter.PageSize)
            .ToListAsync();

        Logger.Debug(
            "Búsqueda con predicado paginada completada para {EntityType}: {Count} resultados de {TotalRecords} totales",
            typeof(T).Name, data.Count, totalRecords);

        return new PagedResponse<T>
        {
            PageNumber = filter.PageNumber,
            PageSize = filter.PageSize,
            TotalRecords = totalRecords,
            Data = data
        };
    }

    public async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        Logger.Debug("Buscando primera entidad {EntityType} con predicado, IncludeDeleted: {IncludeDeleted}",
            typeof(T).Name, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var result = await query.FirstOrDefaultAsync(predicate);

        Logger.Debug("Búsqueda FirstOrDefault completada para {EntityType}: {Found}", typeof(T).Name, result != null);

        return result;
    }

    public async Task<T> FirstAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false)
    {
        Logger.Debug("Buscando primera entidad {EntityType} con predicado, IncludeDeleted: {IncludeDeleted}",
            typeof(T).Name, includeDeleted);

        var query = DbSet.AsQueryable();

        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);

        var result = await query.FirstAsync(predicate);

        Logger.Debug("Búsqueda First completada para {EntityType}", typeof(T).Name);

        return result;
    }

    #endregion
}