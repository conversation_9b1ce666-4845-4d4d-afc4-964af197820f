# Guía para crear una entidad basada en SimpleEntity

Esta guía te permitirá crear una nueva entidad siguiendo el patrón de WasteType, aprovechando la herencia de `SimpleEntity` para evitar trabajo manual y mantener la consistencia.

---

## 1. Dominio

Crea la clase de entidad en `BitacoraResiduosESH.Backend.Backend.Domain/Entities/`:

```csharp
public class MiEntidad : SimpleEntity
{
    // Puedes agregar propiedades adicionales si es necesario
}
```

---

## 2. DTOs

Crea los siguientes archivos en `BitacoraResiduosESH.Backend.Backend.Application/DTOs/MiEntidad/`:

- `CreateMiEntidadDto.cs` (hereda de `CreateSimpleEntityDto`)
- `UpdateMiEntidadDto.cs` (hereda de `UpdateSimpleEntityDto`)
- `MiEntidadDto.cs` (hereda de `SimpleEntityDto`)
- `MiEntidadFilterDto.cs` (hereda de `SimpleEntityFilterDto`)

Ejemplo:

```csharp
public class CreateMiEntidadDto : CreateSimpleEntityDto { }
public class UpdateMiEntidadDto : UpdateSimpleEntityDto { }
public class MiEntidadDto : SimpleEntityDto { }
public class MiEntidadFilterDto : SimpleEntityFilterDto { }
```

---

## 3. Interfaces

### Repositorio

Crea `IMiEntidadRepository.cs` en `Interfaces/Repositories/`:

```csharp
public interface IMiEntidadRepository : ISimpleEntityRepository<MiEntidad> { }
```

### Servicio

Crea `IMiEntidadService.cs` en `Interfaces/Services/`:

```csharp
public interface IMiEntidadService : ISimpleEntityService<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto> { }
```

---

## 4. Implementaciones

### Servicio

Crea `MiEntidadService.cs` en `Services/`:

```csharp
public class MiEntidadService(IMiEntidadRepository repository)
    : SimpleEntityService<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto>(repository), IMiEntidadService
{ }
```

### Repositorio

Crea `MiEntidadRepository.cs` en `Repositories/`:

```csharp
public class MiEntidadRepository(AppDbContext context)
    : SimpleEntityRepository<MiEntidad>(context), IMiEntidadRepository
{ }
```

---

## 5. Controlador

Crea `MiEntidadController.cs` en `Controllers/`:

```csharp
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class MiEntidadController(IMiEntidadService service)
    : SimpleEntityController<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto>(service)
{ }
```

---

## 6. Configuración de la entidad (ModelConfiguration)

Crea el archivo `MiEntidadConfiguration.cs` en `BitacoraResiduosESH.Backend.Backend.Infrastructure/Data/ModelConfigurations/`:

```csharp
namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;

public class MiEntidadConfiguration: SimpleEntityConfiguration<MiEntidad>
{
    protected override string TableName => "MiEntidads";
}
```

---

## 7. AppDbContext

1. **Agrega el DbSet:**

En `AppDbContext.cs`, agrega la propiedad:

```csharp
public DbSet<MiEntidad> MiEntidades { get; set; }
```

2. **Agrega el ApplyConfiguration:**

En el método `OnModelCreating` de `AppDbContext.cs`, agrega:

```csharp
modelBuilder.ApplyConfiguration(new MiEntidadConfiguration());
```

---

## 8. GlobalUsings

Agrega los nuevos DTOs, entidad, interfaces y servicios a los archivos `GlobalUsings.cs` correspondientes.

---

## 9. ServiceCollectionExtensions

Registra el repositorio y servicio en `AddRepositories` y `AddApplicationServices`:

```csharp
services.AddScoped<IMiEntidadRepository, MiEntidadRepository>();
services.AddScoped<IMiEntidadService, MiEntidadService>();
```

---

## 10. ¡Listo!

Tu nueva entidad tipo SimpleEntity está lista para usarse siguiendo el patrón estándar del proyecto.

---

## Automatización

Puedes usar el script `generar_simple_entity.ps1` incluido en esta carpeta para automatizar este proceso.
