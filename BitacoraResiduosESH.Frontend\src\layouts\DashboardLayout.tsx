import React, {useState} from "react";
import Header from "../components/Header";
import Sidebar from "../components/Sidebar";
import Footer from "../components/Footer";

interface DashboardLayoutProps {
    children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({children}) => {
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    const handleMenuToggle = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    const handleSidebarClose = () => {
        setIsSidebarOpen(false);
    };

    return (
        <div className="min-h-screen flex transition-all duration-200" style={{
            backgroundColor: 'var(--bg-color, #f9fafb)'
        }}>
            {/* Sidebar */}
            <Sidebar isOpen={isSidebarOpen} onClose={handleSidebarClose}/>

            {/* Main content area */}
            <div
                className={`flex-1 flex flex-col min-w-0 transition-all duration-300 ${
                    isSidebarOpen ? 'lg:ml-64' : 'lg:ml-64'
                }`}
            >
                {/* Header */}
                <Header onMenuToggle={handleMenuToggle}/>

                {/* Content */}
                <main className="flex-1 pt-16">
                    <div className="min-h-full flex flex-col">
                        {/* Page content */}
                        <div className="flex-1">
                            {children}
                        </div>

                        {/* Footer */}
                        <Footer/>
                    </div>
                </main>
            </div>
        </div>
    );
};

export default DashboardLayout;
