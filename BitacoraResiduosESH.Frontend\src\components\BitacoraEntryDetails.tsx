import React from 'react';
import type { BitacoraEntry } from '@/types/bitacoraEntry';

interface BitacoraEntryDetailsProps {
    entry: BitacoraEntry;
}

export const BitacoraEntryDetails: React.FC<BitacoraEntryDetailsProps> = ({ entry }) => {
    return (
        <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">ID</label>
                    <p className="text-sm">{entry.id}</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Estado</label>
                    <p className="text-sm">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            entry.active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                        }`}>
                            {entry.active ? 'Activo' : 'Inactivo'}
                        </span>
                    </p>
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Comentarios</label>
                <p className="text-sm">{entry.comments || 'Sin comentarios'}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">Tipo de Residuo</label>
                    <p className="text-sm">{entry.wasteType?.name || 'N/A'}</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Área</label>
                    <p className="text-sm">{entry.area?.name || 'N/A'}</p>
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Tipo de Contenedor</label>
                <p className="text-sm">{entry.containerType?.name || 'N/A'}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">Peso Bruto</label>
                    <p className="text-sm">{entry.grossWeight.toFixed(2)} kg</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Tara</label>
                    <p className="text-sm">{entry.tare.toFixed(2)} kg</p>
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">Peso Neto (lb)</label>
                    <p className="text-sm">{entry.netWeightLB.toFixed(2)} lb</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Peso Neto (kg)</label>
                    <p className="text-sm">{entry.netWeightKG.toFixed(2)} kg</p>
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Precio Unitario</label>
                <p className="text-sm">${entry.unitPrice.toFixed(2)}</p>
            </div>
        </div>
    );
};
