import React from 'react';
import { BitacoraEntriesTable } from '@/components/table/BitacoraEntriesTable';
import { BitacoraEntryForm } from '@/components/form/BitacoraEntryForm';
import { BitacoraEntryDetails } from '@/components/BitacoraEntryDetails';
import { useBitacoraEntryManagement } from '@/hooks/useBitacoraEntryManagement';
import type { BitacoraEntry } from '@/types/bitacoraEntry';

const BitacoraEntries: React.FC = () => {
    const {
        entriesData,
        isLoading,
        areaOptions,
        wasteTypeOptions,
        containerTypeOptions,
        pageNumber,
        pageSize,
        handleAreaSearch,
        handleWasteTypeSearch,
        handleContainerTypeSearch,
        handleCreate,
        handleUpdate,
        handleDelete,
        handleActivate,
        handleDeactivate,
        handlePageChange,
        handlePageSizeChange,
    } = useBitacoraEntryManagement();

    return (
        <div className="container mx-auto px-4 py-8">
            <h1 className="text-2xl font-bold mb-6">Bitácora de Residuos</h1>
            
            <BitacoraEntriesTable
                data={entriesData?.data || []}
                isLoading={isLoading}
                pageNumber={pageNumber}
                pageSize={pageSize}
                totalRecords={entriesData?.totalRecords || 0}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                onDelete={handleDelete}
                onActivate={handleActivate}
                onDeactivate={handleDeactivate}
                renderForm={(data?: BitacoraEntry) => (
                    <BitacoraEntryForm
                        data={data}
                        areaOptions={areaOptions}
                        wasteTypeOptions={wasteTypeOptions}
                        containerTypeOptions={containerTypeOptions}
                        onAreaSearch={handleAreaSearch}
                        onWasteTypeSearch={handleWasteTypeSearch}
                        onContainerTypeSearch={handleContainerTypeSearch}
                    />
                )}
                renderDetails={(entry: BitacoraEntry) => (
                    <BitacoraEntryDetails entry={entry} />
                )}
                onSubmitCreate={handleCreate}
                onSubmitUpdate={handleUpdate}
            />
        </div>
    );
};

export default BitacoraEntries;