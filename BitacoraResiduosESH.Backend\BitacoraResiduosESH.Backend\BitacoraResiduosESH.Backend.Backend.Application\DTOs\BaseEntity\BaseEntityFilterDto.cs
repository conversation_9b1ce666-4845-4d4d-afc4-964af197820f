namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;

public class BaseEntityFilterDto
{
    public bool? Active { get; set; }
    public bool IncludeDeleted { get; set; } = false;
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public DateTime? CreatedFrom { get; set; }
    public DateTime? CreatedTo { get; set; }
    public string? CreatedBy { get; set; }
    public DateTime? UpdatedFrom { get; set; }
    public DateTime? UpdatedTo { get; set; }
    public string? UpdatedBy { get; set; }
}