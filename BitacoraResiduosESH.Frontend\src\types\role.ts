import type {PagedResponse} from '../config/api';
import type { SimpleEntity } from './simpleEntity';

// Tipos para la entidad Role basados en los DTOs del backend
// Extendiendo SimpleEntity para compatibilidad con SimpleEntityTable

export interface Role extends SimpleEntity {
    // Role no tiene campos adicionales, solo hereda de SimpleEntity
}

export interface CreateRoleDto {
    name: string;
    description?: string;
    active?: boolean;
}

export interface UpdateRoleDto {
    id: number;
    name: string;
    description?: string;
    active?: boolean;
}

export interface RoleFilterDto {
    name?: string;
    description?: string;
    active?: boolean;
    includeDeleted?: boolean;
    pageNumber: number;
    pageSize: number;
}

// Tipo para las operaciones CRUD
export interface RoleCrudOperations {
    create: (data: CreateRoleDto) => Promise<Role>;
    update: (id: number, data: UpdateRoleDto) => Promise<Role>;
    delete: (id: number) => Promise<boolean>;
    getById: (id: number, includeDeleted?: boolean) => Promise<Role>;
    getAll: (pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<Role>>;
    getActive: (pageNumber?: number, pageSize?: number) => Promise<PagedResponse<Role>>;
    searchByName: (name: string, pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<Role>>;
    searchByDescription: (description: string, pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<Role>>;
    exists: (id: number, includeDeleted?: boolean) => Promise<boolean>;
    activate: (id: number) => Promise<boolean>;
    deactivate: (id: number) => Promise<boolean>;
} 