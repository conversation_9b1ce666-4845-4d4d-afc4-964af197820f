# LocationType CRUD

Este documento describe la funcionalidad CRUD (Create, Read, Update, Delete) para la entidad `LocationType`.

## Descripción

`LocationType` es una entidad que hereda de `SimpleEntity` y representa los tipos de ubicación donde se pueden almacenar o usar las herramientas en el sistema.

## Estructura de la Entidad

```csharp
public class LocationType : SimpleEntity
{
    // Hereda todas las propiedades de SimpleEntity:
    // - Id (int)
    // - Name (string)
    // - Description (string?)
    // - Active (bool)
    // - Created (DateTime)
    // - Updated (DateTime?)
    // - CreatedBy (string?)
    // - UpdatedBy (string?)
    // - IsDeleted (bool)
    // - Deleted (DateTime?)
    // - DeletedBy (string?)
}
```

## Endpoints Disponibles

### GET /api/LocationType
Obtiene una lista paginada de tipos de ubicación.

**Parámetros de consulta:**
- `name` (string, opcional): Filtrar por nombre
- `description` (string, opcional): Filtrar por descripción
- `active` (bool, opcional): Filtrar por estado activo
- `includeDeleted` (bool, opcional): Incluir registros eliminados
- `pageNumber` (int, opcional): Número de página (por defecto: 1)
- `pageSize` (int, opcional): Tamaño de página (por defecto: 10)

**Respuesta:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "Almacén Principal",
            "description": "Ubicación principal de almacenamiento de herramientas",
            "active": true,
            "createdAt": "2024-01-01T00:00:00Z",
            "updatedAt": null,
            "createdBy": "system",
            "updatedBy": null
        }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
}
```

### GET /api/LocationType/{id}
Obtiene un tipo de ubicación específico por su ID.

**Respuesta:**
```json
{
    "id": 1,
    "name": "Almacén Principal",
    "description": "Ubicación principal de almacenamiento de herramientas",
    "active": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": null,
    "createdBy": "system",
    "updatedBy": null
}
```

### POST /api/LocationType
Crea un nuevo tipo de ubicación.

**Cuerpo de la petición:**
```json
{
    "name": "Laboratorio de Calidad",
    "description": "Ubicación en el laboratorio para herramientas de control de calidad"
}
```

**Respuesta:**
```json
{
    "id": 5,
    "name": "Laboratorio de Calidad",
    "description": "Ubicación en el laboratorio para herramientas de control de calidad",
    "active": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": null,
    "createdBy": "<EMAIL>",
    "updatedBy": null
}
```

### PUT /api/LocationType/{id}
Actualiza un tipo de ubicación existente.

**Cuerpo de la petición:**
```json
{
    "name": "Almacén Principal Actualizado",
    "description": "Ubicación principal de almacenamiento de herramientas (actualizado)"
}
```

**Respuesta:**
```json
{
    "id": 1,
    "name": "Almacén Principal Actualizado",
    "description": "Ubicación principal de almacenamiento de herramientas (actualizado)",
    "active": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z",
    "createdBy": "system",
    "updatedBy": "<EMAIL>"
}
```

### DELETE /api/LocationType/{id}
Elimina un tipo de ubicación (soft delete).

**Respuesta:**
```json
{
    "success": true,
    "message": "LocationType eliminado exitosamente"
}
```

### PUT /api/LocationType/restore/{id}
Restaura un tipo de ubicación eliminado.

**Respuesta:**
```json
{
    "success": true,
    "message": "LocationType restaurado exitosamente"
}
```

## Validaciones

### CreateLocationTypeDto
- `Name`: Requerido, máximo 100 caracteres
- `Description`: Opcional, máximo 500 caracteres

### UpdateLocationTypeDto
- `Name`: Requerido, máximo 100 caracteres
- `Description`: Opcional, máximo 500 caracteres

## Datos de Prueba

Al inicializar la base de datos, se crean automáticamente los siguientes tipos de ubicación:

1. **Almacén Principal**
   - Descripción: Ubicación principal de almacenamiento de herramientas

2. **Área de Producción**
   - Descripción: Ubicación en el área de producción donde se usan las herramientas

3. **Taller de Mantenimiento**
   - Descripción: Ubicación en el taller donde se realizan mantenimientos

4. **Oficina Técnica**
   - Descripción: Ubicación en la oficina técnica para herramientas especializadas

## Autenticación

Todos los endpoints requieren autenticación mediante JWT Bearer token. El token se obtiene mediante el endpoint de login:

```http
POST /api/Auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "123456"
}
```

## Permisos

Actualmente, todos los endpoints están disponibles para usuarios autenticados. En el futuro se pueden implementar permisos específicos por rol.

## Ejemplos de Uso

### Obtener todos los tipos de ubicación activos
```http
GET /api/LocationType?active=true
Authorization: Bearer {token}
```

### Buscar tipos de ubicación por nombre
```http
GET /api/LocationType?name=Almacén&pageNumber=1&pageSize=5
Authorization: Bearer {token}
```

### Crear un nuevo tipo de ubicación
```http
POST /api/LocationType
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Nueva Ubicación",
    "description": "Descripción de la nueva ubicación"
}
```

## Notas de Implementación

- La entidad utiliza soft delete (eliminación lógica)
- Todos los cambios se registran con auditoría (usuario y timestamp)
- La paginación es automática en todos los endpoints de listado
- Los filtros son opcionales y se pueden combinar
- La validación se realiza tanto en el cliente como en el servidor 