import type {PagedResponse} from '../config/api';
import {buildApiUrl, getAuthHeadersFromStore, handleApiError} from '../config/api';
import type {
    AdminChangePasswordDto,
    ChangePasswordDto,
    CreateUserDto,
    UpdateUserDto,
    User,
    UserCrudOperations
} from '../types/user';
import type {SimpleEntityService} from '../hooks/useSimpleEntityCrud';

class UserService implements UserCrudOperations, SimpleEntityService<User, CreateUserDto, UpdateUserDto> {
    private baseUrl = 'User';

    // Crear un nuevo usuario
    async create(data: CreateUserDto): Promise<User> {
        return this.request<User>('', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    // Actualizar un usuario existente
    async update(id: number, data: UpdateUserDto): Promise<User> {
        return this.request<User>(`/update/${id}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    // Eliminar un usuario (soft delete)
    async delete(id: number): Promise<boolean> {
        return this.request<boolean>(`/${id}`, {
            method: 'DELETE',
        });
    }

    // Obtener un usuario por ID
    async getById(id: number, includeDeleted = false): Promise<User> {
        return this.request<User>(`/${id}?includeDeleted=${includeDeleted}`);
    }

    // Obtener todos los usuarios con paginación
    async getAll(pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<User>> {
        return this.request<PagedResponse<User>>(
            `?pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Obtener usuarios activos con paginación
    async getActive(pageNumber = 1, pageSize = 10): Promise<PagedResponse<User>> {
        return this.request<PagedResponse<User>>(
            `/active?pageNumber=${pageNumber}&pageSize=${pageSize}`
        );
    }

    // Buscar usuarios por nombre (para SimpleEntityService)
    async searchByName(name: string, pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<User>> {
        return this.request<PagedResponse<User>>(
            `/search/name?name=${encodeURIComponent(name)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Buscar usuarios por descripción (para SimpleEntityService)
    async searchByDescription(description: string, pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<User>> {
        // Como User no tiene descripción, usamos búsqueda por nombre
        return this.searchByName(description, pageNumber, pageSize, includeDeleted);
    }

    // Buscar usuarios por nombre (método específico)
    async getByName(name: string, pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<User>> {
        return this.searchByName(name, pageNumber, pageSize, includeDeleted);
    }

    // Buscar usuarios por email
    async getByEmail(email: string, pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<User>> {
        return this.request<PagedResponse<User>>(
            `/search/email?email=${encodeURIComponent(email)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Obtener usuario por email exacto
    async getByEmailExact(email: string, includeDeleted = false): Promise<User | null> {
        try {
            return await this.request<User>(`/search/email/exact?employeeNumber=${encodeURIComponent(email)}&includeDeleted=${includeDeleted}`);
        } catch (error: any) {
            if (error.message.includes('404')) {
                return null;
            }
            throw error;
        }
    }

    // Obtener usuario por número de empleado exacto
    async getByEmployeeNumberExact(employeeNumber: string, includeDeleted = false): Promise<User | null> {
        try {
            return await this.request<User>(`/search/email/exact?employeeNumber=${encodeURIComponent(employeeNumber)}&includeDeleted=${includeDeleted}`);
        } catch (error: any) {
            if (error.message.includes('404')) {
                return null;
            }
            throw error;
        }
    }

    // Verificar si existe un usuario con el email especificado
    async existsByEmail(email: string, excludeId?: number, includeDeleted = false): Promise<boolean> {
        const params = new URLSearchParams({
            employeeNumber: email,
            includeDeleted: includeDeleted.toString(),
        });
        if (excludeId) params.append('excludeId', excludeId.toString());

        return this.request<boolean>(`/exists/email?${params.toString()}`);
    }

    // Verificar si existe un usuario con el número de empleado especificado
    async existsByEmployeeNumber(employeeNumber: string, excludeId?: number, includeDeleted = false): Promise<boolean> {
        const params = new URLSearchParams({
            employeeNumber,
            includeDeleted: includeDeleted.toString(),
        });
        if (excludeId) params.append('excludeId', excludeId.toString());

        return this.request<boolean>(`/exists/email?${params.toString()}`);
    }

    // Verificar si existe un usuario por ID
    async exists(id: number, includeDeleted = false): Promise<boolean> {
        return this.request<boolean>(`/${id}/exists?includeDeleted=${includeDeleted}`);
    }

    // Activar un usuario
    async activate(id: number): Promise<boolean> {
        return this.request<boolean>(`/${id}/activate`, {
            method: 'PATCH',
        });
    }

    // Desactivar un usuario
    async deactivate(id: number): Promise<boolean> {
        return this.request<boolean>(`/${id}/deactivate`, {
            method: 'PATCH',
        });
    }

    // Búsqueda personalizada
    async customSearch(predicate: string, pageNumber = 1, pageSize = 10, includeDeleted = false): Promise<PagedResponse<User>> {
        return this.request<PagedResponse<User>>(
            `/search/custom?predicate=${encodeURIComponent(predicate)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Obtener perfil del usuario autenticado
    async getProfile(): Promise<any> {
        return this.request<any>('/profile');
    }

    // Verificar autenticación
    async checkAuthentication(): Promise<any> {
        return this.request<any>('/authenticated');
    }

    // Cambiar contraseña del usuario
    async changePassword(data: ChangePasswordDto): Promise<boolean> {
        return this.request<boolean>('/change-password', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    // Cambiar contraseña por administrador
    async adminChangePassword(data: AdminChangePasswordDto): Promise<boolean> {
        return this.request<boolean>('/admin/change-password', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    // Función helper para hacer peticiones HTTP
    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<T> {
        const url = buildApiUrl(`${this.baseUrl}${endpoint}`);

        const config: RequestInit = {
            headers: getAuthHeadersFromStore(),
            ...options,
        };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            throw new Error(handleApiError(error));
        }
    }
}

export const userService = new UserService(); 