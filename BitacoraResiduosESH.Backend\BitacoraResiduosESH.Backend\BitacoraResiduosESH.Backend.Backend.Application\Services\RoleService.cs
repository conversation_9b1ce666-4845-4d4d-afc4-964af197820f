namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class RoleService(IRoleRepository repository)
    : SimpleEntityService<Role, RoleDto, CreateRoleDto, UpdateRoleDto, RoleFilterDto>(repository), IRoleService
{
    // No se agregan métodos adicionales
    // Solo hereda todos los métodos de SimpleEntityService:
    // - Operaciones de lectura: GetByIdAsync, GetAllAsync, GetPagedAsync, GetActiveAsync, GetByNameAsync, GetByDescriptionAsync
    // - Operaciones de escritura: CreateAsync, UpdateAsync
    // - Operaciones de eliminación: DeleteAsync, HardDeleteAsync
    // - Operaciones de activación/desactivación: ActivateAsync, DeactivateAsync
    // - Operaciones de validación: ExistsAsync, ExistsByNameAsync
    // - Logging completo automático
    // - Validaciones automáticas
    // - Mapeo automático entre entidad y DTO
}