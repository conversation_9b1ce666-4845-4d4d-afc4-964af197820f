import type { BitacoraEntry } from '@/types/bitacoraEntry';
import type { SearchableSelectOption } from '@/components/ui';

export interface BitacoraEntriesTableProps {
    data: BitacoraEntry[];
    isLoading: boolean;
    pageNumber: number;
    pageSize: number;
    totalRecords: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (size: number) => void;
    onDelete: (id: number) => Promise<void>;
    onActivate: (id: number) => Promise<void>;
    onDeactivate: (id: number) => Promise<void>;
    renderForm: (data?: BitacoraEntry) => React.ReactNode;
    renderDetails: (entry: BitacoraEntry) => React.ReactNode;
    onSubmitCreate: (data: any) => Promise<void>;
    onSubmitUpdate: (id: number, data: any) => Promise<void>;
}
