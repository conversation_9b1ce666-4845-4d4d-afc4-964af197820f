namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.User;

public class CreateUserDto : CreateBaseEntityDto
{
    [Required(ErrorMessage = "El nombre es requerido")]
    [StringLength(200, ErrorMessage = "El nombre no puede exceder 200 caracteres")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "El numero de empleado es requerido")]
    [StringLength(30, ErrorMessage = "El numero de empleado no puede exceder 30 caracteres")]
    public string EmployeeNumber { get; set; } = string.Empty;

    [EmailAddress(ErrorMessage = "El formato del email no es válido")]
    [StringLength(255, ErrorMessage = "El email no puede exceder 255 caracteres")]
    public string? Email { get; set; }

    [Required(ErrorMessage = "La contraseña es requerida")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "La contraseña debe tener entre 6 y 100 caracteres")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "El rol es requerido")]
    [Range(1, int.MaxValue, ErrorMessage = "El ID del rol debe ser válido")]
    public int RoleId { get; set; }
}