# Sistema de Gestión de Contraseñas

## Descripción

Se ha implementado un sistema separado para la gestión de contraseñas de usuarios, permitiendo actualizar información de
usuario sin requerir cambios de contraseña.

## Problema Resuelto

**Antes**: El `UpdateUserDto` requería obligatoriamente una contraseña, lo que forzaba a los usuarios a cambiar su
contraseña cada vez que actualizaban cualquier información.

**Después**: Se separó la funcionalidad de cambio de contraseña en endpoints específicos, permitiendo:

- Actualizar información de usuario sin cambiar contraseña
- Cambiar contraseña de forma independiente
- Dos tipos de cambio de contraseña: usuario normal y administrador

## Estructura del Backend

### DTOs

#### `UpdateUserDto` (Modificado)

```csharp
public class UpdateUserDto : UpdateBaseEntityDto
{
    [Required(ErrorMessage = "El nombre es requerido")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "El numero de empleado es requerido")]
    public string EmployeeNumber { get; set; } = string.Empty;

    [EmailAddress(ErrorMessage = "El formato del email no es válido")]
    public string? Email { get; set; }
    
    [Required(ErrorMessage = "El rol es requerido")]
    public int RoleId { get; set; }
    
    // ❌ Ya NO incluye Password
}
```

#### `ChangePasswordDto` (Nuevo)

```csharp
public class ChangePasswordDto
{
    [Required(ErrorMessage = "El ID del usuario es requerido")]
    public int UserId { get; set; }
    
    [Required(ErrorMessage = "La contraseña actual es requerida")]
    public string CurrentPassword { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "La nueva contraseña es requerida")]
    public string NewPassword { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "La confirmación de la nueva contraseña es requerida")]
    [Compare("NewPassword", ErrorMessage = "La confirmación de contraseña no coincide")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}
```

#### `AdminChangePasswordDto` (Nuevo)

```csharp
public class AdminChangePasswordDto
{
    [Required(ErrorMessage = "El ID del usuario es requerido")]
    public int UserId { get; set; }
    
    [Required(ErrorMessage = "La nueva contraseña es requerida")]
    public string NewPassword { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "La confirmación de la nueva contraseña es requerida")]
    [Compare("NewPassword", ErrorMessage = "La confirmación de contraseña no coincide")]
    public string ConfirmNewPassword { get; set; } = string.Empty;
}
```

### Servicio (`IUserService`)

```csharp
public interface IUserService : IGenericService<User, UserDto, UserFilterDto, CreateUserDto, UpdateUserDto>
{
    // ... métodos existentes ...
    
    // Nuevos métodos para cambio de contraseña
    Task<bool> ChangePasswordAsync(ChangePasswordDto dto);
    Task<bool> AdminChangePasswordAsync(AdminChangePasswordDto dto, string updatedBy);
}
```

### Controlador (`UserController`)

#### Endpoint para cambio de contraseña de usuario

```csharp
[HttpPost("change-password")]
public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto dto)
```

**Características**:

- Requiere contraseña actual para validación
- Solo el usuario puede cambiar su propia contraseña
- Validación de confirmación de contraseña

#### Endpoint para cambio de contraseña por administrador

```csharp
[HttpPost("admin/change-password")]
public async Task<IActionResult> AdminChangePassword([FromBody] AdminChangePasswordDto dto)
```

**Características**:

- No requiere contraseña actual
- Solo administradores pueden usar este endpoint
- Útil para reset de contraseñas olvidadas

## Estructura del Frontend

### Tipos TypeScript

```typescript
export interface ChangePasswordDto {
  userId: number;
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

export interface AdminChangePasswordDto {
  userId: number;
  newPassword: string;
  confirmNewPassword: string;
}
```

### Servicio (`userService`)

```typescript
// Cambiar contraseña del usuario
async changePassword(data: ChangePasswordDto): Promise<boolean> {
  return this.request<boolean>('/change-password', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// Cambiar contraseña por administrador
async adminChangePassword(data: AdminChangePasswordDto): Promise<boolean> {
  return this.request<boolean>('/admin/change-password', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}
```

### Hook (`useUserCrud`)

```typescript
// Mutation para cambiar contraseña del usuario
const useChangePassword = () => {
  return useMutation({
    mutationFn: (data: ChangePasswordDto) => userService.changePassword(data),
    onSuccess: () => {
      toast.success('Contraseña cambiada exitosamente');
    },
    onError: (error: Error) => {
      toast.error(`Error al cambiar la contraseña: ${error.message}`);
    },
  });
};

// Mutation para cambiar contraseña por administrador
const useAdminChangePassword = () => {
  return useMutation({
    mutationFn: (data: AdminChangePasswordDto) => userService.adminChangePassword(data),
    onSuccess: () => {
      toast.success('Contraseña cambiada exitosamente');
    },
    onError: (error: Error) => {
      toast.error(`Error al cambiar la contraseña: ${error.message}`);
    },
  });
};
```

### Componente (`ChangePasswordModal`)

```typescript
interface ChangePasswordModalProps {
  userId: number;
  isAdmin?: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}
```

**Características**:

- Formulario reutilizable para ambos tipos de cambio
- Validación de confirmación de contraseña
- Estados de carga y manejo de errores
- Integración con TanStack Query y Sonner

## Uso

### Cambio de contraseña por usuario normal

```typescript
import { ChangePasswordModal } from '../components/form/ChangePasswordModal';

<ChangePasswordModal
  userId={user.id}
  isAdmin={false}
  onSuccess={() => {
    // Manejar éxito
  }}
  onCancel={() => {
    // Manejar cancelación
  }}
/>
```

### Cambio de contraseña por administrador

```typescript
<ChangePasswordModal
  userId={user.id}
  isAdmin={true}
  onSuccess={() => {
    // Manejar éxito
  }}
  onCancel={() => {
    // Manejar cancelación
  }}
/>
```

## Beneficios

1. **Separación de responsabilidades**: Actualización de datos y cambio de contraseña son operaciones independientes
2. **Mejor UX**: Los usuarios no están forzados a cambiar contraseña al actualizar información
3. **Seguridad**: Validación de contraseña actual para cambios de usuario normal
4. **Flexibilidad**: Administradores pueden resetear contraseñas sin conocer la actual
5. **Mantenibilidad**: Código más limpio y fácil de mantener

## Consideraciones de Seguridad

⚠️ **Nota importante**: En producción, se debe implementar:

- Hashing de contraseñas (bcrypt, Argon2, etc.)
- Validación de complejidad de contraseñas
- Rate limiting para prevenir ataques de fuerza bruta
- Logging de intentos de cambio de contraseña
- Notificaciones por email al cambiar contraseña

## Endpoints API

| Método | Endpoint                          | Descripción                                        |
|--------|-----------------------------------|----------------------------------------------------|
| POST   | `/api/User/change-password`       | Cambio de contraseña por usuario                   |
| POST   | `/api/User/admin/change-password` | Cambio de contraseña por administrador             |
| PUT    | `/api/User/{id}`                  | Actualización de datos de usuario (sin contraseña) | 