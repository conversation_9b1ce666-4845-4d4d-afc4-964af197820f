﻿using BitacoraResiduosESH.Backend.Backend.Application.Common;
using BitacoraResiduosESH.Backend.Backend.Application.DTOs.SimpleEntity;

namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;

public interface ISimpleEntityService<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>
    where TEntity : SimpleEntity
    where TDto : SimpleEntityDto
    where TCreateDto : CreateSimpleEntityDto
    where TUpdateDto : UpdateSimpleEntityDto
    where TFilterDto : SimpleEntityFilterDto
{
    #region Operaciones de lectura

    Task<TDto?> GetByIdAsync(int id, bool includeDeleted = false);
    Task<PagedResponse<TDto>> GetAllAsync(PaginationFilter filter);
    Task<PagedResponse<TDto>> GetPagedAsync(TFilterDto filter);
    Task<PagedResponse<TDto>> GetActiveAsync(PaginationFilter filter);
    Task<PagedResponse<TDto>> GetByNameAsync(string name, PaginationFilter filter, bool includeDeleted = false);

    Task<PagedResponse<TDto>> GetByDescriptionAsync(string description, PaginationFilter filter,
        bool includeDeleted = false);

    Task<PagedResponse<TDto>> FuzzySearchAsync(string searchTerm, PaginationFilter filter,
        bool includeDeleted = false);

    #endregion

    #region Operaciones de escritura

    Task<TDto> CreateAsync(TCreateDto dto, string createdBy);
    Task<TDto> UpdateAsync(int id, TUpdateDto dto, string updatedBy);

    #endregion

    #region Operaciones de eliminación

    Task<bool> DeleteAsync(int id, string deletedBy);
    Task<bool> HardDeleteAsync(int id);

    #endregion

    #region Operaciones de activación/desactivación

    Task<bool> ActivateAsync(int id, string updatedBy);
    Task<bool> DeactivateAsync(int id, string updatedBy);

    #endregion

    #region Operaciones de validación

    Task<bool> ExistsAsync(int id, bool includeDeleted = false);
    Task<bool> ExistsByNameAsync(string name, int? excludeId = null, bool includeDeleted = false);

    #endregion
}