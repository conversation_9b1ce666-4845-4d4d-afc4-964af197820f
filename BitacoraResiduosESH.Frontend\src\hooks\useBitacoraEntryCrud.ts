import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { bitacoraEntryService } from '../services/bitacoraEntryService';
import type { 
    CreateBitacoraEntryDto, 
    UpdateBitacoraEntryDto, 
    BitacoraEntryFilterDto 
} from '../types/bitacoraEntry';

// Query keys para TanStack Query
export const bitacoraEntryQueryKeys = {
    all: ['bitacoraEntries'] as const,
    lists: () => [...bitacoraEntryQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...bitacoraEntryQueryKeys.lists(), filters] as const,
    details: () => [...bitacoraEntryQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...bitacoraEntryQueryKeys.details(), id] as const,
};

export const useBitacoraEntryCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todas las BitacoraEntries
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ pageNumber, pageSize, includeDeleted }),
            queryFn: () => bitacoraEntryService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 2 * 60 * 1000, // 2 minutos - datos más dinámicos
            gcTime: 5 * 60 * 1000, // 5 minutos
        });
    };

    // Query para obtener BitacoraEntries activas
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ active: true, pageNumber, pageSize }),
            queryFn: () => bitacoraEntryService.getActive(pageNumber, pageSize),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para obtener una BitacoraEntry por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.detail(id),
            queryFn: () => bitacoraEntryService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar BitacoraEntries con filtros
    const useSearch = (filters: BitacoraEntryFilterDto) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ search: 'filters', ...filters }),
            queryFn: () => bitacoraEntryService.search(filters),
            enabled: Object.keys(filters).some(key => 
                key !== 'pageNumber' && key !== 'pageSize' && key !== 'includeDeleted' && 
                filters[key as keyof BitacoraEntryFilterDto] !== undefined
            ),
            staleTime: 1 * 60 * 1000, // 1 minuto para búsquedas
            gcTime: 3 * 60 * 1000,
        });
    };

    // Query para obtener BitacoraEntries paginadas con filtros
    const useGetPaged = (filters: BitacoraEntryFilterDto) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ paged: true, ...filters }),
            queryFn: () => bitacoraEntryService.getPaged(filters),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar BitacoraEntries por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ search: 'name', name, pageNumber, pageSize, includeDeleted }),
            queryFn: () => bitacoraEntryService.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 1 * 60 * 1000,
            gcTime: 3 * 60 * 1000,
        });
    };

    // Query para buscar BitacoraEntries por área
    const useSearchByArea = (
        areaId: number,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ search: 'area', areaId, pageNumber, pageSize, includeDeleted }),
            queryFn: () => bitacoraEntryService.searchByArea(areaId, pageNumber, pageSize, includeDeleted),
            enabled: !!areaId,
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar BitacoraEntries por tipo de residuo
    const useSearchByWasteType = (
        wasteTypeId: number,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ search: 'wasteType', wasteTypeId, pageNumber, pageSize, includeDeleted }),
            queryFn: () => bitacoraEntryService.searchByWasteType(wasteTypeId, pageNumber, pageSize, includeDeleted),
            enabled: !!wasteTypeId,
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar BitacoraEntries por tipo de contenedor
    const useSearchByContainerType = (
        containerTypeId: number,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: bitacoraEntryQueryKeys.list({ search: 'containerType', containerTypeId, pageNumber, pageSize, includeDeleted }),
            queryFn: () => bitacoraEntryService.searchByContainerType(containerTypeId, pageNumber, pageSize, includeDeleted),
            enabled: !!containerTypeId,
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear una BitacoraEntry
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateBitacoraEntryDto) => bitacoraEntryService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
                toast.success('Entrada de bitácora creada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear la entrada de bitácora: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar una BitacoraEntry
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({ id, data }: { id: number; data: UpdateBitacoraEntryDto }) =>
                bitacoraEntryService.update(id, data),
            onSuccess: (updatedEntry) => {
                queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
                queryClient.setQueryData(bitacoraEntryQueryKeys.detail(updatedEntry.id), updatedEntry);
                toast.success('Entrada de bitácora actualizada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar la entrada de bitácora: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar una BitacoraEntry
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => bitacoraEntryService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
                queryClient.removeQueries({ queryKey: bitacoraEntryQueryKeys.detail(deletedId) });
                toast.success('Entrada de bitácora eliminada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar la entrada de bitácora: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar permanentemente una BitacoraEntry
    const useHardDelete = () => {
        return useMutation({
            mutationFn: (id: number) => bitacoraEntryService.hardDelete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
                queryClient.removeQueries({ queryKey: bitacoraEntryQueryKeys.detail(deletedId) });
                toast.success('Entrada de bitácora eliminada permanentemente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar permanentemente la entrada de bitácora: ${error.message}`);
            },
        });
    };

    // Mutation para activar una BitacoraEntry
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => bitacoraEntryService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
                toast.success('Entrada de bitácora activada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar la entrada de bitácora: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar una BitacoraEntry
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => bitacoraEntryService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
                toast.success('Entrada de bitácora desactivada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar la entrada de bitácora: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({ queryKey: bitacoraEntryQueryKeys.lists() });
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearch,
        useGetPaged,
        useSearchByName,
        useSearchByArea,
        useSearchByWasteType,
        useSearchByContainerType,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useHardDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
};
