using System.Text.Json.Serialization;

namespace BitacoraResiduosESH.Backend.Backend.Domain.Entities.OpenIdConnect;

/// <summary>
///     Representa la respuesta de token de OpenID Connect
/// </summary>
public class TokenResponse
{
    [JsonPropertyName("access_token")] public string AccessToken { get; set; } = string.Empty;

    [JsonPropertyName("token_type")] public string TokenType { get; set; } = string.Empty;

    [JsonPropertyName("expires_in")] public int ExpiresIn { get; set; }

    [JsonPropertyName("refresh_token")] public string? RefreshToken { get; set; }

    [JsonPropertyName("scope")] public string Scope { get; set; } = string.Empty;

    [JsonPropertyName("id_token")] public string? IdToken { get; set; }
}