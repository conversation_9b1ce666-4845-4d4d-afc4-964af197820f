namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Services;

/// <summary>
///     Implementación del servicio de autenticación local con JWT
/// </summary>
public class AuthService : IAuthService
{
    private readonly IJwtService _jwtService;
    private readonly IPasswordService _passwordService;
    private readonly IUserService _userService;

    public AuthService(
        IUserService userService,
        IPasswordService passwordService,
        IJwtService jwtService)
    {
        _userService = userService;
        _passwordService = passwordService;
        _jwtService = jwtService;
    }

    /// <summary>
    ///     Autentica un usuario con email y contraseña
    /// </summary>
    /// <param name="email">Email del usuario</param>
    /// <param name="password">Contraseña del usuario</param>
    /// <returns>Token JWT si la autenticación es exitosa, null en caso contrario</returns>
    public async Task<string?> AuthenticateAsync(string email, string password)
    {
        if (string.IsNullOrWhiteSpace(email) || string.IsNullOrWhiteSpace(password))
            return null;

        try
        {
            // Buscar el usuario por email (entidad completa)
            var user = await _userService.GetUserEntityByEmailAsync(email);
            if (user == null)
                return null;

            // Verificar que el usuario esté activo
            if (!user.Active)
                return null;

            // Verificar la contraseña
            if (!_passwordService.VerifyPassword(password, user.Password))
                return null;

            // Generar token JWT
            var token = _jwtService.GenerateToken(user.Id, user.Name, user.Email ?? "", user.Role?.Name ?? "Normal");
            return token;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     Autentica un usuario con número de empleado y contraseña
    /// </summary>
    /// <param name="employeeNumber">Número de empleado del usuario</param>
    /// <param name="password">Contraseña del usuario</param>
    /// <returns>Token JWT si la autenticación es exitosa, null en caso contrario</returns>
    public async Task<string?> AuthenticateByEmployeeNumberAsync(string employeeNumber, string password)
    {
        if (string.IsNullOrWhiteSpace(employeeNumber) || string.IsNullOrWhiteSpace(password))
            return null;

        try
        {
            // Buscar el usuario por número de empleado (entidad completa)
            var user = await _userService.GetUserEntityByEmployeeNumberAsync(employeeNumber);
            if (user == null)
                return null;

            // Verificar que el usuario esté activo
            if (!user.Active)
                return null;

            // Verificar la contraseña
            if (!_passwordService.VerifyPassword(password, user.Password))
                return null;

            // Generar token JWT
            var token = _jwtService.GenerateToken(user.Id, user.Name, user.Email ?? "", user.Role?.Name ?? "Normal");
            return token;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     Autentica un usuario con email o número de empleado y contraseña
    /// </summary>
    /// <param name="email">Email del usuario (opcional)</param>
    /// <param name="employeeNumber">Número de empleado del usuario (opcional)</param>
    /// <param name="password">Contraseña del usuario</param>
    /// <returns>Token JWT si la autenticación es exitosa, null en caso contrario</returns>
    public async Task<string?> AuthenticateAsync(string? email, string? employeeNumber, string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            return null;

        // Debe proporcionarse al menos un identificador
        if (string.IsNullOrWhiteSpace(email) && string.IsNullOrWhiteSpace(employeeNumber))
            return null;

        try
        {
            User? user = null;

            // Intentar autenticar por email si se proporciona
            if (!string.IsNullOrWhiteSpace(email)) user = await _userService.GetUserEntityByEmailAsync(email);

            // Si no se encontró por email, intentar por número de empleado
            if (user == null && !string.IsNullOrWhiteSpace(employeeNumber))
                user = await _userService.GetUserEntityByEmployeeNumberAsync(employeeNumber);

            if (user == null)
                return null;

            // Verificar que el usuario esté activo
            if (!user.Active)
                return null;

            // Verificar la contraseña
            if (!_passwordService.VerifyPassword(password, user.Password))
                return null;

            // Generar token JWT
            var token = _jwtService.GenerateToken(user.Id, user.Name, user.Email ?? "", user.Role?.Name ?? "Normal");
            return token;
        }
        catch (Exception exception)
        {
            Console.WriteLine(exception);
            return null;
        }
    }

    /// <summary>
    ///     Obtiene información del usuario autenticado
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>Información del usuario si el token es válido, null en caso contrario</returns>
    public async Task<User?> GetUserFromTokenAsync(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return null;

        try
        {
            var userId = _jwtService.GetUserIdFromToken(token);
            if (!userId.HasValue)
                return null;

            var user = await _userService.GetUserEntityByIdAsync(userId.Value);
            return user;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     Valida si un token JWT es válido
    /// </summary>
    /// <param name="token">Token JWT a validar</param>
    /// <returns>True si el token es válido, False en caso contrario</returns>
    public bool IsValidToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return false;

        var principal = _jwtService.ValidateToken(token);
        return principal != null;
    }

    /// <summary>
    ///     Refresca un token JWT
    /// </summary>
    /// <param name="token">Token JWT actual</param>
    /// <returns>Nuevo token JWT si el token actual es válido, null en caso contrario</returns>
    public async Task<string?> RefreshTokenAsync(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return null;

        try
        {
            var principal = _jwtService.ValidateToken(token);
            if (principal == null)
                return null;

            // Buscar el claim de ID del usuario (puede estar en diferentes claims)
            var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value
                              ?? principal.FindFirst(ClaimTypes.Name)?.Value
                              ?? principal.FindFirst("nameid")?.Value;
            var emailClaim = principal.FindFirst(ClaimTypes.Email)?.Value;
            var roleClaim = principal.FindFirst(ClaimTypes.Role)?.Value;

            if (string.IsNullOrWhiteSpace(userIdClaim) || string.IsNullOrWhiteSpace(emailClaim))
                return null;

            if (!int.TryParse(userIdClaim, out var userId))
                return null;

            // Verificar que el usuario aún existe y está activo
            var user = await _userService.GetUserEntityByIdAsync(userId);
            if (user == null || !user.Active)
                return null;

            // Generar nuevo token
            var newToken = _jwtService.GenerateToken(user.Id, user.Name, user.Email ?? "", user.Role?.Name ?? "Normal");
            return newToken;
        }
        catch
        {
            return null;
        }
    }
}