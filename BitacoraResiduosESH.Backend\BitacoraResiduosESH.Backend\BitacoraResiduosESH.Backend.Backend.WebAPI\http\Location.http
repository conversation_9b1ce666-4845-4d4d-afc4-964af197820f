# Archivo de pruebas HTTP para Location API
# Variables de configuración
@baseUrl = https://localhost:7036/api
@token = Bearer your_jwt_token_here

### 1. Autenticación (reemplaza el token arriba con el resultado)
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}

### 2. Obtener todos los tipos de ubicación (para referencias)
GET {{baseUrl}}/locationtype/active?pageNumber=1&pageSize=10
Authorization: {{token}}

### 3. Crear una nueva ubicación
POST {{baseUrl}}/location
Authorization: {{token}}
Content-Type: application/json

{
  "name": "Almacén A1",
  "description": "Primer almacén del área A para herramientas",
  "locationTypeId": 1
}

### 4. Crear otra ubicación con diferente tipo
POST {{baseUrl}}/location
Authorization: {{token}}
Content-Type: application/json

{
  "name": "Línea de Producción 1",
  "description": "Primera línea de producción para herramientas activas",
  "locationTypeId": 2
}

### 5. Obtener todas las ubicaciones (paginado)
GET {{baseUrl}}/location?pageNumber=1&pageSize=10
Authorization: {{token}}

### 6. Obtener todas las ubicaciones activas
GET {{baseUrl}}/location/active?pageNumber=1&pageSize=10
Authorization: {{token}}

### 7. Obtener ubicación por ID
GET {{baseUrl}}/location/1
Authorization: {{token}}

### 8. Obtener ubicaciones por tipo específico
GET {{baseUrl}}/location/by-location-type/1
Authorization: {{token}}

### 9. Buscar ubicaciones por nombre
GET {{baseUrl}}/location/search?name=Almacén
Authorization: {{token}}

### 10. Buscar ubicaciones por descripción
GET {{baseUrl}}/location/search?description=producción
Authorization: {{token}}

### 11. Actualizar una ubicación
PUT {{baseUrl}}/location/1
Authorization: {{token}}
Content-Type: application/json

{
  "name": "Almacén Principal A1",
  "description": "Almacén principal del área A para herramientas críticas",
  "locationTypeId": 1
}

### 12. Desactivar una ubicación
PATCH {{baseUrl}}/location/1/deactivate
Authorization: {{token}}

### 13. Activar una ubicación
PATCH {{baseUrl}}/location/1/activate
Authorization: {{token}}

### 14. Verificar si existe una ubicación
GET {{baseUrl}}/location/1/exists
Authorization: {{token}}

### 15. Eliminar una ubicación (soft delete)
DELETE {{baseUrl}}/location/1
Authorization: {{token}}

### 16. Obtener ubicaciones incluyendo eliminadas
GET {{baseUrl}}/location?pageNumber=1&pageSize=10&includeDeleted=true
Authorization: {{token}}

### 17. Casos de error - Crear ubicación con tipo inexistente
POST {{baseUrl}}/location
Authorization: {{token}}
Content-Type: application/json

{
  "name": "Ubicación Inválida",
  "description": "Esta ubicación debe fallar",
  "locationTypeId": 999
}

### 18. Casos de error - Crear ubicación sin datos requeridos
POST {{baseUrl}}/location
Authorization: {{token}}
Content-Type: application/json

{
  "description": "Sin nombre ni tipo"
}

### 19. Obtener estadísticas (si está implementado)
GET {{baseUrl}}/location/stats
Authorization: {{token}}

### 20. Filtro avanzado por múltiples criterios
GET {{baseUrl}}/location?name=Almacén&locationTypeId=1&active=true&pageSize=5
Authorization: {{token}} 