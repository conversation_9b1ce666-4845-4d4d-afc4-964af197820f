import React, {useEffect, useState} from "react";
import {useLocation, useNavigate} from "@tanstack/react-router";
import {
    BarChart3,
    ChevronRight,
    ExternalLink,
    Factory,
    LayoutDashboard,
    Package,
    Settings,
    Shield,
    UserCheck,
    Users,
    Wrench,
    CircleDot,
    MapPin,
    BookOpen
} from "lucide-react";
import {usePermissions} from "../hooks/usePermissions";

interface SidebarProps {
    isOpen: boolean;
    onClose: () => void;
}

interface MenuItem {
    id: string;
    label: string;
    icon: React.ReactNode;
    href?: string;
    externalUrl?: string;
    children?: MenuItem[];
    requiredRoles?: string[];
    requiredPermissions?: string[];
}

const Sidebar: React.FC<SidebarProps> = ({isOpen, onClose}) => {
    const [expandedItems, setExpandedItems] = useState<string[]>([]);
    const navigate = useNavigate();
    const location = useLocation();
    const {hasUserRole} = usePermissions();

    // Obtener la ruta actual considerando hash routing
    const currentPath = location.pathname;

    // Función para verificar si una ruta está activa
    const isRouteActive = (href?: string) => {
        if (!href) return false;

        // Para la ruta raíz, debe ser exactamente "/"
        if (href === "/") {
            return currentPath === "/";
        }

        // Para otras rutas, verificar coincidencia exacta
        return currentPath === href;
    };

    // Función para verificar si un elemento padre debe estar expandido
    const shouldExpandParent = (children?: MenuItem[]) => {
        if (!children) return false;
        return children.some(child => isRouteActive(child.href));
    };

    // Función para verificar si un elemento debe mostrarse basado en permisos
    const shouldShowItem = (item: MenuItem): boolean => {
        // Si no hay restricciones de roles o permisos, mostrar
        if (!item.requiredRoles && !item.requiredPermissions) {
            return true;
        }

        // Verificar roles requeridos
        if (item.requiredRoles && !hasUserRole(item.requiredRoles)) {
            return false;
        }

        // Verificar permisos requeridos
        if (item.requiredPermissions) {
            // Por ahora, implementar lógica básica de permisos
            // En el futuro se puede expandir con un sistema más complejo
            return true;
        }

        return true;
    };

    const menuItems: MenuItem[] = [
        {
            id: "dashboard",
            label: "Dashboard",
            icon: <LayoutDashboard className="w-5 h-5"/>,
            href: "/",
        },
        {
            id: "crud-table",
            label: "Tabla CRUD",
            icon: <BarChart3 className="w-5 h-5"/>,
            href: "/crud-table",
            requiredRoles: ["Admin", "Manager", "Operator"],
        },
        {
            id: "production",
            label: "Producción",
            icon: <Factory className="w-5 h-5"/>,
            requiredRoles: ["Admin", "Manager", "Operator"],
            children: [
                {
                    id: "orders",
                    label: "Órdenes de Trabajo",
                    icon: null,
                    href: "/production/orders",
                },
                {
                    id: "schedule",
                    label: "Programación",
                    icon: null,
                    href: "/production/schedule",
                },
                {
                    id: "quality",
                    label: "Control de Calidad",
                    icon: null,
                    href: "/production/quality",
                },
            ],
        },
        {
            id: "inventory",
            label: "Inventario",
            icon: <Package className="w-5 h-5"/>,
            requiredRoles: ["Admin", "Manager", "Operator"],
            children: [
                {
                    id: "bitacora-entries",
                    label: "Bitácora de Residuos",
                    icon: <BookOpen className="w-4 h-4"/>,
                    href: "/bitacora-entries",
                    requiredRoles: ["Admin", "Manager", "Operator"],
                },
                {
                    id: "waste-types",
                    label: "Tipos de Residuos",
                    icon: <Wrench className="w-4 h-4"/>,
                    href: "/waste-types",
                    requiredRoles: ["Admin", "Manager"],
                },
                {
                    id: "areas",
                    label: "Áreas",
                    icon: <MapPin className="w-4 h-4"/>,
                    href: "/areas",
                    requiredRoles: ["Admin", "Manager"],
                },
                {
                    id: "container-types",
                    label: "Tipos de Contenedores",
                    icon: <CircleDot className="w-4 h-4"/>,
                    href: "/container-types",
                    requiredRoles: ["Admin", "Manager"],
                },
            ],
        },
        {
            id: "employees",
            label: "Empleados",
            icon: <Users className="w-5 h-5"/>,
            requiredRoles: ["Admin", "Manager"],
            children: [
                {
                    id: "users",
                    label: "Usuarios",
                    icon: <UserCheck className="w-4 h-4"/>,
                    href: "/users",
                    requiredRoles: ["Admin", "Manager"],
                },
                {
                    id: "roles",
                    label: "Roles",
                    icon: <Shield className="w-4 h-4"/>,
                    href: "/roles",
                    requiredRoles: ["Admin"],
                },
                {
                    id: "attendance",
                    label: "Asistencia",
                    icon: null,
                    externalUrl: "http://ameawnetweb02.molex.com:50190/TOR/",
                },
                {
                    id: "payroll",
                    label: "Nómina",
                    icon: null,
                    externalUrl: "http://kgsnosvwpapp001.khc.local/nsHCM/Account/Login",
                },
            ],
        },
        {
            id: "reports",
            label: "Reportes",
            icon: <BarChart3 className="w-5 h-5"/>,
            requiredRoles: ["Admin", "Manager"],
            children: [
                {
                    id: "production-reports",
                    label: "Producción",
                    icon: null,
                    href: "/reports/production",
                },
                {
                    id: "financial-reports",
                    label: "Financieros",
                    icon: null,
                    href: "/reports/financial",
                },
                {
                    id: "performance-reports",
                    label: "Rendimiento",
                    icon: null,
                    href: "/reports/performance",
                },
            ],
        },
        {
            id: "settings",
            label: "Configuración",
            icon: <Settings className="w-5 h-5"/>,
            href: "/settings",
            requiredRoles: ["Admin"],
        },
    ];

    // Filtrar elementos basados en permisos
    const filteredMenuItems = menuItems.filter(shouldShowItem).map(item => ({
        ...item,
        children: item.children?.filter(shouldShowItem)
    }));

    // Sincronizar expandedItems con la ruta actual
    useEffect(() => {
        const newExpandedItems: string[] = [];

        filteredMenuItems.forEach(item => {
            if (item.children && shouldExpandParent(item.children)) {
                newExpandedItems.push(item.id);
            }
        });

        setExpandedItems(newExpandedItems);
    }, [currentPath]);

    const toggleExpanded = (itemId: string) => {
        setExpandedItems((prev) =>
            prev.includes(itemId)
                ? prev.filter((id) => id !== itemId)
                : [...prev, itemId]
        );
    };

    const handleNavigation = (item: MenuItem) => {
        if (item.externalUrl) {
            // Abrir enlace externo en nueva pestaña
            window.open(item.externalUrl, '_blank', 'noopener,noreferrer');
        } else if (item.href) {
            // Navegación interna
            navigate({to: item.href});
            // Cerrar sidebar en móvil después de navegar
            if (window.innerWidth < 1024) {
                onClose();
            }
        }
    };

    const renderMenuItem = (item: MenuItem, level: number = 0) => {
        const hasChildren = item.children && item.children.length > 0;
        const isExpanded = expandedItems.includes(item.id);
        const isActive = isRouteActive(item.href);
        const isExternal = !!item.externalUrl;

        const getItemStyles = () => {
            if (level === 0) {
                if (isActive) {
                    return {
                        backgroundColor: '#dbeafe',
                        color: '#1e3a8a'
                    };
                } else {
                    return {
                        backgroundColor: 'transparent',
                        color: 'var(--text-primary)'
                    };
                }
            } else {
                if (isActive) {
                    return {
                        backgroundColor: '#eff6ff',
                        color: '#1e40af',
                        marginLeft: '1rem'
                    };
                } else {
                    return {
                        backgroundColor: 'transparent',
                        color: 'var(--text-secondary)',
                        marginLeft: '1rem'
                    };
                }
            }
        };

        return (
            <div key={item.id}>
                <div
                    className="flex items-center justify-between px-4 py-2 text-sm font-medium rounded-lg cursor-pointer transition-all duration-200"
                    style={getItemStyles()}
                    onMouseEnter={(e) => {
                        if (!isActive) {
                            e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                        }
                    }}
                    onMouseLeave={(e) => {
                        if (!isActive) {
                            e.currentTarget.style.backgroundColor = 'transparent';
                        }
                    }}
                    onClick={() => {
                        if (item.href || item.externalUrl) {
                            handleNavigation(item);
                        } else if (hasChildren) {
                            toggleExpanded(item.id);
                        }
                    }}
                >
                    <div className="flex items-center space-x-3">
                        {item.icon && (
                            <span style={{color: 'var(--text-secondary)'}}>
                {item.icon}
              </span>
                        )}
                        <span>{item.label}</span>
                        {isExternal && (
                            <ExternalLink className="w-3 h-3" style={{color: 'var(--text-secondary)'}}/>
                        )}
                    </div>
                    {hasChildren && (
                        <ChevronRight
                            className={`w-4 h-4 transition-transform duration-200 ${
                                isExpanded ? "transform rotate-90" : ""
                            }`}
                            style={{color: 'var(--text-secondary)'}}
                        />
                    )}
                </div>

                {hasChildren && isExpanded && (
                    <div className="mt-1 space-y-1">
                        {item.children!.map((child) => renderMenuItem(child, level + 1))}
                    </div>
                )}
            </div>
        );
    };

    return (
        <>
            {/* Mobile overlay */}
            {isOpen && (
                <div
                    className="fixed inset-0 bg-gray-600/75 transition-opacity z-20 lg:hidden"
                    onClick={onClose}
                />
            )}

            {/* Sidebar */}
            <div
                className={`
          ${isOpen ? 'w-64' : 'w-0'} 
          lg:w-64 
          fixed
          top-0
          left-0
          h-screen 
          shadow-lg 
          border-r 
          transition-all 
          duration-300 
          ease-in-out 
          overflow-hidden
          flex-shrink-0
          z-30
        `}
                style={{
                    backgroundColor: 'var(--sidebar-bg)',
                    borderColor: 'var(--border-color)'
                }}
            >
                <div className="flex flex-col h-full w-64">
                    {/* Sidebar header */}
                    <div
                        className="flex items-center justify-between h-16 px-4 border-b"
                        style={{borderColor: 'var(--border-color)'}}
                    >
                        <div className="flex items-center space-x-2">
                            <div
                                className="w-10 h-10 rounded-lg flex items-center justify-center p-1 shadow-sm border"
                                style={{
                                    backgroundColor: 'var(--bg-secondary)',
                                    borderColor: 'var(--border-color)'
                                }}
                            >
                                <img
                                    src="/molex-logo-1.png"
                                    alt="Molex Nogales"
                                    className="w-full h-full object-contain"
                                />
                            </div>
                            <span
                                className="text-xl font-semibold"
                                style={{color: 'var(--text-primary)'}}
                            >
                Molex
              </span>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 rounded-md transition-colors duration-200 lg:hidden"
                            style={{
                                color: 'var(--text-secondary)',
                                backgroundColor: 'transparent'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.color = 'var(--text-primary)';
                                e.currentTarget.style.backgroundColor = 'var(--hover-bg)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'var(--text-secondary)';
                                e.currentTarget.style.backgroundColor = 'transparent';
                            }}
                        >
                            <svg
                                className="w-6 h-6"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                />
                            </svg>
                        </button>
                    </div>

                    {/* Navigation */}
                    <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
                        {filteredMenuItems.map((item) => renderMenuItem(item))}
                    </nav>

                    {/* Sidebar footer */}
                    <div
                        className="p-4 border-t"
                        style={{borderColor: 'var(--border-color)'}}
                    >
                        <div className="flex items-center space-x-3 text-sm">
                            <div
                                className="w-8 h-8 rounded-full flex items-center justify-center"
                                style={{
                                    backgroundColor: 'var(--text-secondary)',
                                    color: 'var(--bg-secondary)'
                                }}
                            >
                                <span className="font-medium text-xs">MP</span>
                            </div>
                            <div>
                                <div
                                    className="font-medium"
                                    style={{color: 'var(--text-primary)'}}
                                >
                                    Maximiliano Ponce
                                </div>
                                <div
                                    className="text-xs"
                                    style={{color: 'var(--text-secondary)'}}
                                >
                                    Administrador
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Sidebar;
