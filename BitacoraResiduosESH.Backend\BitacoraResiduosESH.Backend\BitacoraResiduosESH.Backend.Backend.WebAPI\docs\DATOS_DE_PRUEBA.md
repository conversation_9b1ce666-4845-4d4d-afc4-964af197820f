# Datos de Prueba - Sistema de Gestión de Herramientas

## Descripción

Este documento describe los datos de prueba que se crean automáticamente al inicializar la base de datos por primera vez.

## Usuario Administrador

### Credenciales de Acceso
- **Email**: `<EMAIL>`
- **Número de Empleado**: `ADMIN001`
- **Contraseña**: `Admin123!`
- **Rol**: `Admin`

### Información del Usuario
- **Nombre**: `Administrador`
- **Descripción del Rol**: `Administrador del sistema con acceso completo`
- **Estado**: `Activo`

## Roles del Sistema

### Rol Admin
- **Nombre**: `Admin`
- **Descripción**: `Administrador del sistema con acceso completo`
- **Permisos**: Acceso completo a todas las funcionalidades del sistema

## Tipos de Herramientas Iniciales

### 1. Herramienta Manual
- **Descripción**: Herramientas que se operan manualmente sin necesidad de energía eléctrica
- **Ejemplos**: Martillos, destornilladores, llaves, alicates

### 2. Herramienta Eléctrica
- **Descripción**: Herramientas que requieren energía eléctrica para funcionar
- **Ejemplos**: Taladros, sierras eléctricas, lijadoras, pulidoras

### 3. Herramienta Neumática
- **Descripción**: Herramientas que utilizan aire comprimido para su funcionamiento
- **Ejemplos**: Pistolas de aire, martillos neumáticos, lijadoras neumáticas

### 4. Herramienta de Medición
- **Descripción**: Herramientas utilizadas para medir dimensiones, ángulos y otras propiedades
- **Ejemplos**: Calibres, micrómetros, reglas, niveles

### 5. Herramienta de Corte
- **Descripción**: Herramientas diseñadas para cortar diferentes tipos de materiales
- **Ejemplos**: Tijeras, cuchillos, sierras, cortadoras

## Cómo Usar los Datos de Prueba

### 1. Login al Sistema
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}
```

### 2. Usar el Token JWT
Una vez autenticado, usar el token en el header `Authorization`:
```http
Authorization: Bearer {token}
```

### 3. Acceder a los Endpoints
Con el token de administrador, puedes acceder a todos los endpoints del sistema:
- `/api/tooltype/*` - Gestión de tipos de herramientas
- `/api/role/*` - Gestión de roles
- `/api/user/*` - Gestión de usuarios

## Notas Importantes

### Seguridad
- **Cambiar la contraseña**: Se recomienda cambiar la contraseña del administrador después del primer login
- **Entorno de producción**: Estos datos solo se crean en el primer inicio. En producción, crear usuarios manualmente
- **Credenciales temporales**: Estas credenciales son solo para desarrollo y testing

### Inicialización
- Los datos se crean automáticamente solo si la base de datos está vacía
- Si ya existen datos, la inicialización se omite
- El proceso es idempotente y seguro

### Logs
El proceso de inicialización genera logs detallados:
- `"Inicializando base de datos con datos de prueba..."`
- `"Rol Admin creado exitosamente"`
- `"Usuario administrador creado exitosamente"`
- `"X tipos de herramientas creados exitosamente"`
- `"Inicialización de base de datos completada exitosamente"`

## Ejemplos de Uso

### Obtener Tipos de Herramientas
```http
GET /api/tooltype/active?pageNumber=1&pageSize=10
Authorization: Bearer {token}
```

### Crear Nuevo Tipo de Herramienta
```http
POST /api/tooltype
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "Herramienta de Soldadura",
  "description": "Herramientas utilizadas para soldar metales"
}
```

### Obtener Información del Usuario
```http
GET /api/user/1
Authorization: Bearer {token}
```

## Troubleshooting

### Error de Autenticación
Si recibes un error 401, verifica:
1. Que el email y contraseña sean correctos
2. Que el usuario esté activo
3. Que el token JWT no haya expirado

### Datos No Creados
Si los datos de prueba no se crean:
1. Verifica los logs de la aplicación
2. Asegúrate de que la base de datos esté vacía
3. Revisa que todos los servicios estén registrados correctamente

### Error de Base de Datos
Si hay errores de base de datos:
1. Verifica la cadena de conexión
2. Asegúrate de que la base de datos se pueda crear
3. Revisa los permisos de escritura en el directorio 