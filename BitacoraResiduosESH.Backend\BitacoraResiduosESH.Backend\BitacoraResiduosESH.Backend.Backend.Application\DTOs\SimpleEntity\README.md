# Sistema SimpleEntity - G<PERSON><PERSON> de Uso

## Descripción

El sistema `SimpleEntity` es una implementación genérica que permite crear entidades con funcionalidad CRUD completa de
manera rápida y eficiente, evitando la duplicación de código. Está diseñado para entidades que tienen las propiedades
básicas: `Name`, `Description`, y heredan de `BaseEntity`.

## Arquitectura

### Componentes Base

1. **SimpleEntity** - Entidad base con propiedades comunes
2. **SimpleEntityDto** - DTO base para transferencia de datos
3. **CreateSimpleEntityDto** - DTO para operaciones de creación
4. **UpdateSimpleEntityDto** - DTO para operaciones de actualización
5. **SimpleEntityFilterDto** - DTO para filtros de búsqueda

### Interfaces Genéricas

1. **ISimpleEntityRepository<TEntity>** - Repositorio genérico
2. **ISimpleEntityService<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>** - Servicio genérico
3. **SimpleEntityController<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>** - Controlador genérico

### Implementaciones Base

1. **SimpleEntityRepository<TEntity>** - Implementación del repositorio genérico
2. **SimpleEntityService<TEntity, TDto, TCreateDto, TUpdateDto, TFilterDto>** - Implementación del servicio genérico
3. **SimpleEntityConfiguration** - Configuración base para Entity Framework

## Cómo Crear una Nueva Entidad

### Paso 1: Crear la Entidad

```csharp
namespace ToolsMS.Backend.Domain.Entities;

public class MiEntidad : SimpleEntity
{
    // Propiedades específicas de tu entidad
    public string PropiedadEspecifica { get; set; }
    public int ValorNumerico { get; set; }
    public DateTime? FechaOpcional { get; set; }
}
```

### Paso 2: Crear los DTOs

#### DTO Principal

```csharp
using ToolsMS.Backend.Application.DTOs.SimpleEntity;

namespace ToolsMS.Backend.Application.DTOs.MiEntidad;

public class MiEntidadDto : SimpleEntityDto
{
    public string PropiedadEspecifica { get; set; }
    public int ValorNumerico { get; set; }
    public DateTime? FechaOpcional { get; set; }
}
```

#### DTO de Creación

```csharp
using System.ComponentModel.DataAnnotations;
using ToolsMS.Backend.Application.DTOs.SimpleEntity;

namespace ToolsMS.Backend.Application.DTOs.MiEntidad;

public class CreateMiEntidadDto : CreateSimpleEntityDto
{
    [Required(ErrorMessage = "La propiedad específica es requerida")]
    [StringLength(100, ErrorMessage = "La propiedad específica no puede exceder 100 caracteres")]
    public string PropiedadEspecifica { get; set; }
    
    [Range(0, int.MaxValue, ErrorMessage = "El valor numérico debe ser mayor o igual a 0")]
    public int ValorNumerico { get; set; }
    
    public DateTime? FechaOpcional { get; set; }
}
```

#### DTO de Actualización

```csharp
using System.ComponentModel.DataAnnotations;
using ToolsMS.Backend.Application.DTOs.SimpleEntity;

namespace ToolsMS.Backend.Application.DTOs.MiEntidad;

public class UpdateMiEntidadDto : UpdateSimpleEntityDto
{
    [Required(ErrorMessage = "La propiedad específica es requerida")]
    [StringLength(100, ErrorMessage = "La propiedad específica no puede exceder 100 caracteres")]
    public string PropiedadEspecifica { get; set; }
    
    [Range(0, int.MaxValue, ErrorMessage = "El valor numérico debe ser mayor o igual a 0")]
    public int ValorNumerico { get; set; }
    
    public DateTime? FechaOpcional { get; set; }
}
```

#### DTO de Filtros

```csharp
using ToolsMS.Backend.Application.DTOs.SimpleEntity;

namespace ToolsMS.Backend.Application.DTOs.MiEntidad;

public class MiEntidadFilterDto : SimpleEntityFilterDto
{
    public string? PropiedadEspecifica { get; set; }
    public int? MinValorNumerico { get; set; }
    public int? MaxValorNumerico { get; set; }
    public DateTime? FechaDesde { get; set; }
    public DateTime? FechaHasta { get; set; }
}
```

### Paso 3: Crear las Interfaces

#### Interfaz del Repositorio

```csharp
using ToolsMS.Backend.Domain.Entities;

namespace ToolsMS.Backend.Application.Interfaces.Repositories;

public interface IMiEntidadRepository : ISimpleEntityRepository<MiEntidad>
{
    // Métodos específicos de tu entidad
    Task<IEnumerable<MiEntidad>> GetByPropiedadEspecificaAsync(string propiedad, bool includeDeleted = false);
    Task<IEnumerable<MiEntidad>> GetByValorNumericoRangeAsync(int min, int max, bool includeDeleted = false);
}
```

#### Interfaz del Servicio

```csharp
using ToolsMS.Backend.Application.DTOs.MiEntidad;

namespace ToolsMS.Backend.Application.Interfaces.Services;

public interface IMiEntidadService : ISimpleEntityService<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto>
{
    // Métodos específicos de tu entidad
    Task<IEnumerable<MiEntidadDto>> GetByPropiedadEspecificaAsync(string propiedad, bool includeDeleted = false);
    Task<IEnumerable<MiEntidadDto>> GetByValorNumericoRangeAsync(int min, int max, bool includeDeleted = false);
}
```

### Paso 4: Implementar el Repositorio

```csharp
using Microsoft.EntityFrameworkCore;
using ToolsMS.Backend.Application.Interfaces.Repositories;
using ToolsMS.Backend.Domain.Entities;
using ToolsMS.Backend.Infrastructure.Data;
using NLog;

namespace ToolsMS.Backend.Infrastructure.Repositories;

public class MiEntidadRepository(AppDbContext context) : SimpleEntityRepository<MiEntidad>(context), IMiEntidadRepository
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public async Task<IEnumerable<MiEntidad>> GetByPropiedadEspecificaAsync(string propiedad, bool includeDeleted = false)
    {
        Logger.Debug("Buscando MiEntidad por propiedad específica: {Propiedad}, IncludeDeleted: {IncludeDeleted}", propiedad, includeDeleted);
        
        var query = DbSet.AsQueryable();
        
        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);
            
        var result = await query
            .Where(e => e.PropiedadEspecifica.ToLower().Contains(propiedad.ToLower()))
            .ToListAsync();
            
        Logger.Debug("Búsqueda por propiedad específica completada: {Count} resultados", result.Count());
        
        return result;
    }

    public async Task<IEnumerable<MiEntidad>> GetByValorNumericoRangeAsync(int min, int max, bool includeDeleted = false)
    {
        Logger.Debug("Buscando MiEntidad por rango de valor numérico: {Min} - {Max}, IncludeDeleted: {IncludeDeleted}", min, max, includeDeleted);
        
        var query = DbSet.AsQueryable();
        
        if (!includeDeleted)
            query = query.Where(e => !e.IsDeleted);
            
        var result = await query
            .Where(e => e.ValorNumerico >= min && e.ValorNumerico <= max)
            .ToListAsync();
            
        Logger.Debug("Búsqueda por rango de valor numérico completada: {Count} resultados", result.Count());
        
        return result;
    }
}
```

### Paso 5: Implementar el Servicio

```csharp
using ToolsMS.Backend.Application.DTOs.MiEntidad;
using ToolsMS.Backend.Application.Interfaces.Repositories;
using ToolsMS.Backend.Application.Interfaces.Services;
using ToolsMS.Backend.Domain.Entities;
using NLog;

namespace ToolsMS.Backend.Application.Services;

public class MiEntidadService : SimpleEntityService<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto>, IMiEntidadService
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    private readonly IMiEntidadRepository _miEntidadRepository;

    public MiEntidadService(IMiEntidadRepository repository) : base(repository)
    {
        _miEntidadRepository = repository;
    }

    public async Task<IEnumerable<MiEntidadDto>> GetByPropiedadEspecificaAsync(string propiedad, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo MiEntidad por propiedad específica: {Propiedad}, IncludeDeleted: {IncludeDeleted}", propiedad, includeDeleted);
        
        var entities = await _miEntidadRepository.GetByPropiedadEspecificaAsync(propiedad, includeDeleted);
        var result = entities.Select(MapToDto);
        
        Logger.Debug("MiEntidad por propiedad específica obtenidas: {Count} elementos", result.Count());
        
        return result;
    }

    public async Task<IEnumerable<MiEntidadDto>> GetByValorNumericoRangeAsync(int min, int max, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo MiEntidad por rango de valor numérico: {Min} - {Max}, IncludeDeleted: {IncludeDeleted}", min, max, includeDeleted);
        
        var entities = await _miEntidadRepository.GetByValorNumericoRangeAsync(min, max, includeDeleted);
        var result = entities.Select(MapToDto);
        
        Logger.Debug("MiEntidad por rango de valor numérico obtenidas: {Count} elementos", result.Count());
        
        return result;
    }

    protected override MiEntidadDto MapToDto(MiEntidad entity)
    {
        var dto = base.MapToDto(entity);
        dto.PropiedadEspecifica = entity.PropiedadEspecifica;
        dto.ValorNumerico = entity.ValorNumerico;
        dto.FechaOpcional = entity.FechaOpcional;
        return dto;
    }
}
```

### Paso 6: Crear el Controlador

```csharp
using Microsoft.AspNetCore.Mvc;
using ToolsMS.Backend.Application.DTOs.MiEntidad;
using ToolsMS.Backend.Application.Interfaces.Services;
using ToolsMS.Backend.Domain.Entities;
using NLog;

namespace ToolsMS.Backend.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class MiEntidadController : SimpleEntityController<MiEntidad, MiEntidadDto, CreateMiEntidadDto, UpdateMiEntidadDto, MiEntidadFilterDto>
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();
    private readonly IMiEntidadService _miEntidadService;

    public MiEntidadController(IMiEntidadService service) : base(service)
    {
        _miEntidadService = service;
    }

    /// <summary>
    /// Obtiene entidades por propiedad específica
    /// </summary>
    [HttpGet("by-propiedad")]
    [ProducesResponseType(typeof(IEnumerable<MiEntidadDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IEnumerable<MiEntidadDto>>> GetByPropiedad([FromQuery] string propiedad, [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Búsqueda por propiedad específica para MiEntidad: {Propiedad}, IncludeDeleted: {IncludeDeleted}", propiedad, includeDeleted);
        
        try
        {
            if (string.IsNullOrWhiteSpace(propiedad))
            {
                Logger.Warn("Búsqueda por propiedad específica con parámetro vacío para MiEntidad");
                return BadRequest("La propiedad es requerida");
            }

            var result = await _miEntidadService.GetByPropiedadEspecificaAsync(propiedad, includeDeleted);
            Logger.Debug("Búsqueda por propiedad específica completada para MiEntidad - {Count} resultados", result.Count());
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al buscar MiEntidad por propiedad específica: {Propiedad}", propiedad);
            return BadRequest("Error al buscar entidades por propiedad específica");
        }
    }

    /// <summary>
    /// Obtiene entidades por rango de valor numérico
    /// </summary>
    [HttpGet("by-valor-range")]
    [ProducesResponseType(typeof(IEnumerable<MiEntidadDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IEnumerable<MiEntidadDto>>> GetByValorRange(
        [FromQuery] int min, 
        [FromQuery] int max, 
        [FromQuery] bool includeDeleted = false)
    {
        Logger.Debug("Solicitud GET para MiEntidad por rango de valor: {Min} - {Max}, IncludeDeleted: {IncludeDeleted}", min, max, includeDeleted);
        
        try
        {
            var result = await _miEntidadService.GetByValorNumericoRangeAsync(min, max, includeDeleted);
            Logger.Debug("MiEntidad por rango de valor obtenidas - {Count} elementos", result.Count());
            return Ok(result);
        }
        catch (Exception ex)
        {
            Logger.Error(ex, "Error al obtener MiEntidad por rango de valor");
            return BadRequest("Error al obtener las entidades por rango de valor");
        }
    }
}
```

### Paso 7: Crear la Configuración de Entity Framework

```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ToolsMS.Backend.Domain.Entities;

namespace ToolsMS.Backend.Infrastructure.Data.ModelConfigurations;

public class MiEntidadConfiguration : SimpleEntityConfiguration<MiEntidad>
{
    public MiEntidadConfiguration() : base("MiEntidades")
    {
    }

    public override void Configure(EntityTypeBuilder<MiEntidad> builder)
    {
        // Configuración base de SimpleEntity
        base.Configure(builder);
        
        // Configuración específica de MiEntidad
        builder.Property(e => e.PropiedadEspecifica)
            .IsRequired()
            .HasMaxLength(100);
            
        builder.Property(e => e.ValorNumerico)
            .IsRequired()
            .HasDefaultValue(0);
            
        builder.Property(e => e.FechaOpcional)
            .IsRequired(false);
            
        // Índices específicos
        builder.HasIndex(e => e.PropiedadEspecifica)
            .HasDatabaseName("IX_MiEntidades_PropiedadEspecifica");
            
        builder.HasIndex(e => e.ValorNumerico)
            .HasDatabaseName("IX_MiEntidades_ValorNumerico");
            
        builder.HasIndex(e => new { e.ValorNumerico, e.Active })
            .HasDatabaseName("IX_MiEntidades_ValorNumerico_Active");
    }
}
```

### Paso 8: Registrar las Dependencias

#### En AppDbContext.cs

```csharp
public DbSet<MiEntidad> MiEntidades { get; set; }

protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    base.OnModelCreating(modelBuilder);
    
    // Aplicar configuraciones de entidades
    modelBuilder.ApplyConfiguration(new MiEntidadConfiguration());
    // ... otras configuraciones
}
```

#### En ServiceCollectionExtensions.cs

```csharp
public static IServiceCollection AddRepositories(this IServiceCollection services)
{
    services.AddScoped<IMiEntidadRepository, MiEntidadRepository>();
    // ... otros repositorios
    return services;
}

public static IServiceCollection AddApplicationServices(this IServiceCollection services)
{
    services.AddScoped<IMiEntidadService, MiEntidadService>();
    // ... otros servicios
    return services;
}
```

## Funcionalidades Automáticas

Al heredar de `SimpleEntity`, tu entidad obtiene automáticamente:

### Operaciones CRUD Básicas

- ✅ Crear entidad
- ✅ Leer entidad por ID
- ✅ Leer todas las entidades
- ✅ Actualizar entidad
- ✅ Eliminar entidad (soft delete)
- ✅ Eliminación permanente (hard delete)

### Operaciones de Búsqueda

- ✅ Búsqueda por nombre
- ✅ Búsqueda por descripción
- ✅ Paginación con filtros
- ✅ Obtener entidades activas
- ✅ Incluir/excluir entidades eliminadas

### Operaciones de Estado

- ✅ Activar entidad
- ✅ Desactivar entidad
- ✅ Verificar existencia

### Auditoría Automática

- ✅ Fecha y usuario de creación
- ✅ Fecha y usuario de actualización
- ✅ Fecha y usuario de eliminación
- ✅ Estado activo/inactivo
- ✅ Soft delete

### Logging Completo

- ✅ Logging de todas las operaciones
- ✅ Logging de errores
- ✅ Logging de consultas SQL (en DEBUG)

## Ventajas del Sistema

1. **Reutilización de Código**: No necesitas implementar operaciones CRUD básicas
2. **Consistencia**: Todas las entidades siguen el mismo patrón
3. **Mantenibilidad**: Cambios en la base se reflejan en todas las entidades
4. **Escalabilidad**: Fácil agregar nuevas entidades
5. **Auditoría**: Sistema de auditoría automático
6. **Logging**: Logging completo automático
7. **Validaciones**: Validaciones base automáticas

## Ejemplo Completo: Product

El proyecto incluye un ejemplo completo de la entidad `Product` que demuestra cómo usar el sistema:

- **Entidad**: `Product.cs`
- **DTOs**: `ProductDto.cs`, `CreateProductDto.cs`, `UpdateProductDto.cs`, `ProductFilterDto.cs`
- **Interfaces**: `IProductRepository.cs`, `IProductService.cs`
- **Implementaciones**: `ProductRepository.cs`, `ProductService.cs`
- **Controlador**: `ProductController.cs`
- **Configuración**: `ProductConfiguration.cs`
- **Pruebas**: `Product.http`

## Notas Importantes

1. **Herencia**: Tu entidad debe heredar de `SimpleEntity`
2. **DTOs**: Tus DTOs deben heredar de los DTOs base correspondientes
3. **Configuración**: Usa `SimpleEntityConfiguration` como base
4. **Registro**: No olvides registrar las dependencias en el contenedor
5. **Mapeo**: Sobrescribe `MapToDto` en tu servicio para mapear propiedades específicas

## Migraciones

Después de crear tu entidad, ejecuta:

```bash
dotnet ef migrations add AddMiEntidad
dotnet ef database update
```

¡Y listo! Tu nueva entidad tendrá toda la funcionalidad CRUD completa automáticamente. 