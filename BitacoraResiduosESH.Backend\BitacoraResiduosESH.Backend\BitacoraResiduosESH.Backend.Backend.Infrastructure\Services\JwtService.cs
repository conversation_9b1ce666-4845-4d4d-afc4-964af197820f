namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Services;

/// <summary>
///     Implementación del servicio JWT
/// </summary>
public class JwtService : IJwtService
{
    private readonly string _audience;
    private readonly int _expirationMinutes;
    private readonly string _issuer;
    private readonly string _secretKey;

    public JwtService(IConfiguration configuration)
    {
        _secretKey = configuration["Jwt:SecretKey"] ??
                     throw new InvalidOperationException("JWT SecretKey no configurado");
        _issuer = configuration["Jwt:Issuer"] ?? "ToolsMS";
        _audience = configuration["Jwt:Audience"] ?? "ToolsMS";
        _expirationMinutes = int.TryParse(configuration["Jwt:ExpirationMinutes"], out var exp) ? exp : 60;
    }

    /// <summary>
    ///     Genera un token JWT para un usuario
    /// </summary>
    /// <param name="userId">ID del usuario</param>
    /// <param name="email">Email del usuario</param>
    /// <param name="role">Rol del usuario</param>
    /// <returns>Token JWT</returns>
    public string GenerateToken(int userId, string name, string email, string role)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_secretKey);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userId.ToString()),
            new(ClaimTypes.Name, name),
            new(ClaimTypes.Email, email),
            new(ClaimTypes.Role, role),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Iat, DateTimeOffset.Now.ToUnixTimeSeconds().ToString(),
                ClaimValueTypes.Integer64),
            new(JwtRegisteredClaimNames.Nbf, DateTimeOffset.Now.ToUnixTimeSeconds().ToString(),
                ClaimValueTypes.Integer64)
        };

        var now = DateTime.Now;

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            NotBefore = now,
            Expires = now.AddMinutes(_expirationMinutes),
            Issuer = _issuer,
            Audience = _audience,
            SigningCredentials =
                new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    /// <summary>
    ///     Valida un token JWT
    /// </summary>
    /// <param name="token">Token JWT a validar</param>
    /// <returns>Claims del token si es válido, null en caso contrario</returns>
    public ClaimsPrincipal? ValidateToken(string token)
    {
        if (string.IsNullOrWhiteSpace(token))
            return null;

        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_secretKey);

            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _issuer,
                ValidateAudience = true,
                ValidAudience = _audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var validatedToken);
            return principal;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    ///     Extrae el ID del usuario del token JWT
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>ID del usuario si el token es válido, null en caso contrario</returns>
    public int? GetUserIdFromToken(string token)
    {
        var principal = ValidateToken(token);
        if (principal == null)
            return null;

        // Buscar el claim de ID del usuario (puede estar en diferentes claims)
        var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value
                          ?? principal.FindFirst(ClaimTypes.Name)?.Value
                          ?? principal.FindFirst("nameid")?.Value;

        if (int.TryParse(userIdClaim, out var userId))
            return userId;

        return null;
    }

    /// <summary>
    ///     Extrae el email del usuario del token JWT
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>Email del usuario si el token es válido, null en caso contrario</returns>
    public string? GetUserEmailFromToken(string token)
    {
        var principal = ValidateToken(token);
        return principal?.FindFirst(ClaimTypes.Email)?.Value;
    }

    /// <summary>
    ///     Extrae el rol del usuario del token JWT
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>Rol del usuario si el token es válido, null en caso contrario</returns>
    public string? GetUserRoleFromToken(string token)
    {
        var principal = ValidateToken(token);
        return principal?.FindFirst(ClaimTypes.Role)?.Value;
    }
}