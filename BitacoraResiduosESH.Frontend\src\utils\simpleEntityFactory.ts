import type { SimpleEntity, CreateSimpleEntityDto, UpdateSimpleEntityDto } from '@/types/simpleEntity';
import { BaseSimpleEntityService } from '@/services/simpleEntityService';
import { useSimpleEntityCrud } from '@/hooks/useSimpleEntityCrud';

/**
 * Factory utilities for creating SimpleEntity services and hooks
 * These utilities reduce boilerplate code when creating new SimpleEntity-based entities
 */

/**
 * Creates a typed service class for a SimpleEntity
 * 
 * @param baseUrl - The API endpoint base URL
 * @returns A service class constructor
 * 
 * @example
 * ```typescript
 * const CategoryService = createSimpleEntityServiceClass<Category>('categories');
 * export const categoryService = new CategoryService();
 * ```
 */
export function createSimpleEntityServiceClass<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto = CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto = UpdateSimpleEntityDto
>(baseUrl: string) {
    return class extends BaseSimpleEntityService<T, TCreate, TUpdate> {
        constructor() {
            super(baseUrl);
        }
    };
}

/**
 * Creates a service instance for a SimpleEntity
 * 
 * @param baseUrl - The API endpoint base URL
 * @returns A service instance
 * 
 * @example
 * ```typescript
 * export const categoryService = createSimpleEntityService<Category>('categories');
 * ```
 */
export function createSimpleEntityService<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto = CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto = UpdateSimpleEntityDto
>(baseUrl: string): BaseSimpleEntityService<T, TCreate, TUpdate> {
    return new BaseSimpleEntityService<T, TCreate, TUpdate>(baseUrl);
}

/**
 * Creates a custom hook for SimpleEntity CRUD operations
 * 
 * @param entityName - The entity name for cache keys and toast messages
 * @param service - The service instance
 * @returns A custom hook function
 * 
 * @example
 * ```typescript
 * const categoryService = createSimpleEntityService<Category>('categories');
 * export const useCategoryCrud = createSimpleEntityCrudHook('Category', categoryService);
 * ```
 */
export function createSimpleEntityCrudHook<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto = CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto = UpdateSimpleEntityDto
>(entityName: string, service: BaseSimpleEntityService<T, TCreate, TUpdate>) {
    return () => useSimpleEntityCrud<T, TCreate, TUpdate>(entityName, service);
}

/**
 * Complete factory function that creates both service and hook
 * 
 * @param entityName - The entity name for cache keys and toast messages
 * @param baseUrl - The API endpoint base URL
 * @returns An object containing the service instance and hook function
 * 
 * @example
 * ```typescript
 * const { service: categoryService, useCrud: useCategoryCrud } = 
 *     createSimpleEntityCrud<Category>('Category', 'categories');
 * ```
 */
export function createSimpleEntityCrud<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto = CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto = UpdateSimpleEntityDto
>(entityName: string, baseUrl: string) {
    const service = createSimpleEntityService<T, TCreate, TUpdate>(baseUrl);
    const useCrud = createSimpleEntityCrudHook(entityName, service);
    
    return {
        service,
        useCrud,
    };
}

/**
 * Type helpers for better TypeScript inference
 */
export type SimpleEntityCrudResult<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto = CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto = UpdateSimpleEntityDto
> = {
    service: BaseSimpleEntityService<T, TCreate, TUpdate>;
    useCrud: () => ReturnType<typeof useSimpleEntityCrud<T, TCreate, TUpdate>>;
};

/**
 * Utility type for extracting entity type from service
 */
export type EntityFromService<T> = T extends BaseSimpleEntityService<infer U, any, any> ? U : never;

/**
 * Utility type for extracting create DTO type from service
 */
export type CreateDtoFromService<T> = T extends BaseSimpleEntityService<any, infer U, any> ? U : never;

/**
 * Utility type for extracting update DTO type from service
 */
export type UpdateDtoFromService<T> = T extends BaseSimpleEntityService<any, any, infer U> ? U : never;