import {StrictMode} from "react";
import {createRoot} from "react-dom/client";
import {RouterProvider} from "@tanstack/react-router";
import {Toaster} from "sonner";
import "./index.css";
import {router} from "./router";
import ThemeProvider from "./components/ThemeProvider";
import {queryClient} from "./config/queryClient";
import {QueryClientProvider} from "@tanstack/react-query";
import {useAuthInit, useTokenRefresh} from "./stores/authStore";

// Componente para inicializar la autenticación
const AuthInitializer: React.FC = () => {
    useAuthInit();
    useTokenRefresh();
    return null;
};

createRoot(document.getElementById("root")!).render(
    <StrictMode>
        <ThemeProvider>
            <QueryClientProvider client={queryClient}>
                <AuthInitializer/>
                <RouterProvider router={router}/>
                <Toaster
                    position="top-right"
                    richColors
                    closeButton
                    duration={4000}
                />
            </QueryClientProvider>
        </ThemeProvider>
    </StrictMode>
);
