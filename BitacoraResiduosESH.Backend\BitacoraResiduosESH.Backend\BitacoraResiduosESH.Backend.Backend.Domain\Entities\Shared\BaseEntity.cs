﻿namespace BitacoraResiduosESH.Backend.Backend.Domain.Entities.Shared;

public abstract class BaseEntity
{
    public int Id { get; set; }
    public DateTime Created { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsDeleted { get; set; }
    public DateTime? Deleted { get; set; }
    public string? DeletedBy { get; set; }
    public DateTime? Updated { get; set; }
    public string? UpdatedBy { get; set; }
    public bool Active { get; set; } = true;
}