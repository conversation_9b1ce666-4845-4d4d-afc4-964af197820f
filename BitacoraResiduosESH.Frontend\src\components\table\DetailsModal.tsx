import React from 'react';
import {Calendar, Hash, User} from 'lucide-react';
import Modal from '../Modal';

export interface DetailsModalProps {
    isOpen: boolean;
    onClose: () => void;
    data?: any;
    title?: string;
    renderContent?: (data: any) => React.ReactNode;
}

const DetailsModal: React.FC<DetailsModalProps> = ({
                                                       isOpen,
                                                       onClose,
                                                       data,
                                                       title,
                                                       renderContent,
                                                   }) => {
    // Si no hay datos, no renderizar el modal
    if (!data) return null;

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const DetailRow: React.FC<{ icon: React.ReactNode; label: string; value: string }> = ({icon, label, value}) => (
        <div className="flex items-start space-x-3 py-3 border-b last:border-b-0"
             style={{borderColor: 'var(--border-color)'}}>
            <div className="flex-shrink-0 mt-1" style={{color: 'var(--text-secondary)'}}>
                {icon}
            </div>
            <div className="flex-1 min-w-0">
                <p
                    className="text-sm font-medium"
                    style={{color: 'var(--text-secondary)'}}
                >
                    {label}
                </p>
                <p
                    className="text-sm mt-1"
                    style={{color: 'var(--text-primary)'}}
                >
                    {value}
                </p>
            </div>
        </div>
    );

    // Si se proporciona contenido personalizado, usarlo
    if (renderContent) {
        return (
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                title={title || "Detalles del Elemento"}
                size="md"
            >
                {renderContent(data)}

                {/* Botón de cerrar */}
                <div className="mt-6 flex justify-end">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        style={{
                            borderColor: 'var(--border-color)',
                            color: 'var(--text-primary)',
                        }}
                    >
                        Cerrar
                    </button>
                </div>
            </Modal>
        );
    }

    // Contenido por defecto
    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={title || "Detalles del Elemento"}
            size="md"
        >
            <div className="space-y-4">
                <DetailRow
                    icon={<Hash className="h-4 w-4"/>}
                    label="ID"
                    value={data.id.toString()}
                />

                <DetailRow
                    icon={<User className="h-4 w-4"/>}
                    label="Nombre"
                    value={data.name}
                />

                {data.description && (
                    <DetailRow
                        icon={<User className="h-4 w-4"/>}
                        label="Descripción"
                        value={data.description}
                    />
                )}

                <DetailRow
                    icon={<Calendar className="h-4 w-4"/>}
                    label="Última Actualización"
                    value={formatDate(data.updated)}
                />

                <DetailRow
                    icon={<User className="h-4 w-4"/>}
                    label="Actualizado Por"
                    value={data.updatedBy}
                />
            </div>

            {/* Botón de cerrar */}
            <div className="mt-6 flex justify-end">
                <button
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                    style={{
                        borderColor: 'var(--border-color)',
                        color: 'var(--text-primary)',
                    }}
                >
                    Cerrar
                </button>
            </div>
        </Modal>
    );
};

export default DetailsModal; 