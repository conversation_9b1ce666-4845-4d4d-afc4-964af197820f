<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6"/>
        <PackageReference Include="Scalar.AspNetCore" Version="2.4.19"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BitacoraResiduosESH.Backend.Backend.Application\BitacoraResiduosESH.Backend.Backend.Application.csproj"/>
        <ProjectReference Include="..\BitacoraResiduosESH.Backend.Backend.Domain\BitacoraResiduosESH.Backend.Backend.Domain.csproj"/>
        <ProjectReference Include="..\BitacoraResiduosESH.Backend.Backend.Infrastructure\BitacoraResiduosESH.Backend.Backend.Infrastructure.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="wwwroot\"/>
    </ItemGroup>

</Project>
