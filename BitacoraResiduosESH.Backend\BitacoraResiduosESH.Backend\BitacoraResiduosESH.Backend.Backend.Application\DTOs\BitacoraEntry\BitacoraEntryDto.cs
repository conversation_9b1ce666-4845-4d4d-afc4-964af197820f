﻿namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry;

public class BitacoraEntryDto : BaseEntityDto
{
    public string? Comments { get; set; }
    // Waste Type relationship
    public int WasteTypeId { get; set; }
    public WasteTypeDto? WasteType { get; set; }

    // Weight properties
    public decimal GrossWeight { get; set; }
    public decimal Tare { get; set; }
    public decimal NetWeightLB { get; set; }
    public decimal NetWeightKG { get; set; }

    // Price
    public decimal UnitPrice { get; set; }

    // Container Type relationship
    public int ContainerTypeId { get; set; }
    public ContainerTypeDto? ContainerType { get; set; }

    // Area relationship
    public int AreaId { get; set; }
    public AreaDto? Area { get; set; }

    // Dates
    public DateTime EntryDate { get; set; }
    public string EntryDateString { get; set; } = string.Empty;
    public DateTime? DepartureDate { get; set; }
    public string? DepartureDateString { get; set; }

    // User who entered the record
    public string EnteredBy { get; set; } = string.Empty;
}
