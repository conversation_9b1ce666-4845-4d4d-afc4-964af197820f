### Autenticación Local con JWT

### Login con Email (Usuario de Prueba)
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}

### Login con <PERSON><PERSON><PERSON><PERSON>pleado (Usuario de Prueba)
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "employeeNumber": "ADMIN001",
  "password": "Admin123!"
}

### Login con Email y Nú<PERSON>o de Empleado (prioridad: email)
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "employeeNumber": "ADMIN001",
  "password": "Admin123!"
}

### Login con Email (Credenciales de Ejemplo)
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}

### Login con Número <PERSON>pleado (Credenciales de Ejemplo)
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "employeeNumber": "EMP001",
  "password": "admin123"
}

### Refresh Token
POST {{baseUrl}}/api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "{{token}}"
}

### Validar Token
POST {{baseUrl}}/api/auth/validate
Content-Type: application/json

{
  "token": "{{token}}"
}

### OpenID Connect

### Iniciar Login OIDC
GET {{baseUrl}}/api/auth/oidc/login?state=test123&scope=openid profile email

### Callback OIDC
GET {{baseUrl}}/api/auth/oidc/callback?code=test_code&state=test123

### Refresh Token OIDC
POST {{baseUrl}}/api/auth/oidc/refresh
Content-Type: application/json

{
  "refreshToken": "{{oidcRefreshToken}}"
}

### Revocar Token
POST {{baseUrl}}/api/auth/revoke
Content-Type: application/json

{
  "token": "{{token}}",
  "tokenTypeHint": "access_token"
}

### Obtener Información del Usuario
GET {{baseUrl}}/api/auth/userinfo
Authorization: Bearer {{token}}

### Variables de entorno
@baseUrl = http://localhost:5001
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
@oidcRefreshToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... 