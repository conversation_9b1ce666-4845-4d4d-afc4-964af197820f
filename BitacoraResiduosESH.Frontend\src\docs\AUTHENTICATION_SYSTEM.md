# Sistema de Autenticación Frontend

## Descripción General

El sistema de autenticación frontend está implementado con React, <PERSON>ustand para el manejo de estado, y se integra con el
backend C# que proporciona autenticación JWT. El sistema incluye:

- **Autenticación local** con email/número de empleado y contraseña
- **Gestión de tokens JWT** con refresh automático
- **Control de acceso basado en roles** (RBAC)
- **Persistencia de sesión** en localStorage
- **Interfaz de usuario responsive** con notificaciones

## Arquitectura

### 1. Tipos y Interfaces (`src/types/auth.ts`)

```typescript
// Tipos principales
interface LoginRequest {
  email?: string;
  employeeNumber?: string;
  password: string;
}

interface UserAuthInfo {
  id: number;
  name: string;
  email: string;
  employeeNumber: string;
  role: string;
}

interface AuthResponse {
  token: string;
  tokenType: string;
  expiresIn: number;
  user: UserAuthInfo;
  message: string;
}

// Constantes de roles
export const ROLES = {
  ADMIN: 'Admin',
  USER: 'User',
  MANAGER: 'Manager',
  OPERATOR: 'Operator'
} as const;

// Constantes de permisos
export const PERMISSIONS = {
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  // ... más permisos
} as const;
```

### 2. Servicio de Autenticación (`src/services/authService.ts`)

```typescript
class AuthService {
  // Login con email o número de empleado
  async login(credentials: LoginRequest): Promise<AuthResponse>
  
  // Refresh de token
  async refreshToken(token: string): Promise<AuthResponse>
  
  // Validación de token
  async validateToken(token: string): Promise<UserAuthInfo>
  
  // Revocación de token (logout)
  async revokeToken(token: string): Promise<void>
  
  // Información del usuario
  async getUserInfo(): Promise<UserAuthInfo>
}
```

### 3. Store de Estado (`src/stores/authStore.ts`)

```typescript
interface AuthState {
  // Estado
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  user: UserAuthInfo | null;
  token: string | null;
  tokenExpiry: Date | null;
  
  // Acciones
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
  validateToken: () => Promise<boolean>;
  clearError: () => void;
  
  // Utilidades
  hasRole: (role: string | string[]) => boolean;
  isTokenExpired: () => boolean;
  getTokenExpiryTime: () => number;
}
```

### 4. Hook de Permisos (`src/hooks/usePermissions.ts`)

```typescript
export const usePermissions = () => {
  // Verificaciones de rol
  const isAdmin = (): boolean;
  const isManager = (): boolean;
  const hasUserRole = (role: string | string[]): boolean;
  
  // Permisos específicos
  const canCreateUser = (): boolean;
  const canUpdateUser = (): boolean;
  const canDeleteUser = (): boolean;
  const canViewUsers = (): boolean;
  
  // Función genérica
  const hasPermission = (permission: string): boolean;
};
```

## Componentes

### 1. Login (`src/pages/Login.tsx`)

- **Funcionalidad**: Formulario de inicio de sesión
- **Características**:
    - Soporte para email o número de empleado
    - Validación de campos
    - Indicador de carga
    - Manejo de errores
    - Redirección automática

### 2. ProtectedRoute (`src/components/ProtectedRoute.tsx`)

- **Funcionalidad**: Protección de rutas
- **Características**:
    - Verificación de autenticación
    - Verificación de roles
    - Páginas de error personalizadas
    - Redirección automática

### 3. Header (`src/components/Header.tsx`)

- **Funcionalidad**: Barra superior con información del usuario
- **Características**:
    - Información del usuario autenticado
    - Menú de usuario con logout
    - Notificaciones
    - Toggle de tema

### 4. Sidebar (`src/components/Sidebar.tsx`)

- **Funcionalidad**: Menú lateral con navegación
- **Características**:
    - Filtrado basado en roles
    - Navegación jerárquica
    - Enlaces externos
    - Indicadores de estado activo

## Flujo de Autenticación

### 1. Inicio de Sesión

```mermaid
sequenceDiagram
    participant U as Usuario
    participant L as Login Component
    participant S as Auth Store
    participant A as Auth Service
    participant B as Backend

    U->>L: Ingresa credenciales
    L->>S: login(credentials)
    S->>A: login(credentials)
    A->>B: POST /api/auth/login
    B-->>A: AuthResponse
    A-->>S: AuthResponse
    S->>S: Guardar token y usuario
    S-->>L: success = true
    L->>L: Redirigir a dashboard
```

### 2. Verificación de Token

```mermaid
sequenceDiagram
    participant A as Auth Store
    participant S as Auth Service
    participant B as Backend

    A->>A: Verificar token expirado
    alt Token válido
        A->>S: validateToken(token)
        S->>B: POST /api/auth/validate
        B-->>S: UserInfo
        S-->>A: UserInfo
        A->>A: Actualizar usuario
    else Token expirado
        A->>S: refreshToken(token)
        S->>B: POST /api/auth/refresh
        B-->>S: AuthResponse
        S-->>A: AuthResponse
        A->>A: Actualizar token
    end
```

### 3. Logout

```mermaid
sequenceDiagram
    participant U as Usuario
    participant H as Header
    participant S as Auth Store
    participant A as Auth Service
    participant B as Backend

    U->>H: Click logout
    H->>S: logout()
    S->>A: revokeToken(token)
    A->>B: POST /api/auth/revoke
    S->>S: Limpiar estado
    S->>S: Limpiar localStorage
    S-->>H: Estado actualizado
    H->>H: Redirigir a login
```

## Configuración de Rutas

### Estructura de Protección

```typescript
// Ruta pública (login)
const loginRoute = createRoute({
  path: "/login",
  component: Login,
});

// Ruta protegida (requiere autenticación)
const protectedRoute = createRoute({
  path: "/dashboard",
  component: () => (
    <ProtectedRoute>
      <DashboardLayout>
        <Dashboard />
      </DashboardLayout>
    </ProtectedRoute>
  ),
});

// Ruta con roles específicos
const adminRoute = createRoute({
  path: "/admin",
  component: () => (
    <ProtectedRoute requiredRoles="Admin">
      <DashboardLayout>
        <AdminPanel />
      </DashboardLayout>
    </ProtectedRoute>
  ),
});
```

### Roles y Permisos

| Rol          | Permisos                      | Acceso a                          |
|--------------|-------------------------------|-----------------------------------|
| **Admin**    | Todos los permisos            | Todas las páginas                 |
| **Manager**  | Gestión de usuarios, reportes | Dashboard, Usuarios, Reportes     |
| **Operator** | Operaciones básicas           | Dashboard, Producción, Inventario |
| **User**     | Solo lectura                  | Dashboard básico                  |

## Persistencia de Datos

### localStorage

```typescript
// Claves utilizadas
const STORAGE_KEYS = {
  AUTH_STORE: 'auth-store',    // Estado completo de Zustand
  TOKEN: 'auth-token',         // Token JWT
  USER: 'auth-user',           // Información del usuario
} as const;
```

### Configuración de Zustand

```typescript
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Estado y acciones
    }),
    {
      name: STORAGE_KEYS.AUTH_STORE,
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        tokenExpiry: state.tokenExpiry,
      }),
    }
  )
);
```

## Manejo de Errores

### Tipos de Errores

1. **Errores de Red**: Problemas de conectividad
2. **Errores de Autenticación**: Credenciales inválidas
3. **Errores de Autorización**: Permisos insuficientes
4. **Errores de Token**: Token expirado o inválido

### Estrategias de Recuperación

```typescript
// Auto-refresh de token
useEffect(() => {
  const checkTokenExpiry = () => {
    if (isTokenExpired()) {
      refreshToken();
    }
  };

  const interval = setInterval(checkTokenExpiry, 60000);
  return () => clearInterval(interval);
}, [refreshToken, isTokenExpired]);

// Verificación al cargar
useEffect(() => {
  if (token && !isAuthenticated) {
    validateToken();
  }
}, [token, isAuthenticated, validateToken]);
```

## Notificaciones

### Integración con Sonner

```typescript
import { toast } from 'sonner';

// Notificaciones de éxito
toast.success('Inicio de sesión exitoso');

// Notificaciones de error
toast.error('Credenciales inválidas');

// Notificaciones de información
toast.info('Sesión cerrada exitosamente');
```

## Seguridad

### Mejores Prácticas

1. **Tokens JWT**: Almacenamiento seguro en localStorage
2. **Expiración**: Verificación automática de expiración
3. **Refresh**: Renovación automática de tokens
4. **Revocación**: Invalidación de tokens al logout
5. **Validación**: Verificación de tokens en cada carga

### Consideraciones

- Los tokens se almacenan en localStorage (vulnerable a XSS)
- Se implementa refresh automático para minimizar exposición
- Se valida el token en cada carga de la aplicación
- Se revoca el token en el servidor al hacer logout

## Testing

### Casos de Prueba Recomendados

1. **Login exitoso** con email y contraseña
2. **Login exitoso** con número de empleado y contraseña
3. **Login fallido** con credenciales inválidas
4. **Refresh de token** automático
5. **Logout** y limpieza de estado
6. **Acceso denegado** por roles insuficientes
7. **Persistencia** de sesión entre recargas
8. **Manejo de errores** de red

## Configuración del Entorno

### Variables de Entorno

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:5001/api
VITE_API_TIMEOUT=10000

# JWT Configuration
VITE_JWT_EXPIRY_MINUTES=60
VITE_REFRESH_INTERVAL_MS=60000
```

### Configuración de Desarrollo

```typescript
// src/config/api.ts
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5001/api',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  RETRY_ATTEMPTS: 3,
} as const;
```

## Troubleshooting

### Problemas Comunes

1. **Token no se guarda**: Verificar localStorage disponible
2. **Refresh no funciona**: Verificar configuración de CORS
3. **Roles no se aplican**: Verificar estructura de respuesta del backend
4. **Logout no funciona**: Verificar endpoint de revocación

### Debug

```typescript
// Habilitar logs de debug
const DEBUG_AUTH = import.meta.env.DEV;

if (DEBUG_AUTH) {
  console.log('Auth State:', useAuthStore.getState());
  console.log('Token:', localStorage.getItem('auth-token'));
  console.log('User:', localStorage.getItem('auth-user'));
}
```

## Futuras Mejoras

1. **Refresh Token**: Implementar refresh tokens separados
2. **Multi-factor Auth**: Soporte para 2FA
3. **Sesiones múltiples**: Gestión de múltiples sesiones
4. **Auditoría**: Logs de autenticación
5. **Rate Limiting**: Protección contra ataques de fuerza bruta
6. **Biometría**: Soporte para autenticación biométrica 