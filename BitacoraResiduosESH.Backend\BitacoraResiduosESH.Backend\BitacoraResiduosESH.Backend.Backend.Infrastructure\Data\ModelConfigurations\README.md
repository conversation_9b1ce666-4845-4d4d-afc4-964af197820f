# Configuraciones de Entidades - Entity Framework

## Descripción

Este directorio contiene las configuraciones de entidades para Entity Framework Core. Las configuraciones permiten
definir cómo se mapean las entidades a la base de datos, siguiendo las reglas estándar de nomenclatura y tipos de datos.

## Reglas Estándar de Base de Datos

### Nomenclatura de Tablas y Campos

#### Tablas

- **✅ CORRECTO**: Nombres en singular y Pascal case
    - `User` (correcto)
    - `Product` (correcto)
    - `Category` (correcto)

- **❌ INCORRECTO**:
    - `mytablenames` (incorrecto, lower case & plural)
    - `myTableName` (incorrecto, camel case)
    - `Users` (incorrecto, plural)

#### Campos

- **✅ CORRECTO**: Nombres en Pascal case
    - `Name` (correcto)
    - `Email` (correcto)
    - `CreatedBy` (correcto)

- **❌ INCORRECTO**:
    - `myfieldnames` (incorrecto, lower case)
    - `myFieldName` (incorrecto, camel case)

#### Restricción Importante

- **EVITAR**: Usar las letras Y y S o números (0-9) como última letra de nombres de tablas y campos
- **RAZÓN**: Causan problemas en Visual Studio

### Tipos de Datos

#### Campos Alfanuméricos

- **✅ USAR**: `NVARCHAR()`
- **❌ NO USAR**: `VARCHAR()` o `CHAR()`
- **RAZÓN**: `NVARCHAR` permite almacenar datos en Unicode que soporta caracteres de otros idiomas

#### Fechas

- **✅ USAR**: `DATETIME2(3)`
- **❌ NO USAR**: `DATETIME`
- **RAZÓN**: `DATETIME` será deprecado en futuras versiones de SQL Server

#### Tamaños de NVARCHAR

- **✅ USAR**: Tamaños específicos cuando sea posible
    - `nvarchar(50)` para códigos cortos
    - `nvarchar(200)` para nombres
    - `nvarchar(1000)` para descripciones
    - `nvarchar(255)` para emails
- **❌ EVITAR**: `NVARCHAR(MAX)` a menos que sea necesario
- **RAZÓN**: `NVARCHAR(MAX)` reserva mucho espacio innecesario

### Claves Primarias y Foráneas

#### Claves Primarias

- **NOMBRE**: `<TableName>PK`
- **POSICIÓN**: Primer campo de la tabla
- **EJEMPLO**: `UserPK`, `ProductPK`

#### Claves Foráneas

- **NOMBRE**: `<TableName>FK`
- **POSICIÓN**: Inmediatamente después del campo PK
- **EJEMPLO**: `UserFK`, `CategoryFK`

#### Razones para esta Nomenclatura

1. **Evitar Confusión**: Diferentes datasets ya incluyen campos ID
2. **Claridad en Código**: Fácil identificar qué tipo de campo se está usando
3. **Trazabilidad**: Fácil identificar relaciones entre tablas

### Columnas Requeridas

**TODAS** las tablas deben tener estos campos como **últimos campos** de la tabla (en este orden):

```sql
UPDATEDBY nvarchar(50) NOT NULL
-- Default: suser_name()

UPDATED datetime2(3) NOT NULL  
-- Default: getutcdate()

ACTIVE bit NOT NULL
-- Default: 1
```

## Configuraciones Base

### BaseEntityConfiguration<T>

Configuración base para todas las entidades que heredan de `BaseEntity`:

#### Propiedades Automáticas

- **Id**: Clave primaria auto-incremental (`<TableName>PK`)
- **Created**: Fecha de creación (`datetime2(3)`, default: `getutcdate()`)
- **CreatedBy**: Usuario que creó (`nvarchar(50)`, default: `suser_name()`)
- **Updated**: Fecha de última actualización (`datetime2(3)`)
- **UpdatedBy**: Usuario que actualizó (`nvarchar(50)`)
- **IsDeleted**: Eliminación lógica (`bit`, default: `false`)
- **Deleted**: Fecha de eliminación (`datetime2(3)`)
- **DeletedBy**: Usuario que eliminó (`nvarchar(50)`)
- **Active**: Estado activo/inactivo (`bit`, default: `true`)

#### Índices Automáticos

- `IX_{TableName}_IsDeleted`
- `IX_{TableName}_Active`
- `IX_{TableName}_Created`
- `IX_{TableName}_Active_IsDeleted`

#### Filtro Global

```csharp
builder.HasQueryFilter(e => !e.IsDeleted);
```

### SimpleEntityConfiguration<T>

Configuración para entidades que heredan de `SimpleEntity`:

#### Propiedades Adicionales

- **Name**: Nombre (`nvarchar(200)`, required)
- **Description**: Descripción (`nvarchar(1000)`, optional)

#### Índices Adicionales

- `IX_{TableName}_Name`
- `IX_{TableName}_Name_Active`
- `IX_{TableName}_Name_Created`

#### Restricciones Adicionales

- `UQ_{TableName}_Name_NotDeleted` (Name único entre elementos no eliminados)

## Cómo Crear una Nueva Configuración

### Paso 1: Determinar el Tipo de Entidad

#### Para Entidades que Heredan de BaseEntity

```csharp
public class MyEntityConfiguration : BaseEntityConfiguration<MyEntity>
{
    protected override string TableName => "MyEntity";
    
    public override void Configure(EntityTypeBuilder<MyEntity> builder)
    {
        base.Configure(builder);
        
        // Configuraciones específicas
        ConfigureSpecificProperties(builder);
        ConfigureSpecificIndexes(builder);
        ConfigureSpecificConstraints(builder);
    }
}
```

#### Para Entidades que Heredan de SimpleEntity

```csharp
public class MyEntityConfiguration : SimpleEntityConfiguration<MyEntity>
{
    protected override string TableName => "MyEntity";
    
    public override void Configure(EntityTypeBuilder<MyEntity> builder)
    {
        base.Configure(builder);
        
        // Configuraciones específicas adicionales
        ConfigureAdditionalProperties(builder);
        ConfigureAdditionalIndexes(builder);
        ConfigureAdditionalConstraints(builder);
    }
}
```

### Paso 2: Configurar Propiedades Específicas

```csharp
protected virtual void ConfigureSpecificProperties(EntityTypeBuilder<MyEntity> builder)
{
    // Propiedad requerida
    builder.Property(e => e.Code)
        .HasColumnName("Code")
        .HasColumnType("nvarchar(50)")
        .IsRequired();
        
    // Propiedad opcional
    builder.Property(e => e.Notes)
        .HasColumnName("Notes")
        .HasColumnType("nvarchar(1000)")
        .IsRequired(false);
        
    // Propiedad numérica
    builder.Property(e => e.Price)
        .HasColumnName("Price")
        .HasColumnType("decimal(18,2)")
        .IsRequired();
}
```

### Paso 3: Configurar Índices Específicos

```csharp
protected virtual void ConfigureSpecificIndexes(EntityTypeBuilder<MyEntity> builder)
{
    // Índice simple
    builder.HasIndex(e => e.Code)
        .HasDatabaseName($"IX_{TableName}_Code");
        
    // Índice compuesto
    builder.HasIndex(e => new { e.Code, e.Active })
        .HasDatabaseName($"IX_{TableName}_Code_Active");
        
    // Índice para ordenamiento
    builder.HasIndex(e => e.Price)
        .HasDatabaseName($"IX_{TableName}_Price");
}
```

### Paso 4: Configurar Restricciones Específicas

```csharp
protected virtual void ConfigureSpecificConstraints(EntityTypeBuilder<MyEntity> builder)
{
    // Restricción única con filtro
    builder.HasIndex(e => e.Code)
        .IsUnique()
        .HasFilter("[IsDeleted] = 0")
        .HasDatabaseName($"UQ_{TableName}_Code_NotDeleted");
}
```

## Ejemplos de Configuraciones

### Ejemplo 1: Entidad Simple (Hereda de SimpleEntity)

```csharp
public class CategoryConfiguration : SimpleEntityConfiguration<Category>
{
    protected override string TableName => "Category";
    
    public override void Configure(EntityTypeBuilder<Category> builder)
    {
        base.Configure(builder);
        // No se necesitan configuraciones adicionales
    }
}
```

**Resultado en BD:**

- Tabla: `Category`
- PK: `CategoryPK`
- Campos: `Id`, `Name`, `Description`, `Created`, `CreatedBy`, `Updated`, `UpdatedBy`, `Active`, `IsDeleted`, `Deleted`,
  `DeletedBy`
- Índices: `IX_Category_IsDeleted`, `IX_Category_Active`, `IX_Category_Created`, `IX_Category_Active_IsDeleted`,
  `IX_Category_Name`, `IX_Category_Name_Active`, `IX_Category_Name_Created`
- Restricciones: `UQ_Category_Name_NotDeleted`

### Ejemplo 2: Entidad Compleja (Hereda de BaseEntity)

```csharp
public class UserConfiguration : BaseEntityConfiguration<User>
{
    protected override string TableName => "User";
    
    public override void Configure(EntityTypeBuilder<User> builder)
    {
        base.Configure(builder);
        
        ConfigureName(builder);
        ConfigureEmail(builder);
        ConfigurePassword(builder);
        ConfigureUserIndexes(builder);
        ConfigureUserConstraints(builder);
    }
    
    protected virtual void ConfigureName(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.Name)
            .HasColumnName("Name")
            .HasColumnType("nvarchar(200)")
            .IsRequired();
    }
    
    protected virtual void ConfigureEmail(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.Email)
            .HasColumnName("Email")
            .HasColumnType("nvarchar(255)")
            .IsRequired();
    }
    
    protected virtual void ConfigurePassword(EntityTypeBuilder<User> builder)
    {
        builder.Property(e => e.Password)
            .HasColumnName("Password")
            .HasColumnType("nvarchar(255)")
            .IsRequired();
    }
    
    protected virtual void ConfigureUserIndexes(EntityTypeBuilder<User> builder)
    {
        builder.HasIndex(e => e.Name)
            .HasDatabaseName($"IX_{TableName}_Name");
            
        builder.HasIndex(e => e.Email)
            .HasDatabaseName($"IX_{TableName}_Email");
            
        builder.HasIndex(e => new { e.Email, e.Active })
            .HasDatabaseName($"IX_{TableName}_Email_Active");
    }
    
    protected virtual void ConfigureUserConstraints(EntityTypeBuilder<User> builder)
    {
        builder.HasIndex(e => e.Email)
            .IsUnique()
            .HasFilter("[IsDeleted] = 0")
            .HasDatabaseName($"UQ_{TableName}_Email_NotDeleted");
    }
}
```

## Registro en AppDbContext

```csharp
public class AppDbContext : DbContext
{
    public DbSet<User> User { get; set; }
    public DbSet<Category> Category { get; set; }
    public DbSet<Product> Product { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Aplicar configuraciones
        modelBuilder.ApplyConfiguration(new UserConfiguration());
        modelBuilder.ApplyConfiguration(new CategoryConfiguration());
        modelBuilder.ApplyConfiguration(new ProductConfiguration());
    }
}
```

## Migraciones

Después de crear o modificar configuraciones:

```bash
# Generar migración
dotnet ef migrations add AddMyEntityConfiguration

# Aplicar migración
dotnet ef database update
```

## Consideraciones Importantes

### Rendimiento

- Los índices mejoran las consultas pero ralentizan las escrituras
- Usar índices solo en campos frecuentemente consultados
- Considerar índices compuestos para consultas complejas

### Restricciones Únicas

- Siempre usar filtros para excluir entidades eliminadas
- Formato: `[IsDeleted] = 0`

### Relaciones

- Considerar el comportamiento de eliminación en cascada
- Usar `DeleteBehavior.Restrict` para evitar eliminaciones accidentales

### Auditoría

- Los campos de auditoría se llenan automáticamente en `SaveChanges`
- Usar `getutcdate()` para fechas y `suser_name()` para usuarios

### Filtros Globales

- Se aplican automáticamente a todas las consultas
- Excluyen entidades eliminadas por defecto
- Se pueden deshabilitar con `IgnoreQueryFilters()` si es necesario 