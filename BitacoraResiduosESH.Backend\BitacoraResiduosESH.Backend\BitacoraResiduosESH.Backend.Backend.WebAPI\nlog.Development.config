<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      autoReload="true"
      internalLogLevel="Debug"
      internalLogFile="logs/internal-nlog.txt">

    <!-- enable asp.net core layout renderers -->
    <extensions>
        <add assembly="NLog.Web.AspNetCore"/>
    </extensions>

    <!-- the targets to write to -->
    <targets>
        <!-- File Target for all log messages with basic details -->
        <target xsi:type="File" name="allfile"
                fileName="logs/${shortdate}/all-${shortdate}.log"
                layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}"/>

        <!-- File Target for own log messages with extra web details -->
        <target xsi:type="File" name="ownFile-web"
                fileName="logs/${shortdate}/own-${shortdate}.log"
                layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}|${callsite}"/>

        <!-- File Target for Entity Framework log messages -->
        <target xsi:type="File" name="efFile"
                fileName="logs/${shortdate}/ef-${shortdate}.log"
                layout="${longdate}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}"/>

        <!-- Console Target for hosting lifetime messages to improve Docker, Visual Studio startup detection -->
        <target xsi:type="Console" name="lifetimeConsole"
                layout="${MicrosoftConsoleLayout}"/>

        <!-- Console Target for all log messages -->
        <target xsi:type="Console" name="console"
                layout="${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}"/>

        <!-- Structured logging target for JSON output -->
        <target xsi:type="File" name="jsonFile"
                fileName="logs/${shortdate}/structured-${shortdate}.json"
                layout="{ 'timestamp': '${longdate}', 'level': '${level}', 'logger': '${logger}', 'message': '${message}', 'exception': '${exception:format=tostring}', 'properties': { 'EventId': '${event-properties:item=EventId_Id:whenEmpty=0}', 'RequestId': '${aspnet-request-id}', 'Url': '${aspnet-request-url}', 'Method': '${aspnet-request-method}', 'UserAgent': '${aspnet-request-useragent}', 'IP': '${aspnet-request-ip}' } }"/>

        <!-- Error log file for critical errors -->
        <target xsi:type="File" name="errorFile"
                fileName="logs/${shortdate}/errors-${shortdate}.log"
                layout="${longdate}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}|${callsite}"/>

        <!-- Performance log file for slow operations -->
        <target xsi:type="File" name="performanceFile"
                fileName="logs/${shortdate}/performance-${shortdate}.log"
                layout="${longdate}|${uppercase:${level}}|${logger}|${message}|duration: ${aspnet-request-duration}"/>

        <!-- Debug log file for detailed debugging -->
        <target xsi:type="File" name="debugFile"
                fileName="logs/${shortdate}/debug-${shortdate}.log"
                layout="${longdate}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|${callsite}"/>
    </targets>

    <!-- rules to map from logger name to target -->
    <rules>
        <!--All logs, including from Microsoft-->
        <logger name="*" minlevel="Trace" writeTo="allfile"/>

        <!-- Entity Framework detailed logging for development -->
        <logger name="Microsoft.EntityFrameworkCore.Database.Command" minlevel="Debug" writeTo="efFile,console"/>
        <logger name="Microsoft.EntityFrameworkCore.Database.Connection" minlevel="Debug" writeTo="efFile,console"/>
        <logger name="Microsoft.EntityFrameworkCore.Infrastructure" minlevel="Debug" writeTo="efFile,console"/>
        <logger name="Microsoft.EntityFrameworkCore.Query" minlevel="Debug" writeTo="efFile,console"/>

        <!-- Own application logging with debug level -->
        <logger name="ToolsMS.Backend.*" minlevel="Trace" writeTo="ownFile-web,console,debugFile"/>

        <!-- Error logging -->
        <logger name="*" minlevel="Error" writeTo="errorFile,console"/>

        <!-- Performance logging -->
        <logger name="*" minlevel="Info" writeTo="performanceFile"
                when="${when:when=starts-with(logger,'Performance')}"/>

        <!-- Structured logging for API requests -->
        <logger name="BitacoraResiduosESH.Backend.Backend.WebAPI.*" minlevel="Debug" writeTo="jsonFile"/>

        <!-- Ensure to catch all information about ASP.NET Core hosting -->
        <logger name="Microsoft.Hosting.Lifetime" minlevel="Info" writeTo="lifetimeConsole"/>

        <!-- Development specific logging -->
        <logger name="Microsoft.AspNetCore.*" minlevel="Debug" writeTo="console"/>
        <logger name="Microsoft.Extensions.*" minlevel="Debug" writeTo="console"/>
    </rules>
</nlog> 