### Controlador Genérico para BaseEntity - Ejemplos de Uso

# =============================================================================
# OPERACIONES DE LECTURA
# =============================================================================

### Obtener elemento por ID
GET https://localhost:7001/api/examplegeneric/1
Accept: application/json

### Obtener elemento por ID (incluir eliminados)
GET https://localhost:7001/api/examplegeneric/1?includeDeleted=true
Accept: application/json

### Obtener todos los elementos
GET https://localhost:7001/api/examplegeneric
Accept: application/json

### Obtener todos los elementos (incluir eliminados)
GET https://localhost:7001/api/examplegeneric?includeDeleted=true
Accept: application/json

### Obtener elementos paginados
GET https://localhost:7001/api/examplegeneric/paged?pageNumber=1&pageSize=10
Accept: application/json

### Obtener elementos paginados con filtros
GET https://localhost:7001/api/examplegeneric/paged?pageNumber=1&pageSize=10&active=true&createdFrom=2024-01-01&
    createdTo=2024-12-31
Accept: application/json

### Obtener solo elementos activos
GET https://localhost:7001/api/examplegeneric/active
Accept: application/json

### Contar elementos
GET https://localhost:7001/api/examplegeneric/count
Accept: application/json

### Contar elementos (incluir eliminados)
GET https://localhost:7001/api/examplegeneric/count?includeDeleted=true
Accept: application/json

# =============================================================================
# OPERACIONES DE ESCRITURA
# =============================================================================

### Agregar un nuevo elemento
POST https://localhost:7001/api/examplegeneric
Content-Type: application/json

{
  "name": "Nuevo Elemento Genérico",
  "description": "Descripción del nuevo elemento",
  "categoryId": 1
}

### Agregar múltiples elementos
POST https://localhost:7001/api/examplegeneric/batch
Content-Type: application/json

[
  {
    "name": "Elemento 1",
    "description": "Descripción del elemento 1",
    "categoryId": 1
  },
  {
    "name": "Elemento 2",
    "description": "Descripción del elemento 2",
    "categoryId": 2
  }
]

### Actualizar un elemento
PUT https://localhost:7001/api/examplegeneric
Content-Type: application/json

{
  "id": 1,
  "name": "Elemento Actualizado",
  "description": "Descripción actualizada",
  "categoryId": 2
}

### Actualizar múltiples elementos
PUT https://localhost:7001/api/examplegeneric/batch
Content-Type: application/json

[
  {
    "id": 1,
    "name": "Elemento 1 Actualizado",
    "description": "Descripción actualizada 1",
    "categoryId": 1
  },
  {
    "id": 2,
    "name": "Elemento 2 Actualizado",
    "description": "Descripción actualizada 2",
    "categoryId": 2
  }
]

# =============================================================================
# OPERACIONES DE ELIMINACIÓN
# =============================================================================

### Eliminar elemento (soft delete)
DELETE https://localhost:7001/api/examplegeneric/1

### Eliminar múltiples elementos (soft delete)
DELETE https://localhost:7001/api/examplegeneric/batch
Content-Type: application/json

[
  1,
  2,
  3
]

### Eliminar elemento permanentemente (hard delete)
DELETE https://localhost:7001/api/examplegeneric/1/permanent

### Eliminar múltiples elementos permanentemente (hard delete)
DELETE https://localhost:7001/api/examplegeneric/batch/permanent
Content-Type: application/json

[
  1,
  2,
  3
]

# =============================================================================
# OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN
# =============================================================================

### Activar elemento
PATCH https://localhost:7001/api/examplegeneric/1/activate

### Desactivar elemento
PATCH https://localhost:7001/api/examplegeneric/1/deactivate

# =============================================================================
# OPERACIONES DE VALIDACIÓN
# =============================================================================

### Verificar existencia de elemento
GET https://localhost:7001/api/examplegeneric/1/exists
Accept: application/json

### Verificar existencia de elemento (incluir eliminados)
GET https://localhost:7001/api/examplegeneric/1/exists?includeDeleted=true
Accept: application/json

# =============================================================================
# OPERACIONES DE BÚSQUEDA
# =============================================================================

### Búsqueda personalizada (implementación específica)
GET https://localhost:7001/api/examplegeneric/search?predicate=test&includeDeleted=false
Accept: application/json

# =============================================================================
# ENDPOINTS ESPECÍFICOS DEL EJEMPLO
# =============================================================================

### Obtener información específica del controlador
GET https://localhost:7001/api/examplegeneric/example-specific
Accept: application/json

### Obtener elementos por categoría (endpoint específico)
GET https://localhost:7001/api/examplegeneric/category/1
Accept: application/json

### Obtener elementos por categoría (incluir eliminados)
GET https://localhost:7001/api/examplegeneric/category/1?includeDeleted=true
Accept: application/json

# =============================================================================
# EJEMPLOS CON HEADERS DE AUTORIZACIÓN
# =============================================================================

### Obtener elemento con autorización (ejemplo)
GET https://localhost:7001/api/examplegeneric/1
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

### Agregar elemento con autorización (ejemplo)
POST https://localhost:7001/api/examplegeneric
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "name": "Elemento con Autorización",
  "description": "Descripción del elemento",
  "categoryId": 1
}

# =============================================================================
# EJEMPLOS DE FILTROS AVANZADOS
# =============================================================================

### Filtros por fecha de creación
GET https://localhost:7001/api/examplegeneric/paged?createdFrom=2024-01-01T00:00:00Z&createdTo=2024-12-31T23:59:59Z
Accept: application/json

### Filtros por usuario que creó
GET https://localhost:7001/api/examplegeneric/paged?createdBy=admin
Accept: application/json

### Filtros por fecha de actualización
GET https://localhost:7001/api/examplegeneric/paged?updatedFrom=2024-06-01T00:00:00Z&updatedTo=2024-06-30T23:59:59Z
Accept: application/json

### Filtros por usuario que actualizó
GET https://localhost:7001/api/examplegeneric/paged?updatedBy=user1
Accept: application/json

### Filtros combinados
GET https://localhost:7001/api/examplegeneric/paged?active=true&createdBy=admin&pageNumber=1&pageSize=20
Accept: application/json

# =============================================================================
# NOTAS IMPORTANTES
# =============================================================================

# 1. Reemplaza 'examplegeneric' con el nombre de tu controlador específico
# 2. Ajusta los IDs y datos según tu base de datos
# 3. Para usar autenticación, reemplaza 'YOUR_JWT_TOKEN_HERE' con un token válido
# 4. Las fechas deben estar en formato ISO 8601
# 5. Los filtros de fecha son opcionales y pueden usarse individualmente
# 6. El parámetro 'includeDeleted' es opcional y por defecto es 'false'
# 7. La paginación usa 'pageNumber' (base 1) y 'pageSize' (mínimo 1)
# 8. Los endpoints de batch requieren arrays en el body
# 9. Los endpoints de eliminación permanente son irreversibles
# 10. El método Search es virtual y debe implementarse en controladores específicos 