# Servicios de Aplicación

Este directorio contiene todos los servicios de la capa de aplicación que implementan la lógica de negocio del sistema.

## Servicios Disponibles

### SimpleEntityService<T> (Servicio Base)
- **Archivo**: `SimpleEntityService.cs`
- **Propósito**: Clase base para servicios que manejan entidades simples
- **Funcionalidades**:
  - CRUD básico (Create, Read, Update, Delete)
  - Paginación y filtrado
  - Búsquedas por nombre y descripción
  - Activación/desactivación
  - Validaciones estándar

### RoleService
- **Archivo**: `RoleService.cs`
- **Entidad**: `Role`
- **Hereda de**: `SimpleEntityService<Role, RoleDto, CreateRoleDto, UpdateRoleDto, RoleFilterDto>`
- **Funcionalidades**: CRUD básico de roles sin lógica adicional

### ToolTypeService
- **Archivo**: `ToolTypeService.cs`
- **Entidad**: `ToolType`
- **Hereda de**: `SimpleEntityService<ToolType, ToolTypeDto, CreateToolTypeDto, UpdateToolTypeDto, ToolTypeFilterDto>`
- **Funcionalidades**: CRUD básico de tipos de herramientas sin lógica adicional

### ToolStatusService
- **Archivo**: `ToolStatusService.cs`
- **Entidad**: `ToolStatus`
- **Hereda de**: `SimpleEntityService<ToolStatus, ToolStatusDto, CreateToolStatusDto, UpdateToolStatusDto, ToolStatusFilterDto>`
- **Funcionalidades**: CRUD básico de estados de herramientas sin lógica adicional

### LocationTypeService
- **Archivo**: `LocationTypeService.cs`
- **Entidad**: `LocationType`
- **Hereda de**: `SimpleEntityService<LocationType, LocationTypeDto, CreateLocationTypeDto, UpdateLocationTypeDto, LocationTypeFilterDto>`
- **Funcionalidades**: CRUD básico de tipos de ubicación sin lógica adicional

### LocationService
- **Archivo**: `LocationService.cs`
- **Entidad**: `Location`
- **Hereda de**: `SimpleEntityService<Location, LocationDto, CreateLocationDto, UpdateLocationDto, LocationFilterDto>`
- **Funcionalidades**:
  - CRUD básico de ubicaciones
  - Validación de existencia y estado activo del LocationType
  - Mapeo automático de información del LocationType
  - Búsqueda de ubicaciones por tipo
- **Dependencias**: `ILocationRepository`, `ILocationTypeRepository`

### UserService
- **Archivo**: `UserService.cs`
- **Entidad**: `User`
- **Hereda de**: `SimpleEntityService<User, UserDto, CreateUserDto, UpdateUserDto, UserFilterDto>`
- **Funcionalidades**:
  - CRUD básico de usuarios
  - Validación de existencia y estado activo del Role
  - Mapeo automático de información del Role
  - Búsquedas específicas por número de empleado
  - Hash de contraseñas
- **Dependencias**: `IUserRepository`, `IRoleRepository`, `IPasswordService`

## Patrón de Implementación

### Servicios Simples
Para entidades que no requieren lógica adicional, simplemente heredan de `SimpleEntityService`:

```csharp
public class ExampleService : SimpleEntityService<Example, ExampleDto, CreateExampleDto, UpdateExampleDto, ExampleFilterDto>, IExampleService
{
    public ExampleService(IExampleRepository repository) : base(repository)
    {
        // No se agrega lógica adicional
    }
}
```

### Servicios con Relaciones
Para entidades que tienen relaciones con otras entidades:

```csharp
public class LocationService : SimpleEntityService<Location, LocationDto, CreateLocationDto, UpdateLocationDto, LocationFilterDto>, ILocationService
{
    private readonly ILocationRepository _locationRepository;
    private readonly ILocationTypeRepository _locationTypeRepository;

    public LocationService(ILocationRepository repository, ILocationTypeRepository locationTypeRepository) : base(repository)
    {
        _locationRepository = repository;
        _locationTypeRepository = locationTypeRepository;
    }

    // Sobrescribir métodos para agregar validaciones y lógica específica
    public override async Task<LocationDto> CreateAsync(CreateLocationDto createDto)
    {
        // Validar relaciones
        // Lógica específica
        // Mapeo customizado
    }
}
```

## Funcionalidades Comunes

### Operaciones CRUD
- `CreateAsync(CreateDto)`: Crear nueva entidad
- `GetByIdAsync(int id)`: Obtener por ID
- `GetAllAsync()`: Obtener todas (paginado)
- `UpdateAsync(int id, UpdateDto)`: Actualizar existente
- `DeleteAsync(int id)`: Eliminar (soft delete)

### Operaciones de Estado
- `ActivateAsync(int id)`: Activar entidad
- `DeactivateAsync(int id)`: Desactivar entidad
- `ExistsAsync(int id)`: Verificar existencia

### Búsquedas
- `SearchByNameAsync(string name)`: Buscar por nombre
- `SearchByDescriptionAsync(string description)`: Buscar por descripción
- `GetActiveAsync()`: Obtener solo activos

### Validaciones Automáticas
- Validación de DTOs con Data Annotations
- Verificación de existencia antes de operaciones
- Validación de estado activo para relaciones
- Prevención de eliminación de entidades relacionadas

## Manejo de Errores

Los servicios manejan los siguientes tipos de errores:
- `KeyNotFoundException`: Entidad no encontrada
- `ArgumentException`: Datos inválidos o relaciones inexistentes
- `InvalidOperationException`: Operaciones no permitidas por reglas de negocio
- `ValidationException`: Errores de validación de DTOs

## Logging

Todos los servicios utilizan NLog para registro de operaciones:
- Operaciones CRUD exitosas
- Errores y excepciones
- Validaciones fallidas
- Información de auditoría 