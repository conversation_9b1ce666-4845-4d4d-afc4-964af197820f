﻿namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class BitacoraEntryService(IBitacoraEntryRepository repository)
    : GenericService<BitacoraEntry, BitacoraEntryDto, BitacoraEntryFilterDto, CreateBitacoraEntryDto, UpdateBitacoraEntryDto>(repository), IBitacoraEntryService
{

    protected override BitacoraEntryDto MapToDto(BitacoraEntry entity)
    {
        var dto = base.MapToDto(entity);
        dto.Comments = entity.Comments;

        // Map additional properties
        dto.WasteTypeId = entity.WasteTypeId;
        dto.GrossWeight = entity.GrossWeight;
        dto.Tare = entity.Tare;
        dto.NetWeightLB = entity.NetWeightLB;
        dto.NetWeightKG = entity.NetWeightKG;
        dto.UnitPrice = entity.UnitPrice;
        dto.ContainerTypeId = entity.ContainerTypeId;
        dto.AreaId = entity.AreaId;
        dto.EntryDate = entity.EntryDate;
        dto.EntryDateString = entity.EntryDate.ToString(DateFormats.LONG_DATE_TIME);
        dto.DepartureDate = entity.DepartureDate;
        dto.DepartureDateString = entity.DepartureDate?.ToString(DateFormats.LONG_DATE_TIME);
        dto.EnteredBy = entity.EnteredBy;
        
        dto.Active = entity.Active;
        dto.Created = entity.Created;
        dto.CreatedString = entity.Created.ToString(DateFormats.LONG_DATE_TIME);
        dto.CreatedBy = entity.CreatedBy;
        dto.Updated = entity.Updated;
        dto.UpdatedString = entity.Updated?.ToString(DateFormats.LONG_DATE_TIME);
        dto.UpdatedBy = entity.UpdatedBy;
        dto.IsDeleted = entity.IsDeleted;
        dto.Deleted = entity.Deleted;
        dto.DeletedString = entity.Deleted?.ToString(DateFormats.LONG_DATE_TIME);
        dto.DeletedBy = entity.DeletedBy;

        // Map navigation properties if loaded
        dto.WasteType = new WasteTypeDto
        {
            Id = entity.WasteType.Id,
            Name = entity.WasteType.Name,
            Description = entity.WasteType.Description,
            Active = entity.WasteType.Active,
            Created = entity.WasteType.Created,
            CreatedString = entity.WasteType.Created.ToString(DateFormats.LONG_DATE_TIME),
            CreatedBy = entity.WasteType.CreatedBy,
            Updated = entity.WasteType.Updated,
            UpdatedString = entity.WasteType.Updated?.ToString(DateFormats.LONG_DATE_TIME),
            UpdatedBy = entity.WasteType.UpdatedBy,
            IsDeleted = entity.WasteType.IsDeleted,
            Deleted = entity.WasteType.Deleted,
            DeletedString = entity.WasteType.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
            DeletedBy = entity.WasteType.DeletedBy
        };

        dto.ContainerType = new ContainerTypeDto
        {
            Id = entity.ContainerType.Id,
            Name = entity.ContainerType.Name,
            Description = entity.ContainerType.Description,
            Active = entity.ContainerType.Active,
            Created = entity.ContainerType.Created,
            CreatedString = entity.ContainerType.Created.ToString(DateFormats.LONG_DATE_TIME),
            CreatedBy = entity.ContainerType.CreatedBy,
            Updated = entity.ContainerType.Updated,
            UpdatedString = entity.ContainerType.Updated?.ToString(DateFormats.LONG_DATE_TIME),
            UpdatedBy = entity.ContainerType.UpdatedBy,
            IsDeleted = entity.ContainerType.IsDeleted,
            Deleted = entity.ContainerType.Deleted,
            DeletedString = entity.ContainerType.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
            DeletedBy = entity.ContainerType.DeletedBy
        };

        dto.Area = new AreaDto
        {
            Id = entity.Area.Id,
            Name = entity.Area.Name,
            Description = entity.Area.Description,
            Active = entity.Area.Active,
            Created = entity.Area.Created,
            CreatedString = entity.Area.Created.ToString(DateFormats.LONG_DATE_TIME),
            CreatedBy = entity.Area.CreatedBy,
            Updated = entity.Area.Updated,
            UpdatedString = entity.Area.Updated?.ToString(DateFormats.LONG_DATE_TIME),
            UpdatedBy = entity.Area.UpdatedBy,
            IsDeleted = entity.Area.IsDeleted,
            Deleted = entity.Area.Deleted,
            DeletedString = entity.Area.Deleted?.ToString(DateFormats.LONG_DATE_TIME),
            DeletedBy = entity.Area.DeletedBy
        };

        return dto;
    }

    protected override BitacoraEntry MapToEntity(CreateBitacoraEntryDto dto)
    {
        return new BitacoraEntry
        {
            Comments = dto.Comments,
            WasteTypeId = dto.WasteTypeId,
            GrossWeight = dto.GrossWeight,
            Tare = dto.Tare,
            NetWeightLB = dto.NetWeightLB,
            NetWeightKG = dto.NetWeightKG,
            UnitPrice = dto.UnitPrice,
            ContainerTypeId = dto.ContainerTypeId,
            AreaId = dto.AreaId,
            EntryDate = dto.EntryDate,
            DepartureDate = dto.DepartureDate,
            EnteredBy = dto.EnteredBy,
            Active = dto.Active
        };
    }

    protected override BitacoraEntry MapToEntity(UpdateBitacoraEntryDto dto)
    {
        return new BitacoraEntry
        {
            Id = dto.Id,
            Comments = dto.Comments,
            WasteTypeId = dto.WasteTypeId,
            GrossWeight = dto.GrossWeight,
            Tare = dto.Tare,
            NetWeightLB = dto.NetWeightLB,
            NetWeightKG = dto.NetWeightKG,
            UnitPrice = dto.UnitPrice,
            ContainerTypeId = dto.ContainerTypeId,
            AreaId = dto.AreaId,
            EntryDate = dto.EntryDate,
            DepartureDate = dto.DepartureDate,
            EnteredBy = dto.EnteredBy
        };
    }
}
