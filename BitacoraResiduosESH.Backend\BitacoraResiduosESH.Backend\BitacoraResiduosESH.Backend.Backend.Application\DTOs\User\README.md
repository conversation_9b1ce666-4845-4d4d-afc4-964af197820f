# Sistema de Usuarios (User)

## Descripción General

El sistema de usuarios proporciona una implementación completa para el manejo de entidades `User` basada en la
arquitectura genérica del sistema. Incluye DTOs específicos, repositorio especializado, servicio con lógica de negocio y
controlador con endpoints específicos.

## Arquitectura

### DTOs

#### UserDto

- **Propósito**: Representación de datos para transferencia de usuarios
- **Hereda de**: `BaseEntityDto`
- **Propiedades**:
    - `Id`: Identificador único
    - `Name`: Nombre del usuario
    - `Email`: Email del usuario (opcional)
    - `Password`: Contraseña del usuario
    - Propiedades de auditoría heredadas de `BaseEntityDto`

#### UserFilterDto

- **Propósito**: Filtros para búsqueda y paginación de usuarios
- **Hereda de**: `BaseEntityFilterDto`
- **Propiedades**:
    - `Name`: Filtro por nombre (búsqueda parcial)
    - `Email`: Filtro por email (búsqueda parcial)
    - Propiedades de paginación heredadas de `BaseEntityFilterDto`

#### CreateUserDto

- **Propósito**: Datos para crear nuevos usuarios
- **Hereda de**: `CreateBaseEntityDto`
- **Propiedades**:
    - `Name`: Nombre del usuario (requerido, máximo 200 caracteres)
    - `Email`: Email del usuario (opcional, formato válido, máximo 255 caracteres)
    - `Password`: Contraseña del usuario (requerida, 6-100 caracteres)
- **Validaciones**:
    - Validación de email único
    - Validación de formato de email
    - Validación de longitud de contraseña

#### UpdateUserDto

- **Propósito**: Datos para actualizar usuarios existentes
- **Hereda de**: `UpdateBaseEntityDto`
- **Propiedades**:
    - `Id`: ID del usuario a actualizar
    - `Name`: Nombre del usuario (requerido, máximo 200 caracteres)
    - `Email`: Email del usuario (opcional, formato válido, máximo 255 caracteres)
    - `Password`: Contraseña del usuario (requerida, 6-100 caracteres)
- **Validaciones**:
    - Validación de email único (excluyendo el usuario actual)
    - Validación de formato de email
    - Validación de longitud de contraseña

### Repositorio

#### IUserRepository

- **Hereda de**: `IGenericRepository<User>`
- **Métodos específicos**:
    - `GetByNameAsync(string name, bool includeDeleted)`: Búsqueda por nombre
    - `GetByEmailAsync(string email, bool includeDeleted)`: Búsqueda por email
    - `GetByEmailExactAsync(string email, bool includeDeleted)`: Búsqueda exacta por email
    - `ExistsByEmailAsync(string email, int? excludeId, bool includeDeleted)`: Verificación de existencia

### Servicio

#### IUserService

- **Hereda de**: `IGenericService<User, UserDto, UserFilterDto>`
- **Métodos específicos**:
    - `GetByNameAsync(string name, bool includeDeleted)`: Obtener usuarios por nombre
    - `GetByEmailAsync(string email, bool includeDeleted)`: Obtener usuarios por email
    - `GetByEmailExactAsync(string email, bool includeDeleted)`: Obtener usuario por email exacto
    - `ExistsByEmailAsync(string email, int? excludeId, bool includeDeleted)`: Verificar existencia por email
    - `CreateAsync(CreateUserDto dto, string createdBy)`: Crear usuario con DTO específico
    - `UpdateAsync(int id, UpdateUserDto dto, string updatedBy)`: Actualizar usuario con DTO específico

#### UserService

- **Implementación**: Lógica de negocio específica para usuarios
- **Características**:
    - Validación de email único en creación y actualización
    - Mapeo automático entre entidades y DTOs
    - Logging detallado de operaciones
    - Manejo de errores específicos

### Controlador

#### UserController

- **Hereda de**: `GenericController<User, UserDto, UserFilterDto>`
- **Endpoints específicos**:
    - `GET /api/User/search/name`: Búsqueda por nombre
    - `GET /api/User/search/email`: Búsqueda por email
    - `GET /api/User/search/email/exact`: Búsqueda exacta por email
    - `GET /api/User/exists/email`: Verificación de existencia por email
    - `POST /api/User/create`: Crear usuario con DTO específico
    - `PUT /api/User/update/{id}`: Actualizar usuario con DTO específico
    - `GET /api/User/search/custom`: Búsqueda personalizada

## Endpoints Disponibles

### Endpoints Genéricos (heredados)

- `GET /api/User`: Obtener usuarios paginados
- `GET /api/User/{id}`: Obtener usuario por ID
- `POST /api/User`: Crear usuario (método genérico)
- `PUT /api/User/{id}`: Actualizar usuario (método genérico)
- `DELETE /api/User/{id}`: Eliminar usuario (soft delete)
- `PATCH /api/User/{id}/activate`: Activar usuario
- `PATCH /api/User/{id}/deactivate`: Desactivar usuario
- `GET /api/User/search`: Búsqueda genérica

### Endpoints Específicos

- `GET /api/User/search/name?name={name}`: Búsqueda por nombre
- `GET /api/User/search/email?email={email}`: Búsqueda por email
- `GET /api/User/search/email/exact?email={email}`: Búsqueda exacta por email
- `GET /api/User/exists/email?email={email}&excludeId={id}`: Verificar existencia
- `POST /api/User/create`: Crear usuario con validaciones específicas
- `PUT /api/User/update/{id}`: Actualizar usuario con validaciones específicas
- `GET /api/User/search/custom?predicate={predicate}`: Búsqueda personalizada

## Casos de Uso

### 1. Registro de Usuario

```http
POST /api/User/create
{
  "name": "Juan Pérez",
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

### 2. Actualización de Perfil

```http
PUT /api/User/update/1
{
  "id": 1,
  "name": "Juan Carlos Pérez",
  "email": "<EMAIL>",
  "password": "NewSecurePassword456!"
}
```

### 3. Búsqueda de Usuarios

```http
GET /api/User/search/name?name=garcia
GET /api/User/search/email?email=empresa.com
```

### 4. Verificación de Disponibilidad

```http
GET /api/User/exists/email?email=<EMAIL>
GET /api/User/exists/email?email=<EMAIL>&excludeId=1
```

## Validaciones

### Creación de Usuario

- Nombre requerido (máximo 200 caracteres)
- Email opcional pero debe ser válido si se proporciona
- Contraseña requerida (6-100 caracteres)
- Email único en el sistema

### Actualización de Usuario

- Todas las validaciones de creación
- Email único excluyendo el usuario actual
- ID en URL debe coincidir con ID en DTO

### Búsquedas

- Parámetros de búsqueda no pueden estar vacíos
- Validación de formato de email para búsquedas por email

## Consideraciones de Seguridad

### Contraseñas

- **Actual**: Las contraseñas se almacenan en texto plano (NO recomendado para producción)
- **Recomendado**: Implementar hashing de contraseñas (bcrypt, Argon2, etc.)
- **Validación**: Longitud mínima de 6 caracteres

### Emails

- Validación de formato de email
- Verificación de unicidad
- Búsqueda case-insensitive

### Auditoría

- Registro de usuario que realiza cada operación
- Timestamps de creación, actualización y eliminación
- Soft delete para mantener historial

## Logging

El sistema incluye logging detallado para:

- Operaciones CRUD
- Búsquedas y filtros
- Validaciones fallidas
- Errores de negocio
- Intentos de acceso no autorizado

## Próximos Pasos

### Mejoras de Seguridad

1. **Hashing de contraseñas**: Implementar bcrypt o Argon2
2. **Validación de fortaleza**: Requerir caracteres especiales, números, etc.
3. **Autenticación**: Integrar con sistema de autenticación
4. **Autorización**: Implementar roles y permisos

### Funcionalidades Adicionales

1. **Recuperación de contraseña**: Sistema de reset por email
2. **Verificación de email**: Confirmación de cuenta
3. **Perfiles de usuario**: Información adicional
4. **Historial de cambios**: Tracking de modificaciones

### Optimizaciones

1. **Caché**: Implementar Redis para búsquedas frecuentes
2. **Índices**: Optimizar consultas de base de datos
3. **Paginación**: Mejorar rendimiento en grandes volúmenes
4. **Búsqueda full-text**: Implementar Elasticsearch

## Archivos Relacionados

- **DTOs**: `UserDto.cs`, `UserFilterDto.cs`, `CreateUserDto.cs`, `UpdateUserDto.cs`
- **Interfaces**: `IUserRepository.cs`, `IUserService.cs`
- **Implementaciones**: `UserRepository.cs`, `UserService.cs`
- **Controlador**: `UserController.cs`
- **Pruebas**: `User.http` (ejemplos de uso)
- **Documentación**: Este archivo README.md 