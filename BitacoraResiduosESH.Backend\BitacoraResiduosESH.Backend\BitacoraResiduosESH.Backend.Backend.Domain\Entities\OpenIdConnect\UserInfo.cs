using System.Text.Json.Serialization;

namespace BitacoraResiduosESH.Backend.Backend.Domain.Entities.OpenIdConnect;

/// <summary>
///     Representa la información del usuario de OpenID Connect
/// </summary>
public class UserInfo
{
    public string Sub { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("given_name")] public string GivenName { get; set; } = string.Empty;

    [JsonPropertyName("family_name")] public string FamilyName { get; set; } = string.Empty;

    [JsonPropertyName("preferred_username")]
    public string PreferedUserName { get; set; } = string.Empty;

    public string NetworkId { get; set; } = string.Empty;

    [JsonPropertyName("p1.region")] public string Region { get; set; } = string.Empty;

    [JsonPropertyName("updated_at")] public long UpdatedAt { get; set; }

    /// <summary>
    ///     Obtiene la fecha de actualización convertida desde timestamp epoch
    /// </summary>
    public DateTime UpdatedAtDateTime => DateTimeOffset.FromUnixTimeSeconds(UpdatedAt).DateTime;
}