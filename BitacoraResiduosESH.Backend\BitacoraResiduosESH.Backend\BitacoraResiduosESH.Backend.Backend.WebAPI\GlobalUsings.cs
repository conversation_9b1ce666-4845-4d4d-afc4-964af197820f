// Global using directives

global using System.Text;
global using Microsoft.AspNetCore.Authentication;
global using Microsoft.AspNetCore.Authentication.JwtBearer;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.OpenApi;
global using Microsoft.IdentityModel.Tokens;
global using Microsoft.OpenApi.Models;
global using BitacoraResiduosESH.Backend.Backend.Application.Common;
global using BitacoraResiduosESH.Backend.Backend.Application.Constants;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.SimpleEntity;
global using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Data;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Extensions;
global using NLog;
global using NLog.Web;
global using Scalar.AspNetCore;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.Auth;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.ContainerType;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.User;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.WasteType;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities.OpenIdConnect;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities.Shared;
global using BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Services;
global using ILogger = NLog.ILogger;