﻿namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;

public interface IGenericRepository<T> where T : BaseEntity
{
    // Read operations
    Task<T?> GetByIdAsync(int id, bool includeDeleted = false);
    Task<PagedResponse<T>> GetAllAsync(PaginationFilter filter);
    Task<PagedResponse<T>> GetPagedAsync(PaginationFilter filter);
    Task<PagedResponse<T>> GetActiveAsync(PaginationFilter filter);
    Task<bool> ExistsAsync(int id, bool includeDeleted = false);
    Task<int> CountAsync(bool includeDeleted = false);

    // Write operations
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task<T> UpdateAsync(T entity);
    Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities);

    // Delete operations
    Task<bool> DeleteAsync(int id, string deletedBy);
    Task<bool> DeleteAsync(T entity, string deletedBy);
    Task<bool> DeleteRangeAsync(IEnumerable<int> ids, string deletedBy);
    Task<bool> DeleteRangeAsync(IEnumerable<T> entities, string deletedBy);
    Task<bool> HardDeleteAsync(int id);
    Task<bool> HardDeleteAsync(T entity);
    Task<bool> HardDeleteRangeAsync(IEnumerable<int> ids);
    Task<bool> HardDeleteRangeAsync(IEnumerable<T> entities);

    // Activation/deactivations operations
    Task<bool> ActivateAsync(int id, string updatedBy);
    Task<bool> DeactivateAsync(int id, string updatedBy);
    Task<bool> ActivateAsync(T entity, string updatedBy);
    Task<bool> DeactivateAsync(T entity, string updatedBy);

    // Search operations
    Task<PagedResponse<T>> FindAsync(Expression<Func<T, bool>> predicate, PaginationFilter filter,
        bool includeDeleted = false);

    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);
    Task<T> FirstAsync(Expression<Func<T, bool>> predicate, bool includeDeleted = false);
}