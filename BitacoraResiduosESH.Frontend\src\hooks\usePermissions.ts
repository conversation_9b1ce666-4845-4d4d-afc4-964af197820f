import {useAuthStore} from '../stores/authStore';
import {PERMISSIONS, ROLES} from '../types/auth';

/**
 * Hook para verificar permisos basados en roles
 */
export const usePermissions = () => {
    const {user, hasRole} = useAuthStore();

    // Verificar si el usuario tiene un rol específico
    const hasUserRole = (role: string | string[]): boolean => {
        return hasRole(role);
    };

    // Verificar si el usuario es administrador
    const isAdmin = (): boolean => {
        return hasRole(ROLES.ADMIN);
    };

    // Verificar si el usuario es manager
    const isManager = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    // Verificar permisos específicos
    const canCreateUser = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    const canUpdateUser = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    const canDeleteUser = (): boolean => {
        return hasRole(ROLES.ADMIN);
    };

    const canViewUsers = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    const canCreateRole = (): boolean => {
        return hasRole(ROLES.ADMIN);
    };

    const canUpdateRole = (): boolean => {
        return hasRole(ROLES.ADMIN);
    };

    const canDeleteRole = (): boolean => {
        return hasRole(ROLES.ADMIN);
    };

    const canViewRoles = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    const canCreateEntity = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER, ROLES.OPERATOR]);
    };

    const canUpdateEntity = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER, ROLES.OPERATOR]);
    };

    const canDeleteEntity = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    const canViewEntities = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER, ROLES.OPERATOR, ROLES.USER]);
    };

    // Verificar permisos del sistema
    const canAccessSystemAdmin = (): boolean => {
        return hasRole(ROLES.ADMIN);
    };

    const canAccessSystemConfig = (): boolean => {
        return hasRole([ROLES.ADMIN, ROLES.MANAGER]);
    };

    // Función genérica para verificar cualquier permiso
    const hasPermission = (permission: string): boolean => {
        switch (permission) {
            case PERMISSIONS.USER_CREATE:
                return canCreateUser();
            case PERMISSIONS.USER_READ:
                return canViewUsers();
            case PERMISSIONS.USER_UPDATE:
                return canUpdateUser();
            case PERMISSIONS.USER_DELETE:
                return canDeleteUser();
            case PERMISSIONS.ROLE_CREATE:
                return canCreateRole();
            case PERMISSIONS.ROLE_READ:
                return canViewRoles();
            case PERMISSIONS.ROLE_UPDATE:
                return canUpdateRole();
            case PERMISSIONS.ROLE_DELETE:
                return canDeleteRole();
            case PERMISSIONS.ENTITY_CREATE:
                return canCreateEntity();
            case PERMISSIONS.ENTITY_READ:
                return canViewEntities();
            case PERMISSIONS.ENTITY_UPDATE:
                return canUpdateEntity();
            case PERMISSIONS.ENTITY_DELETE:
                return canDeleteEntity();
            case PERMISSIONS.SYSTEM_ADMIN:
                return canAccessSystemAdmin();
            case PERMISSIONS.SYSTEM_CONFIG:
                return canAccessSystemConfig();
            default:
                return false;
        }
    };

    return {
        // Estado del usuario
        user,
        userRole: user?.role,

        // Verificaciones de rol
        hasUserRole,
        isAdmin,
        isManager,

        // Permisos de usuarios
        canCreateUser,
        canUpdateUser,
        canDeleteUser,
        canViewUsers,

        // Permisos de roles
        canCreateRole,
        canUpdateRole,
        canDeleteRole,
        canViewRoles,

        // Permisos de entidades
        canCreateEntity,
        canUpdateEntity,
        canDeleteEntity,
        canViewEntities,

        // Permisos del sistema
        canAccessSystemAdmin,
        canAccessSystemConfig,

        // Función genérica
        hasPermission,
    };
}; 