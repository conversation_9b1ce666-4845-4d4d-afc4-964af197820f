import {create} from 'zustand'

type Theme = 'light' | 'dark'

interface ThemeState {
    theme: Theme
    toggleTheme: () => void
    setTheme: (theme: Theme) => void
}

// Función para aplicar el tema al documento
const applyThemeToDocument = (theme: Theme) => {
    const root = document.documentElement

    if (theme === 'dark') {
        root.classList.add('dark')
    } else {
        root.classList.remove('dark')
    }

    // También actualizar el meta theme-color para móviles
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
        metaThemeColor.setAttribute(
            'content',
            theme === 'dark' ? '#1f2937' : '#ffffff'
        )
    }
}

// Obtener tema inicial del localStorage
const getInitialTheme = (): Theme => {
    if (typeof window !== 'undefined') {
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme === 'dark' || savedTheme === 'light') {
            return savedTheme
        }
    }
    return 'light'
}

export const useThemeStore = create<ThemeState>((set, get) => ({
    theme: getInitialTheme(),
    toggleTheme: () => {
        const currentTheme = get().theme
        const newTheme = currentTheme === 'light' ? 'dark' : 'light'
        set({theme: newTheme})
        localStorage.setItem('theme', newTheme)
        applyThemeToDocument(newTheme)
    },
    setTheme: (theme: Theme) => {
        set({theme})
        localStorage.setItem('theme', theme)
        applyThemeToDocument(theme)
    },
}))

// Aplicar el tema inicial al cargar
if (typeof window !== 'undefined') {
    // Esperar a que el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme')
            if (savedTheme === 'dark' || savedTheme === 'light') {
                applyThemeToDocument(savedTheme)
            }
        })
    } else {
        // DOM ya está listo
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme === 'dark' || savedTheme === 'light') {
            applyThemeToDocument(savedTheme)
        }
    }
} 