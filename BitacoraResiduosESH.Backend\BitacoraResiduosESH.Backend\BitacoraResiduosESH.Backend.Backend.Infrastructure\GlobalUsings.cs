// Global using directives

global using System.IdentityModel.Tokens.Jwt;
global using System.Linq.Expressions;
global using System.Security.Claims;
global using System.Text;
global using System.Text.Json;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Metadata.Builders;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Microsoft.IdentityModel.Tokens;
global using BitacoraResiduosESH.Backend.Backend.Application.Common;
global using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;
global using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;
global using BitacoraResiduosESH.Backend.Backend.Application.Services;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Data;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Data.ModelConfigurations;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Repositories;
global using NLog;
global using NLog.Extensions.Logging;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities.OpenIdConnect;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities.Shared;
global using BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;
global using BitacoraResiduosESH.Backend.Backend.Infrastructure.Services;
global using ILogger = NLog.ILogger;