import type { CreateRoleDto, Role, UpdateRoleDto } from '@/types/role';
import { BaseSimpleEntityService } from './simpleEntityService';

/**
 * Service for Role entity operations
 * Extends the base SimpleEntity service with Role-specific typing
 */
class RoleService extends BaseSimpleEntityService<Role, CreateRoleDto, UpdateRoleDto> {
    constructor() {
        super('role'); // Pass the API endpoint base URL
    }

    // Add any Role-specific methods here if needed in the future
    // For now, all standard CRUD operations are inherited from BaseSimpleEntityService
}

// Export a singleton instance
export const roleService = new RoleService();

// Alternative approach using factory utilities:
// import { createSimpleEntityService } from '@/utils/simpleEntityFactory';
// export const roleService = createSimpleEntityService<Role, CreateRoleDto, UpdateRoleDto>('role');