@import "tailwindcss";

/* Configuración del tema */
@theme {
    --color-primary: #3b82f6;
    --color-primary-dark: #1d4ed8;
}

/* Variables CSS para temas */
:root {
    --bg-color: #f9fafb;
    --bg-secondary: #ffffff;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --sidebar-bg: #ffffff;
    --header-bg: #ffffff;
    --card-bg: #ffffff;
    --hover-bg: #f3f4f6;
}

html.dark {
    --bg-color: #111827;
    --bg-secondary: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    --sidebar-bg: #1f2937;
    --header-bg: #1f2937;
    --card-bg: #374151;
    --hover-bg: #374151;
}

/* Estilos base para tema claro y oscuro */
@layer base {
    html {
        @apply antialiased;
    }

    body {
        background-color: var(--bg-color);
        color: var(--text-primary);
        transition: background-color 0.2s, color 0.2s;
    }
}

/* Estilos personalizados para componentes */
@layer components {
    /* Estilos para el scrollbar */
    ::-webkit-scrollbar {
        width: 0.5rem;
    }

    ::-webkit-scrollbar-track {
        background-color: var(--bg-secondary);
    }

    ::-webkit-scrollbar-thumb {
        background-color: var(--text-secondary);
        border-radius: 9999px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background-color: var(--text-primary);
    }

    /* Estilos para selección de texto */
    ::selection {
        background-color: #bfdbfe;
        color: #1e3a8a;
    }

    html.dark ::selection {
        background-color: #1e40af;
        color: #dbeafe;
    }

    /* Estilos para focus visible */
    .focus-visible {
        outline: none;
        box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px var(--bg-color);
    }
}

/* Animaciones personalizadas */
@layer utilities {
    .animate-fade-in {
        animation: fadeIn 0.3s ease-in-out;
    }

    .animate-slide-in {
        animation: slideIn 0.3s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @keyframes slideIn {
        from {
            transform: translateY(-10px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
}
