import React from 'react';

export interface FormDividerProps {
    text?: string;
    className?: string;
}

const FormDivider: React.FC<FormDividerProps> = ({
                                                     text,
                                                     className = ''
                                                 }) => {
    return (
        <div className={`relative ${className}`}>
            <div
                className="absolute inset-0 flex items-center"
                style={{borderColor: 'var(--border-color)'}}
            >
                <div
                    className="w-full border-t"
                    style={{borderColor: 'var(--border-color)'}}
                />
            </div>

            {text && (
                <div className="relative flex justify-center text-sm">
          <span
              className="px-2 bg-white dark:bg-gray-900"
              style={{
                  backgroundColor: 'var(--card-bg)',
                  color: 'var(--text-secondary)'
              }}
          >
            {text}
          </span>
                </div>
            )}
        </div>
    );
};

export default FormDivider; 