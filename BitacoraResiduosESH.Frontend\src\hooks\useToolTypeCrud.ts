import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import {toolTypeService} from '../services/toolTypeService';
import type {CreateToolTypeDto, UpdateToolTypeDto} from '../types/toolType';

// Query keys para TanStack Query
export const toolTypeQueryKeys = {
    all: ['tooltypes'] as const,
    lists: () => [...toolTypeQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...toolTypeQueryKeys.lists(), filters] as const,
    details: () => [...toolTypeQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...toolTypeQueryKeys.details(), id] as const,
};

export const useToolTypeCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todos los tipos de herramientas
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: toolTypeQueryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => toolTypeService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener tipos de herramientas activos
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: toolTypeQueryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => toolTypeService.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener un tipo de herramienta por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: toolTypeQueryKeys.detail(id),
            queryFn: () => toolTypeService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar tipos de herramientas por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: toolTypeQueryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => toolTypeService.searchByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar tipos de herramientas por descripción
    const useSearchByDescription = (
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: toolTypeQueryKeys.list({search: 'description', description, pageNumber, pageSize, includeDeleted}),
            queryFn: () => toolTypeService.searchByDescription(description, pageNumber, pageSize, includeDeleted),
            enabled: !!description.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear un tipo de herramienta
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateToolTypeDto) => toolTypeService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: toolTypeQueryKeys.lists()});
                toast.success('Tipo de herramienta creado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el tipo de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar un tipo de herramienta
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: UpdateToolTypeDto }) =>
                toolTypeService.update(id, data),
            onSuccess: (updatedToolType) => {
                queryClient.invalidateQueries({queryKey: toolTypeQueryKeys.lists()});
                queryClient.setQueryData(toolTypeQueryKeys.detail(updatedToolType.id), updatedToolType);
                toast.success('Tipo de herramienta actualizado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el tipo de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar un tipo de herramienta
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => toolTypeService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: toolTypeQueryKeys.lists()});
                queryClient.removeQueries({queryKey: toolTypeQueryKeys.detail(deletedId)});
                toast.success('Tipo de herramienta eliminado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el tipo de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para activar un tipo de herramienta
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => toolTypeService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: toolTypeQueryKeys.lists()});
                toast.success('Tipo de herramienta activado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el tipo de herramienta: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar un tipo de herramienta
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => toolTypeService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: toolTypeQueryKeys.lists()});
                toast.success('Tipo de herramienta desactivado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el tipo de herramienta: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: toolTypeQueryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByDescription,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,

        // Helpers
        invalidateCache,
    };
}; 