import {useMutation, useQuery, useQueryClient} from '@tanstack/react-query';
import {toast} from 'sonner';
import {userService} from '../services/userService';
import type {AdminChangePasswordDto, ChangePasswordDto, CreateUserDto, UpdateUserDto} from '../types/user';

// Query keys para TanStack Query
export const userQueryKeys = {
    all: ['users'] as const,
    lists: () => [...userQueryKeys.all, 'list'] as const,
    list: (filters: any) => [...userQueryKeys.lists(), filters] as const,
    details: () => [...userQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...userQueryKeys.details(), id] as const,
};

export const useUserCrud = () => {
    const queryClient = useQueryClient();

    // Query para obtener todos los usuarios
    const useGetAll = (
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: userQueryKeys.list({pageNumber, pageSize, includeDeleted}),
            queryFn: () => userService.getAll(pageNumber, pageSize, includeDeleted),
            staleTime: 5 * 60 * 1000, // 5 minutos
            gcTime: 10 * 60 * 1000, // 10 minutos
        });
    };

    // Query para obtener usuarios activos
    const useGetActive = (pageNumber = 1, pageSize = 10) => {
        return useQuery({
            queryKey: userQueryKeys.list({active: true, pageNumber, pageSize}),
            queryFn: () => userService.getActive(pageNumber, pageSize),
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para obtener un usuario por ID
    const useGetById = (id: number, includeDeleted = false) => {
        return useQuery({
            queryKey: userQueryKeys.detail(id),
            queryFn: () => userService.getById(id, includeDeleted),
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
        });
    };

    // Query para buscar usuarios por nombre
    const useSearchByName = (
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: userQueryKeys.list({search: 'name', name, pageNumber, pageSize, includeDeleted}),
            queryFn: () => userService.getByName(name, pageNumber, pageSize, includeDeleted),
            enabled: !!name.trim(),
            staleTime: 2 * 60 * 1000, // 2 minutos para búsquedas
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para buscar usuarios por email
    const useSearchByEmail = (
        email: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: userQueryKeys.list({search: 'email', email, pageNumber, pageSize, includeDeleted}),
            queryFn: () => userService.getByEmail(email, pageNumber, pageSize, includeDeleted),
            enabled: !!email.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Query para búsqueda personalizada
    const useCustomSearch = (
        predicate: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ) => {
        return useQuery({
            queryKey: userQueryKeys.list({search: 'custom', predicate, pageNumber, pageSize, includeDeleted}),
            queryFn: () => userService.customSearch(predicate, pageNumber, pageSize, includeDeleted),
            enabled: !!predicate.trim(),
            staleTime: 2 * 60 * 1000,
            gcTime: 5 * 60 * 1000,
        });
    };

    // Mutation para crear un usuario
    const useCreate = () => {
        return useMutation({
            mutationFn: (data: CreateUserDto) => userService.create(data),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: userQueryKeys.lists()});
                toast.success('Usuario creado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al crear el usuario: ${error.message}`);
            },
        });
    };

    // Mutation para actualizar un usuario
    const useUpdate = () => {
        return useMutation({
            mutationFn: ({id, data}: { id: number; data: UpdateUserDto }) =>
                userService.update(id, data),
            onSuccess: (updatedUser) => {
                queryClient.invalidateQueries({queryKey: userQueryKeys.lists()});
                queryClient.setQueryData(userQueryKeys.detail(updatedUser.id), updatedUser);
                toast.success('Usuario actualizado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al actualizar el usuario: ${error.message}`);
            },
        });
    };

    // Mutation para eliminar un usuario
    const useDelete = () => {
        return useMutation({
            mutationFn: (id: number) => userService.delete(id),
            onSuccess: (_, deletedId) => {
                queryClient.invalidateQueries({queryKey: userQueryKeys.lists()});
                queryClient.removeQueries({queryKey: userQueryKeys.detail(deletedId)});
                toast.success('Usuario eliminado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al eliminar el usuario: ${error.message}`);
            },
        });
    };

    // Mutation para activar un usuario
    const useActivate = () => {
        return useMutation({
            mutationFn: (id: number) => userService.activate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: userQueryKeys.lists()});
                toast.success('Usuario activado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al activar el usuario: ${error.message}`);
            },
        });
    };

    // Mutation para desactivar un usuario
    const useDeactivate = () => {
        return useMutation({
            mutationFn: (id: number) => userService.deactivate(id),
            onSuccess: () => {
                queryClient.invalidateQueries({queryKey: userQueryKeys.lists()});
                toast.success('Usuario desactivado exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al desactivar el usuario: ${error.message}`);
            },
        });
    };

    // Mutation para cambiar contraseña del usuario
    const useChangePassword = () => {
        return useMutation({
            mutationFn: (data: ChangePasswordDto) => userService.changePassword(data),
            onSuccess: () => {
                toast.success('Contraseña cambiada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al cambiar la contraseña: ${error.message}`);
            },
        });
    };

    // Mutation para cambiar contraseña por administrador
    const useAdminChangePassword = () => {
        return useMutation({
            mutationFn: (data: AdminChangePasswordDto) => userService.adminChangePassword(data),
            onSuccess: () => {
                toast.success('Contraseña cambiada exitosamente');
            },
            onError: (error: Error) => {
                toast.error(`Error al cambiar la contraseña: ${error.message}`);
            },
        });
    };

    // Función helper para invalidar cache
    const invalidateCache = () => {
        queryClient.invalidateQueries({queryKey: userQueryKeys.lists()});
    };

    return {
        // Queries
        useGetAll,
        useGetActive,
        useGetById,
        useSearchByName,
        useSearchByEmail,
        useCustomSearch,

        // Mutations
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,
        useChangePassword,
        useAdminChangePassword,

        // Helpers
        invalidateCache,
    };
}; 