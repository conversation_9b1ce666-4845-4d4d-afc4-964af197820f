import React, {useState} from 'react';

export interface FormFieldProps {
    name: string;
    label: string;
    type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
    placeholder?: string;
    required?: boolean;
    disabled?: boolean;
    value: string;
    onChange: (value: string) => void;
    onBlur?: () => void;
    error?: string;
    touched?: boolean;
    className?: string;
    autoComplete?: string;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
}

const FormField: React.FC<FormFieldProps> = ({
                                                 name,
                                                 label,
                                                 type = 'text',
                                                 placeholder,
                                                 required = false,
                                                 disabled = false,
                                                 value,
                                                 onChange,
                                                 onBlur,
                                                 error,
                                                 touched = false,
                                                 className = '',
                                                 autoComplete,
                                                 minLength,
                                                 maxLength,
                                                 pattern
                                             }) => {
    const [isFocused, setIsFocused] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const handleFocus = () => {
        setIsFocused(true);
    };

    const handleBlur = () => {
        setIsFocused(false);
        onBlur?.();
    };

    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const inputType = type === 'password' && showPassword ? 'text' : type;
    const hasError = touched && error;
    const isActive = isFocused || value.length > 0;

    return (
        <div className={`relative ${className}`}>
            {/* Label */}
            <label
                htmlFor={name}
                className={`
           left-3 transition-all duration-200 pointer-events-none
          ${isActive
                    ? 'top-2 text-xs'
                    : 'top-4 text-sm'
                }
          ${hasError
                    ? 'text-red-600 dark:text-red-400'
                    : isActive
                        ? 'text-blue-600 dark:text-blue-400'
                        : 'text-black dark:text-gray-400'
                }
        `}
            >
                {label}
                {required && <span className="text-red-500 ml-1">*</span>}
            </label>

            {/* Input */}
            <div className="relative">
                <input
                    id={name}
                    name={name}
                    type={inputType}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    placeholder={isActive ? placeholder : ''}
                    disabled={disabled}
                    autoComplete={autoComplete}
                    minLength={minLength}
                    maxLength={maxLength}
                    pattern={pattern}
                    className={`
            w-full px-3 pt-6 pb-2 text-sm border rounded-lg transition-all duration-200
            focus:outline-none focus:ring-2 focus:ring-offset-0
            ${hasError
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-200 dark:focus:ring-red-800'
                        : isActive
                            ? 'border-blue-300 focus:border-blue-500 focus:ring-blue-200 dark:focus:ring-blue-800'
                            : 'border-gray-300 dark:border-gray-600 focus:border-gray-400 focus:ring-gray-200 dark:focus:ring-gray-800'
                    }
            ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed' : 'bg-white dark:bg-gray-900'}
            ${hasError ? 'text-red-900 dark:text-red-100' : 'text-black dark:text-gray-100'}
          `}
                    style={{
                        backgroundColor: disabled ? '#f3f4f6' : '#ffffff',
                        borderColor: hasError ? '#fca5a5' : isActive ? '#93c5fd' : '#d1d5db',
                        color: '#111827'
                    }}
                />

                {/* Password toggle button */}
                {type === 'password' && (
                    <button
                        type="button"
                        onClick={togglePasswordVisibility}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                    >
                        {showPassword ? (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                      d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                            </svg>
                        ) : (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                        )}
                    </button>
                )}
            </div>

            {/* Error message */}
            {hasError && (
                <p className="mt-1 text-xs text-red-600 dark:text-red-400 flex items-center">
                    <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"/>
                    </svg>
                    {error}
                </p>
            )}
        </div>
    );
};

export default FormField; 