import React from 'react';
import {useForm} from '@tanstack/react-form';
import Modal from '../Modal';
import <PERSON>Field from './FormField';
import FormButton from './FormButton';

export interface FormModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: any) => Promise<void>;
    data?: any;
    mode: 'create' | 'edit';
    loading?: boolean;
    title?: string;
    renderFields?: (data?: any) => React.ReactNode;
}

const FormModal: React.FC<FormModalProps> = ({
                                                 isOpen,
                                                 onClose,
                                                 onSubmit,
                                                 data,
                                                 mode,
                                                 loading = false,
                                                 title,
                                                 renderFields,
                                             }) => {
    const form = useForm({
        defaultValues: {
            name: data?.name || '',
            description: data?.description || ''
        },
        onSubmit: async ({value}) => {
            try {
                await onSubmit(value);
                onClose();
            } catch (error) {
                console.error('Error al guardar:', error);
            }
        },
    });

    // Validación manual
    const validateName = (name: string): string | undefined => {
        if (!name.trim()) return 'El nombre es requerido';
        if (name.trim().length < 2) return 'El nombre debe tener al menos 2 caracteres';
        if (name.trim().length > 200) return 'El nombre no puede exceder 200 caracteres';
        return undefined;
    };

    const validateDescription = (description: string): string | undefined => {
        if (description && description.trim().length > 1000) {
            return 'La descripción no puede exceder 1000 caracteres';
        }
        return undefined;
    };

    // Si se proporcionan campos personalizados, usarlos
    if (renderFields) {
        return (
            <Modal
                isOpen={isOpen}
                onClose={onClose}
                title={title || (mode === 'create' ? 'Crear Nuevo Elemento' : 'Editar Elemento')}
                size="md"
                closeOnOverlayClick={!loading}
                closeOnEscape={!loading}
            >
                <form onSubmit={(e) => {
                    e.preventDefault();
                    const formData = new FormData(e.currentTarget);
                    const data: any = {};

                    // Recopilar todos los campos del formulario
                    for (const [key, value] of formData.entries()) {
                        data[key] = value;
                    }

                    // Si estamos en modo de edición y hay datos, incluir el ID
                    if (mode === 'edit' && data && data.id) {
                        // El ID ya está incluido en los datos del formulario
                    }

                    onSubmit(data);
                }}>
                    {renderFields(data)}

                    {/* Botones */}
                    <div className="flex justify-end space-x-3 mt-6">
                        <button
                            type="button"
                            onClick={onClose}
                            disabled={loading}
                            className="px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                            style={{
                                borderColor: 'var(--border-color)',
                                color: 'var(--text-primary)',
                            }}
                        >
                            Cancelar
                        </button>
                        <FormButton
                            type="submit"
                            variant="primary"
                            loading={loading}
                        >
                            {loading ? 'Guardando...' : mode === 'create' ? 'Crear' : 'Guardar Cambios'}
                        </FormButton>
                    </div>
                </form>
            </Modal>
        );
    }

    // Campos por defecto
    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={title || (mode === 'create' ? 'Crear Nuevo Elemento' : 'Editar Elemento')}
            size="md"
            closeOnOverlayClick={!loading}
            closeOnEscape={!loading}
        >
            <form.Field
                name="name"
                validators={{
                    onChange: ({value}) => validateName(value),
                    onBlur: ({value}) => validateName(value),
                }}
            >
                {(field) => (
                    <FormField
                        name="name"
                        label="Nombre"
                        type="text"
                        placeholder="Ingrese el nombre del elemento"
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value)}
                        onBlur={() => field.handleBlur()}
                        error={field.state.meta.errors?.[0]}
                        touched={!!field.state.meta.errors?.length}
                        required
                        maxLength={200}
                    />
                )}
            </form.Field>

            <form.Field
                name="description"
                validators={{
                    onChange: ({value}) => validateDescription(value),
                    onBlur: ({value}) => validateDescription(value),
                }}
            >
                {(field) => (
                    <div className="relative">
                        <label
                            htmlFor="description"
                            className="block text-sm font-medium mb-2"
                            style={{color: 'var(--text-primary)'}}
                        >
                            Descripción
                        </label>
                        <textarea
                            id="description"
                            name="description"
                            value={field.state.value}
                            onChange={(e) => field.handleChange(e.target.value)}
                            onBlur={() => field.handleBlur()}
                            placeholder="Ingrese una descripción (opcional)"
                            rows={3}
                            maxLength={1000}
                            className={`
                w-full px-3 py-2 text-sm border rounded-lg transition-all duration-200
                focus:outline-none focus:ring-2 focus:ring-offset-0
                ${field.state.meta.errors?.length
                                ? 'border-red-300 focus:border-red-500 focus:ring-red-200 dark:focus:ring-red-800'
                                : 'border-gray-300 dark:border-gray-600 focus:border-gray-400 focus:ring-gray-200 dark:focus:ring-gray-800'
                            }
                bg-white dark:bg-gray-900
                ${field.state.meta.errors?.length ? 'text-red-900 dark:text-red-100' : 'text-black dark:text-gray-100'}
              `}
                            style={{
                                backgroundColor: '#ffffff',
                                borderColor: field.state.meta.errors?.length ? '#fca5a5' : '#d1d5db',
                                color: '#111827'
                            }}
                        />
                        {field.state.meta.errors?.length && (
                            <p className="mt-1 text-xs text-red-600 dark:text-red-400 flex items-center">
                                <svg className="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd"
                                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                          clipRule="evenodd"/>
                                </svg>
                                {field.state.meta.errors[0]}
                            </p>
                        )}
                    </div>
                )}
            </form.Field>

            {/* Botones */}
            <div className="flex justify-end space-x-3 mt-6">
                <button
                    type="button"
                    onClick={onClose}
                    disabled={loading}
                    className="px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-700"
                    style={{
                        borderColor: 'var(--border-color)',
                        color: 'var(--text-primary)',
                    }}
                >
                    Cancelar
                </button>
                <FormButton
                    type="button"
                    variant="primary"
                    loading={loading}
                    onClick={() => form.handleSubmit()}
                >
                    {loading ? 'Guardando...' : mode === 'create' ? 'Crear' : 'Guardar Cambios'}
                </FormButton>
            </div>
        </Modal>
    );
};

export default FormModal; 