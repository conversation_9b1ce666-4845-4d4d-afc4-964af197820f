import type {PagedResponse} from '../config/api';
import type {BaseEntity} from '../components/table/BaseEntityTable';

// Tipos para la entidad User basados en los DTOs del backend
// Extendiendo BaseEntity para compatibilidad con BaseEntityTable

export interface User extends BaseEntity {
    email: string;
    employeeNumber: string;
    roleId: number;
    roleName: string;
}

export interface CreateUserDto {
    name: string;
    email: string;
    employeeNumber: string;
    roleId: number;
    password: string;
    active?: boolean;
}

export interface UpdateUserDto {
    id: number;
    name: string;
    email: string;
    employeeNumber: string;
    roleId: number;
    active?: boolean;
}

// Nuevos DTOs para cambio de contraseña
export interface ChangePasswordDto {
    userId: number;
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
}

export interface AdminChangePasswordDto {
    userId: number;
    newPassword: string;
    confirmNewPassword: string;
}

export interface UserFilterDto {
    name?: string;
    email?: string;
    employeeNumber?: string;
    roleId?: number;
    active?: boolean;
    includeDeleted?: boolean;
    pageNumber: number;
    pageSize: number;
}

// Tipo para las operaciones CRUD
export interface UserCrudOperations {
    create: (data: CreateUserDto) => Promise<User>;
    update: (id: number, data: UpdateUserDto) => Promise<User>;
    delete: (id: number) => Promise<boolean>;
    getById: (id: number, includeDeleted?: boolean) => Promise<User>;
    getAll: (pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<User>>;
    getActive: (pageNumber?: number, pageSize?: number) => Promise<PagedResponse<User>>;
    getByName: (name: string, pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<User>>;
    getByEmail: (email: string, pageNumber?: number, pageSize?: number, includeDeleted?: boolean) => Promise<PagedResponse<User>>;
    getByEmailExact: (email: string, includeDeleted?: boolean) => Promise<User | null>;
    getByEmployeeNumberExact: (employeeNumber: string, includeDeleted?: boolean) => Promise<User | null>;
    existsByEmail: (email: string, excludeId?: number, includeDeleted?: boolean) => Promise<boolean>;
    existsByEmployeeNumber: (employeeNumber: string, excludeId?: number, includeDeleted?: boolean) => Promise<boolean>;
    exists: (id: number, includeDeleted?: boolean) => Promise<boolean>;
    activate: (id: number) => Promise<boolean>;
    deactivate: (id: number) => Promise<boolean>;

    // Nuevos métodos para cambio de contraseña
    changePassword: (data: ChangePasswordDto) => Promise<boolean>;
    adminChangePassword: (data: AdminChangePasswordDto) => Promise<boolean>;
} 