import React from "react";

const Footer: React.FC = () => {
    return (
        <footer
            className="py-6 px-4 border-t transition-all duration-200"
            style={{
                backgroundColor: 'var(--bg-secondary)',
                borderColor: 'var(--border-color)'
            }}
        >
            <div className="max-w-7xl mx-auto">
                <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    {/* Logo y copyright */}
                    <div className="flex items-center space-x-4">
                        <div
                            className="w-8 h-8 rounded-lg flex items-center justify-center p-1 shadow-sm border"
                            style={{
                                backgroundColor: 'var(--bg-secondary)',
                                borderColor: 'var(--border-color)'
                            }}
                        >
                            <img
                                src="/molex-logo-1.png"
                                alt="Molex Nogales"
                                className="w-full h-full object-contain"
                            />
                        </div>
                        <div>
                            <p
                                className="text-sm font-medium"
                                style={{color: 'var(--text-primary)'}}
                            >
                                Molex Nogales
                            </p>
                            <p
                                className="text-xs"
                                style={{color: 'var(--text-secondary)'}}
                            >
                                © {new Date().getFullYear()} Todos los derechos reservados
                            </p>
                        </div>
                    </div>

                    {/* Enlaces */}
                    <div className="flex items-center space-x-6">
                        <a
                            href="#"
                            className="text-sm transition-colors duration-200"
                            style={{color: 'var(--text-secondary)'}}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.color = 'var(--text-primary)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'var(--text-secondary)';
                            }}
                        >
                            Política de Privacidad
                        </a>
                        <a
                            href="#"
                            className="text-sm transition-colors duration-200"
                            style={{color: 'var(--text-secondary)'}}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.color = 'var(--text-primary)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'var(--text-secondary)';
                            }}
                        >
                            Términos de Servicio
                        </a>
                        <a
                            href="#"
                            className="text-sm transition-colors duration-200"
                            style={{color: 'var(--text-secondary)'}}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.color = 'var(--text-primary)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'var(--text-secondary)';
                            }}
                        >
                            Soporte
                        </a>
                    </div>

                    {/* Información de versión */}
                    <div className="text-center md:text-right">
                        <p
                            className="text-xs"
                            style={{color: 'var(--text-secondary)'}}
                        >
                            Versión 1.0.0
                        </p>
                        <p
                            className="text-xs"
                            style={{color: 'var(--text-secondary)'}}
                        >
                            Sistema de Gestión
                        </p>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
