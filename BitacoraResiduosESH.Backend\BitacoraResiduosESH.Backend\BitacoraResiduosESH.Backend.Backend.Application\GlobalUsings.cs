// Global using directives

global using System.ComponentModel.DataAnnotations;
global using System.Linq.Expressions;
global using BitacoraResiduosESH.Backend.Backend.Application.Common;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.SimpleEntity;
global using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;
global using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities;
global using NLog;
global using BitacoraResiduosESH.Backend.Backend.Application.Constants;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.Area;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.BitacoraEntry;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.ContainerType;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.Role;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.User;
global using BitacoraResiduosESH.Backend.Backend.Application.DTOs.WasteType;
global using BitacoraResiduosESH.Backend.Backend.Domain.Entities.Shared;
global using BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;
global using WasteType = BitacoraResiduosESH.Backend.Backend.Domain.Entities.WasteType;