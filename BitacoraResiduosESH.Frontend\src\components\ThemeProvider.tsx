import React, {useEffect} from 'react';
import {useThemeStore} from '../stores/themeStore';

interface ThemeProviderProps {
    children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({children}) => {
    const {theme} = useThemeStore();

    useEffect(() => {
        // Aplicar el tema al documento cuando cambie
        const root = document.documentElement;

        if (theme === 'dark') {
            root.classList.add('dark');
        } else {
            root.classList.remove('dark');
        }

        // También actualizar el meta theme-color para móviles
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.setAttribute(
                'content',
                theme === 'dark' ? '#1f2937' : '#ffffff'
            );
        }
    }, [theme]);

    return <>{children}</>;
};

export default ThemeProvider; 