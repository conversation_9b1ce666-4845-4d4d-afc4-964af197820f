using System.Security.Claims;

namespace BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;

/// <summary>
///     Servicio para manejo de tokens JWT
/// </summary>
public interface IJwtService
{
    /// <summary>
    ///     Genera un token JWT para un usuario
    /// </summary>
    /// <param name="userId">ID del usuario</param>
    /// <param name="email">Email del usuario</param>
    /// <param name="role">Rol del usuario</param>
    /// <returns>Token JWT</returns>
    string GenerateToken(int userId, string name, string email, string role);

    /// <summary>
    ///     Valida un token JWT
    /// </summary>
    /// <param name="token">Token JWT a validar</param>
    /// <returns>Claims del token si es válido, null en caso contrario</returns>
    ClaimsPrincipal? ValidateToken(string token);

    /// <summary>
    ///     Extrae el ID del usuario del token JWT
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>ID del usuario si el token es válido, null en caso contrario</returns>
    int? GetUserIdFromToken(string token);

    /// <summary>
    ///     Extrae el email del usuario del token JWT
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>Email del usuario si el token es válido, null en caso contrario</returns>
    string? GetUserEmailFromToken(string token);

    /// <summary>
    ///     Extrae el rol del usuario del token JWT
    /// </summary>
    /// <param name="token">Token JWT</param>
    /// <returns>Rol del usuario si el token es válido, null en caso contrario</returns>
    string? GetUserRoleFromToken(string token);
}