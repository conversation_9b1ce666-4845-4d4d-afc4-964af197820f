namespace BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;

/// <summary>
///     Servicio para manejo seguro de contraseñas con hash y salt
/// </summary>
public interface IPasswordService
{
    /// <summary>
    ///     Genera un hash seguro de la contraseña con salt
    /// </summary>
    /// <param name="password">Contraseña en texto plano</param>
    /// <returns>Hash de la contraseña con salt incluido</returns>
    string HashPassword(string password);

    /// <summary>
    ///     Verifica si una contraseña coincide con su hash
    /// </summary>
    /// <param name="password">Contraseña en texto plano</param>
    /// <param name="hashedPassword">Hash de la contraseña</param>
    /// <returns>True si la contraseña coincide, False en caso contrario</returns>
    bool VerifyPassword(string password, string hashedPassword);

    /// <summary>
    ///     Genera un salt aleatorio
    /// </summary>
    /// <returns>Salt en formato string</returns>
    string GenerateSalt();
}