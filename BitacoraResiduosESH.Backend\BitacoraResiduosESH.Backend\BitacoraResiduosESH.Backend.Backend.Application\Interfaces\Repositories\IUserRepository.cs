using BitacoraResiduosESH.Backend.Backend.Application.Common;

namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;

public interface IUserRepository : IGenericRepository<User>
{
    Task<PagedResponse<User>> GetByNameAsync(string name, PaginationFilter filter, bool includeDeleted = false);
    Task<PagedResponse<User>> GetByEmailAsync(string email, PaginationFilter filter, bool includeDeleted = false);
    Task<User?> GetByEmailExactAsync(string email, bool includeDeleted = false);
    Task<User?> GetByEmployeeNumberExactAsync(string employeeNumber, bool includeDeleted = false);
    Task<bool> ExistsByEmailAsync(string email, int? excludeId = null, bool includeDeleted = false);
    Task<bool> ExistsByEmployeeNumberAsync(string employeeNumber, int? excludeId = null, bool includeDeleted = false);
}