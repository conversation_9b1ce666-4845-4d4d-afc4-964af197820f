using BitacoraResiduosESH.Backend.Backend.Application.Common;
using BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;

namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;

public interface IGenericService<TEntity, TDto, TFilterDto, TCreateDto, TUpdateDto>
    where TEntity : BaseEntity
    where TDto : BaseEntityDto
    where TFilterDto : BaseEntityFilterDto
    where TCreateDto : CreateBaseEntityDto
    where TUpdateDto : UpdateBaseEntityDto
{
    #region Operaciones de validación

    Task<bool> ExistsAsync(int id, bool includeDeleted = false);

    #endregion

    #region Operaciones de lectura

    Task<TDto?> GetByIdAsync(int id, bool includeDeleted = false);
    Task<PagedResponse<TDto>> GetAllAsync(PaginationFilter filter);
    Task<PagedResponse<TDto>> GetPagedAsync(TFilterDto filter);
    Task<PagedResponse<TDto>> GetActiveAsync(PaginationFilter filter);
    Task<int> CountAsync(bool includeDeleted = false);

    #endregion

    #region Operaciones de escritura

    Task<TDto> CreateAsync(TCreateDto dto, string createdBy);
    Task<IEnumerable<TDto>> CreateRangeAsync(IEnumerable<TCreateDto> dtos, string createdBy);
    Task<TDto> UpdateAsync(int id, TUpdateDto dto, string updatedBy);
    Task<IEnumerable<TDto>> UpdateRangeAsync(IEnumerable<TUpdateDto> dtos, string updatedBy);

    #endregion

    #region Operaciones de eliminación

    Task<bool> DeleteAsync(int id, string deletedBy);
    Task<bool> DeleteAsync(TEntity entity, string deletedBy);
    Task<bool> DeleteRangeAsync(IEnumerable<int> ids, string deletedBy);
    Task<bool> DeleteRangeAsync(IEnumerable<TEntity> entities, string deletedBy);
    Task<bool> HardDeleteAsync(int id);
    Task<bool> HardDeleteAsync(TEntity entity);
    Task<bool> HardDeleteRangeAsync(IEnumerable<int> ids);
    Task<bool> HardDeleteRangeAsync(IEnumerable<TEntity> entities);

    #endregion

    #region Operaciones de activación/desactivación

    Task<bool> ActivateAsync(int id, string updatedBy);
    Task<bool> DeactivateAsync(int id, string updatedBy);
    Task<bool> ActivateAsync(TEntity entity, string updatedBy);
    Task<bool> DeactivateAsync(TEntity entity, string updatedBy);

    #endregion

    #region Operaciones de búsqueda

    Task<PagedResponse<TDto>> FindAsync(Expression<Func<TEntity, bool>> predicate, PaginationFilter filter,
        bool includeDeleted = false);

    Task<TDto?> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false);
    Task<TDto> FirstAsync(Expression<Func<TEntity, bool>> predicate, bool includeDeleted = false);

    #endregion
}