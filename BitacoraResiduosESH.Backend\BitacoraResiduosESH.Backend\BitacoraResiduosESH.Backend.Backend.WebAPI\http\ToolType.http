### Variables de entorno
@baseUrl = https://localhost:7001
@token = {{login.response.body.token}}

### Login para obtener token
# @name login
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Admin123!"
}

###

### ========================================
### OPERACIONES DE LECTURA
### ========================================

### Obtener todos los tipos de herramientas
GET {{baseUrl}}/api/tooltype?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Obtener tipos de herramientas paginados con filtros
GET {{baseUrl}}/api/tooltype/paged?pageNumber=1&pageSize=10&name=&description=&active=true&includeDeleted=false
Authorization: Bearer {{token}}

###

### Obtener solo tipos de herramientas activos
GET {{baseUrl}}/api/tooltype/active?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Obtener tipo de herramienta por ID
GET {{baseUrl}}/api/tooltype/1
Authorization: Bearer {{token}}

###

### Buscar tipos de herramientas por nombre
GET {{baseUrl}}/api/tooltype/search/name?name=manual&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Buscar tipos de herramientas por descripción
GET {{baseUrl}}/api/tooltype/search/description?description=herramienta&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

###

### Verificar si existe un tipo de herramienta
GET {{baseUrl}}/api/tooltype/1/exists
Authorization: Bearer {{token}}

###

### ========================================
### OPERACIONES DE ESCRITURA
### ========================================

### Crear nuevo tipo de herramienta
POST {{baseUrl}}/api/tooltype
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Herramienta Manual",
  "description": "Herramientas que se operan manualmente sin necesidad de energía eléctrica"
}

###

### Crear otro tipo de herramienta
POST {{baseUrl}}/api/tooltype
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Herramienta Eléctrica",
  "description": "Herramientas que requieren energía eléctrica para funcionar"
}

###

### Crear tipo de herramienta sin descripción
POST {{baseUrl}}/api/tooltype
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Herramienta Neumática"
}

###

### Actualizar tipo de herramienta
PUT {{baseUrl}}/api/tooltype/1
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Herramienta Manual Actualizada",
  "description": "Descripción actualizada para herramientas manuales"
}

###

### ========================================
### OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN
### ========================================

### Desactivar tipo de herramienta
PATCH {{baseUrl}}/api/tooltype/1/deactivate
Authorization: Bearer {{token}}

###

### Activar tipo de herramienta
PATCH {{baseUrl}}/api/tooltype/1/activate
Authorization: Bearer {{token}}

###

### ========================================
### OPERACIONES DE ELIMINACIÓN
### ========================================

### Eliminar tipo de herramienta (soft delete)
DELETE {{baseUrl}}/api/tooltype/1
Authorization: Bearer {{token}}

###

### Eliminar tipo de herramienta permanentemente
DELETE {{baseUrl}}/api/tooltype/1/permanent
Authorization: Bearer {{token}}

###

### ========================================
### CASOS DE ERROR
### ========================================

### Intentar crear tipo de herramienta sin nombre
POST {{baseUrl}}/api/tooltype
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "description": "Solo descripción sin nombre"
}

###

### Intentar crear tipo de herramienta con nombre duplicado
POST {{baseUrl}}/api/tooltype
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Herramienta Manual",
  "description": "Intento de duplicado"
}

###

### Intentar actualizar tipo de herramienta inexistente
PUT {{baseUrl}}/api/tooltype/999
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "Tipo Inexistente",
  "description": "Este tipo no existe"
}

###

### Intentar acceder sin token
GET {{baseUrl}}/api/tooltype

###

### Intentar acceder con token inválido
GET {{baseUrl}}/api/tooltype
Authorization: Bearer token_invalido 