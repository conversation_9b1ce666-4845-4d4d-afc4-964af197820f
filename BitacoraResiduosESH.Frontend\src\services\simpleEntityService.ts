import type { SimpleEntity, CreateSimpleEntityDto, UpdateSimpleEntityDto } from '@/types/simpleEntity';
import type { PagedResponse } from '../config/api';
import { buildApiUrl, getAuthHeadersFromStore, handleApiError } from '../config/api';
import type { SimpleEntityService } from '../hooks/useSimpleEntityCrud';

/**
 * Generic service class for SimpleEntity-based entities
 * This class provides all the standard CRUD operations for entities that extend SimpleEntity
 * 
 * @template T - The entity type that extends SimpleEntity
 * @template TCreate - The DTO type for creating entities
 * @template TUpdate - The DTO type for updating entities
 */
export class BaseSimpleEntityService<
    T extends SimpleEntity,
    TCreate extends CreateSimpleEntityDto = CreateSimpleEntityDto,
    TUpdate extends UpdateSimpleEntityDto = UpdateSimpleEntityDto
> implements SimpleEntityService<T, TCreate, TUpdate> {
    protected baseUrl: string;

    /**
     * Constructor for the base service
     * @param baseUrl - The API endpoint base URL for this entity (e.g., 'roles', 'areas')
     */
    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
    }

    // Create a new entity
    async create(data: TCreate): Promise<T> {
        return this.request<T>('', {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    // Update an existing entity
    async update(id: number, data: TUpdate): Promise<T> {
        return this.request<T>(`/${id}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    // Delete an entity (soft delete)
    async delete(id: number): Promise<boolean> {
        return this.request<boolean>(`/${id}`, {
            method: 'DELETE',
        });
    }

    // Get an entity by ID
    async getById(id: number, includeDeleted = false): Promise<T> {
        return this.request<T>(`/${id}?includeDeleted=${includeDeleted}`);
    }

    // Get all entities with pagination
    async getAll(
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<T>> {
        return this.request<PagedResponse<T>>(
            `?pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Get active entities with pagination
    async getActive(pageNumber = 1, pageSize = 10): Promise<PagedResponse<T>> {
        return this.request<PagedResponse<T>>(
            `/active?pageNumber=${pageNumber}&pageSize=${pageSize}`
        );
    }

    // Search entities by name
    async searchByName(
        name: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<T>> {
        return this.request<PagedResponse<T>>(
            `/search/name?name=${encodeURIComponent(name)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Search entities by description
    async searchByDescription(
        description: string,
        pageNumber = 1,
        pageSize = 10,
        includeDeleted = false
    ): Promise<PagedResponse<T>> {
        return this.request<PagedResponse<T>>(
            `/search/description?description=${encodeURIComponent(description)}&pageNumber=${pageNumber}&pageSize=${pageSize}&includeDeleted=${includeDeleted}`
        );
    }

    // Check if an entity exists
    async exists(id: number, includeDeleted = false): Promise<boolean> {
        return this.request<boolean>(`/${id}/exists?includeDeleted=${includeDeleted}`);
    }

    // Activate an entity
    async activate(id: number): Promise<boolean> {
        return this.request<boolean>(`/${id}/activate`, {
            method: 'PATCH',
        });
    }

    // Deactivate an entity
    async deactivate(id: number): Promise<boolean> {
        return this.request<boolean>(`/${id}/deactivate`, {
            method: 'PATCH',
        });
    }

    // Get entities with advanced filtering
    async getPaged(filter: {
        pageNumber: number;
        pageSize: number;
        name?: string;
        description?: string;
        active?: boolean;
        includeDeleted?: boolean;
    }): Promise<PagedResponse<T>> {
        const params = new URLSearchParams({
            pageNumber: filter.pageNumber.toString(),
            pageSize: filter.pageSize.toString(),
            includeDeleted: (filter.includeDeleted || false).toString(),
        });

        if (filter.name) params.append('name', filter.name);
        if (filter.description) params.append('description', filter.description);
        if (filter.active !== undefined) params.append('active', filter.active.toString());

        return this.request<PagedResponse<T>>(`/paged?${params.toString()}`);
    }

    // Protected method for making HTTP requests
    protected async request<TResponse>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<TResponse> {
        const url = buildApiUrl(`${this.baseUrl}${endpoint}`);

        const config: RequestInit = {
            headers: getAuthHeadersFromStore(),
            ...options,
        };

        try {
            const response = await fetch(url, config);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            throw new Error(handleApiError(error));
        }
    }
}

/**
 * Factory function to create a simple entity service
 * This is a convenience function for creating services for basic SimpleEntity types
 * 
 * @param baseUrl - The API endpoint base URL for this entity
 * @returns A new instance of BaseSimpleEntityService
 */
export const createSimpleEntityService = <T extends SimpleEntity>(
    baseUrl: string
): BaseSimpleEntityService<T> => {
    return new BaseSimpleEntityService<T>(baseUrl);
};

/**
 * Type helper for creating typed service instances
 */
export type SimpleEntityServiceInstance<T extends SimpleEntity> = BaseSimpleEntityService<T>;