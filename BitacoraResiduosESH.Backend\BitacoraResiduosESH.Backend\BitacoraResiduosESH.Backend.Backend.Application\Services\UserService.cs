using BitacoraResiduosESH.Backend.Backend.Application.Common;
using BitacoraResiduosESH.Backend.Backend.Application.Constants;
using BitacoraResiduosESH.Backend.Backend.Application.DTOs.User;
using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Repositories;
using BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;

namespace BitacoraResiduosESH.Backend.Backend.Application.Services;

public class UserService(IUserRepository repository, IRoleRepository roleRepository, IPasswordService passwordService)
    : GenericService<User, UserDto, UserFilterDto, CreateUserDto, UpdateUserDto>(repository), IUserService
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public async Task<PagedResponse<UserDto>> GetByNameAsync(string name, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Obteniendo usuarios por nombre paginados - Nombre: {Name}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            name, filter.PageNumber, filter.PageSize, includeDeleted);

        var pagedResult = await repository.GetByNameAsync(name, filter, includeDeleted);
        var result = new PagedResponse<UserDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "Usuarios por nombre obtenidos paginados: {Count} elementos de {TotalRecords} totales para '{Name}'",
            result.Data.Count, result.TotalRecords, name);

        return result;
    }

    public async Task<PagedResponse<UserDto>> GetByEmailAsync(string email, PaginationFilter filter,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Obteniendo usuarios por email paginados - Email: {Email}, Página: {PageNumber}, Tamaño: {PageSize}, IncludeDeleted: {IncludeDeleted}",
            email, filter.PageNumber, filter.PageSize, includeDeleted);

        var pagedResult = await repository.GetByEmailAsync(email, filter, includeDeleted);
        var result = new PagedResponse<UserDto>
        {
            PageNumber = pagedResult.PageNumber,
            PageSize = pagedResult.PageSize,
            TotalRecords = pagedResult.TotalRecords,
            Data = pagedResult.Data.Select(MapToDto).ToList()
        };

        Logger.Debug(
            "Usuarios por email obtenidos paginados: {Count} elementos de {TotalRecords} totales para '{Email}'",
            result.Data.Count, result.TotalRecords, email);

        return result;
    }

    public async Task<UserDto?> GetByEmailExactAsync(string email, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo usuario por email exacto: {Email}, IncludeDeleted: {IncludeDeleted}", email,
            includeDeleted);

        var entity = await repository.GetByEmailExactAsync(email, includeDeleted);
        var result = entity != null ? MapToDto(entity) : null;

        Logger.Debug("Usuario por email exacto obtenido: {Found} para '{Email}'", result != null, email);

        return result;
    }

    public async Task<UserDto?> GetByEmployeeNumberExactAsync(string employeeNumber, bool includeDeleted = false)
    {
        Logger.Debug(
            "Obteniendo usuario por numero de empleado exacto: {EmployeeNumber}, IncludeDeleted: {IncludeDeleted}",
            employeeNumber, includeDeleted);

        var entity = await repository.GetByEmployeeNumberExactAsync(employeeNumber, includeDeleted);
        var result = entity != null ? MapToDto(entity) : null;

        Logger.Debug("Usuario por numero de empleado exacto obtenido: {Found} para '{EmployeeNumber}'", result != null,
            employeeNumber);

        return result;
    }

    public async Task<bool> ExistsByEmailAsync(string email, int? excludeId = null, bool includeDeleted = false)
    {
        Logger.Debug(
            "Verificando existencia de usuario por email: {Email}, ExcludeId: {ExcludeId}, IncludeDeleted: {IncludeDeleted}",
            email, excludeId, includeDeleted);

        var result = await repository.ExistsByEmailAsync(email, excludeId, includeDeleted);

        Logger.Debug("Verificación de existencia por email completada para '{Email}': {Exists}", email, result);

        return result;
    }

    public async Task<bool> ExistsByEmployeeNumberAsync(string employeeNumber, int? excludeId = null,
        bool includeDeleted = false)
    {
        Logger.Debug(
            "Verificando existencia de usuario por numero de empleado: {EmployeeNumber}, ExcludeId: {ExcludeId}, IncludeDeleted: {IncludeDeleted}",
            employeeNumber, excludeId, includeDeleted);

        var result = await repository.ExistsByEmployeeNumberAsync(employeeNumber, excludeId, includeDeleted);

        Logger.Debug("Verificación de existencia por numero de empleado completada para '{EmployeeNumber}': {Exists}",
            employeeNumber, result);

        return result;
    }

    public override async Task<UserDto> CreateAsync(CreateUserDto dto, string createdBy)
    {
        Logger.Debug("Creando nuevo usuario: {@Dto}, CreatedBy: {User}", dto, createdBy);

        // Validar que el email no exista
        if (!string.IsNullOrWhiteSpace(dto.Email) && await ExistsByEmailAsync(dto.Email))
        {
            Logger.Warn("Intento de crear usuario con email duplicado: {Email}", dto.Email);
            throw new InvalidOperationException($"Ya existe un usuario con el email '{dto.Email}'");
        }

        // Validar que el numero de empleado no exista
        if (!string.IsNullOrWhiteSpace(dto.EmployeeNumber) && await ExistsByEmployeeNumberAsync(dto.EmployeeNumber))
        {
            Logger.Warn("Intento de crear usuario con numero de empleado duplicado: {EmployeeNumber}",
                dto.EmployeeNumber);
            throw new InvalidOperationException(
                $"Ya existe un usuario con el numero de empleado '{dto.EmployeeNumber}'");
        }

        // Validar que el rol exista
        var role = await roleRepository.GetByIdAsync(dto.RoleId);
        if (role == null)
        {
            Logger.Warn("Intento de crear usuario con rol inexistente: {RoleId}", dto.RoleId);
            throw new InvalidOperationException($"No se encontró el rol con ID {dto.RoleId}");
        }

        var result = await base.CreateAsync(dto, createdBy);

        Logger.Info("Usuario creado exitosamente con ID: {Id} por usuario: {User}", result.Id, createdBy);

        return result;
    }

    public override async Task<UserDto> UpdateAsync(int id, UpdateUserDto dto, string updatedBy)
    {
        Logger.Debug("Actualizando usuario ID: {Id}, DTO: {@Dto}, UpdatedBy: {User}", id, dto, updatedBy);

        // Obtener la entidad existente del repositorio
        var existingEntity = await repository.GetByIdAsync(id);
        if (existingEntity == null)
        {
            Logger.Warn("Intento de actualizar usuario inexistente con ID: {Id}", id);
            throw new InvalidOperationException($"No se encontró el usuario con ID {id}");
        }

        // Validar que el email no exista en otro usuario
        if (!string.IsNullOrWhiteSpace(dto.Email) && await ExistsByEmailAsync(dto.Email, id))
        {
            Logger.Warn("Intento de actualizar usuario con email duplicado: {Email}, ID: {Id}", dto.Email, id);
            throw new InvalidOperationException($"Ya existe otro usuario con el email '{dto.Email}'");
        }

        // Validar que el numero de empleado no exista
        if (!string.IsNullOrWhiteSpace(dto.EmployeeNumber) && await ExistsByEmployeeNumberAsync(dto.EmployeeNumber, id))
        {
            Logger.Warn("Intento de actualizar usuario con numero de empleado duplicado: {EmployeeNumber}",
                dto.EmployeeNumber);
            throw new InvalidOperationException(
                $"Ya existe un usuario con el numero de empleado '{dto.EmployeeNumber}'");
        }

        // Validar que el rol exista
        var role = await roleRepository.GetByIdAsync(dto.RoleId);
        if (role == null)
        {
            Logger.Warn("Intento de actualizar usuario con rol inexistente: {RoleId}", dto.RoleId);
            throw new InvalidOperationException($"No se encontró el rol con ID {dto.RoleId}");
        }

        // Actualizar las propiedades de la entidad existente
        existingEntity.Name = dto.Name;
        existingEntity.Email = dto.Email;
        existingEntity.EmployeeNumber = dto.EmployeeNumber;
        existingEntity.RoleId = dto.RoleId;
        existingEntity.Updated = DateTime.Now;
        existingEntity.UpdatedBy = updatedBy;

        // Actualizar la entidad en el repositorio
        var updatedEntity = await repository.UpdateAsync(existingEntity);
        var result = MapToDto(updatedEntity);

        Logger.Info("Usuario actualizado exitosamente con ID: {Id} por usuario: {User}", result.Id, updatedBy);

        return result;
    }

    public async Task<bool> ChangePasswordAsync(ChangePasswordDto dto)
    {
        Logger.Debug("Cambiando contraseña para usuario ID: {UserId}", dto.UserId);

        var entity = await repository.GetByIdAsync(dto.UserId);
        if (entity == null)
        {
            Logger.Warn("Intento de cambiar contraseña para usuario inexistente con ID: {Id}", dto.UserId);
            throw new InvalidOperationException($"No se encontró el usuario con ID {dto.UserId}");
        }

        // Verificar que la contraseña actual sea correcta
        if (!passwordService.VerifyPassword(dto.CurrentPassword, entity.Password))
        {
            Logger.Warn("Intento de cambiar contraseña con contraseña actual incorrecta para usuario ID: {Id}",
                dto.UserId);
            throw new InvalidOperationException("La contraseña actual es incorrecta");
        }

        // Actualizar la contraseña con hash
        entity.Password = passwordService.HashPassword(dto.NewPassword);
        entity.Updated = DateTime.Now;
        entity.UpdatedBy = "Usuario"; // En un sistema real, obtener del contexto de autenticación

        await repository.UpdateAsync(entity);

        Logger.Info("Contraseña cambiada exitosamente para usuario ID: {Id}", dto.UserId);

        return true;
    }

    public async Task<bool> AdminChangePasswordAsync(AdminChangePasswordDto dto, string updatedBy)
    {
        Logger.Debug("Administrador cambiando contraseña para usuario ID: {UserId}, UpdatedBy: {User}", dto.UserId,
            updatedBy);

        var entity = await repository.GetByIdAsync(dto.UserId);
        if (entity == null)
        {
            Logger.Warn("Intento de administrador de cambiar contraseña para usuario inexistente con ID: {Id}",
                dto.UserId);
            throw new InvalidOperationException($"No se encontró el usuario con ID {dto.UserId}");
        }

        // Actualizar la contraseña con hash (sin verificar contraseña actual)
        entity.Password = passwordService.HashPassword(dto.NewPassword);
        entity.Updated = DateTime.Now;
        entity.UpdatedBy = updatedBy;

        await repository.UpdateAsync(entity);

        Logger.Info("Contraseña cambiada por administrador exitosamente para usuario ID: {Id} por usuario: {User}",
            dto.UserId, updatedBy);

        return true;
    }

    public async Task<User?> GetUserEntityByEmailAsync(string email, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo entidad de usuario por email: {Email}, IncludeDeleted: {IncludeDeleted}", email,
            includeDeleted);

        var entity = await repository.GetByEmailExactAsync(email, includeDeleted);
        var result = entity;

        Logger.Debug("Entidad de usuario por email obtenida: {Found} para '{Email}'", result != null, email);

        return result;
    }

    public async Task<User?> GetUserEntityByIdAsync(int id, bool includeDeleted = false)
    {
        Logger.Debug("Obteniendo entidad de usuario por ID: {Id}, IncludeDeleted: {IncludeDeleted}", id,
            includeDeleted);

        var entity = await repository.GetByIdAsync(id, includeDeleted);
        var result = entity;

        Logger.Debug("Entidad de usuario por ID obtenida: {Found} para ID: {Id}", result != null, id);

        return result;
    }

    public async Task<User?> GetUserEntityByEmployeeNumberAsync(string employeeNumber, bool includeDeleted = false)
    {
        Logger.Debug(
            "Obteniendo entidad de usuario por número de empleado: {EmployeeNumber}, IncludeDeleted: {IncludeDeleted}",
            employeeNumber, includeDeleted);

        var entity = await repository.GetByEmployeeNumberExactAsync(employeeNumber, includeDeleted);
        var result = entity;

        Logger.Debug("Entidad de usuario por número de empleado obtenida: {Found} para '{EmployeeNumber}'",
            result != null, employeeNumber);

        return result;
    }

    protected override User MapToEntity(CreateUserDto dto)
    {
        return new User
        {
            Name = dto.Name,
            Email = dto.Email,
            EmployeeNumber = dto.EmployeeNumber,
            Password = passwordService.HashPassword(dto.Password), // Hashear la contraseña
            RoleId = dto.RoleId
        };
    }

    protected override User MapToEntity(UpdateUserDto dto)
    {
        // Este método ya no se usa para actualización, pero se mantiene por compatibilidad
        // La actualización ahora se hace directamente en la entidad existente
        return new User
        {
            Id = dto.Id,
            Name = dto.Name,
            EmployeeNumber = dto.EmployeeNumber,
            Email = dto.Email,
            RoleId = dto.RoleId
        };
    }

    protected override UserDto MapToDto(User entity)
    {
        return new UserDto
        {
            Id = entity.Id,
            Name = entity.Name,
            EmployeeNumber = entity.EmployeeNumber,
            Email = entity.Email,
            RoleId = entity.RoleId,
            RoleName = entity.Role?.Name ?? "Sin rol",
            Created = entity.Created,
            CreatedString = entity.Created.ToString(DateFormats.SHORT_DATE_TIME),
            CreatedBy = entity.CreatedBy,
            IsDeleted = entity.IsDeleted,
            Deleted = entity.Deleted,
            DeletedString = entity.Deleted?.ToString(DateFormats.SHORT_DATE_TIME),
            DeletedBy = entity.DeletedBy,
            Updated = entity.Updated,
            UpdatedString = entity.Updated?.ToString(DateFormats.SHORT_DATE_TIME),
            UpdatedBy = entity.UpdatedBy,
            Active = entity.Active
        };
    }
}