﻿namespace BitacoraResiduosESH.Backend.Backend.Infrastructure.Data;

public class AppDbContext(DbContextOptions<AppDbContext> options) : DbContext(options)
{
    private static readonly ILogger Logger = LogManager.GetCurrentClassLogger();

    public DbSet<Role> Roles { get; set; }
    public DbSet<User> User { get; set; }
    public DbSet<Area> Areas { get; set; }
    public DbSet<ContainerType> ContainerTypes { get; set; }
    public DbSet<WasteType> WasteTypes { get; set; }
    public DbSet<BitacoraEntry> BitacoraEntries { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        Logger.Debug("Configurando modelo de Entity Framework");

        // Aplicar configuraciones de entidades
        modelBuilder.ApplyConfiguration(new RoleConfiguration());
        modelBuilder.ApplyConfiguration(new UserConfiguration());
        modelBuilder.ApplyConfiguration(new AreaConfiguration());
        modelBuilder.ApplyConfiguration(new WasteTypeConfiguration());
        modelBuilder.ApplyConfiguration(new ContainerTypeConfiguration());
        modelBuilder.ApplyConfiguration(new BitacoraEntryConfiguration());

        Logger.Debug("Modelo de Entity Framework configurado exitosamente");
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        // Configuraciones específicas para SQLite
        if (!optionsBuilder.IsConfigured)
        {
            // Esta configuración solo se aplica si no se ha configurado desde el Program.cs
            optionsBuilder.UseSqlite("Data Source=app.db");
            Logger.Info("Configuración de SQLite aplicada desde OnConfiguring");
        }

        // Habilitar logging de consultas SQL usando NLog
    }

    public async Task<int> SaveChangesAsync(
        bool acceptAllChangesOnSuccess,
        string username = "",
        CancellationToken cancellationToken = default)
    {
        Logger.Debug("Iniciando SaveChangesAsync para usuario: {Username}", username);

        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is BaseEntity &&
                        (e.State == EntityState.Added
                         || e.State == EntityState.Modified
                         || e.State == EntityState.Deleted));

        var currentTime = DateTime.Now;
        var modifiedCount = entries.Count();

        Logger.Debug("Procesando {Count} entidades modificadas", modifiedCount);

        foreach (var entry in entries)
        {
            var entity = (BaseEntity)entry.Entity;
            var entityType = entity.GetType().Name;

            switch (entry.State)
            {
                case EntityState.Added:
                    entity.Created = currentTime;
                    entity.CreatedBy = username;
                    entity.Active = true;
                    entity.IsDeleted = false;
                    Logger.Debug("Entidad {EntityType} marcada como creada por {Username}", entityType, username);
                    break;

                case EntityState.Modified:
                    entity.Updated = currentTime;
                    entity.UpdatedBy = username;
                    Logger.Debug("Entidad {EntityType} marcada como actualizada por {Username}", entityType, username);
                    break;

                case EntityState.Deleted:
                    entry.State = EntityState.Modified;
                    entity.IsDeleted = true;
                    entity.Deleted = currentTime;
                    entity.DeletedBy = username;
                    entity.Active = false;
                    Logger.Debug("Entidad {EntityType} marcada como eliminada por {Username}", entityType, username);
                    break;
            }
        }

        var result = await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        Logger.Debug("SaveChangesAsync completado. {Count} entidades guardadas", result);

        return result;
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        Logger.Warn(
            "SaveChangesAsync(CancellationToken) llamado sin username. Use SaveChangesAsync(bool, string, CancellationToken) en su lugar.");
        throw new Exception(
            "Use SaveChangesAsync(bool acceptAllChangesOnSuccess, string username = \"\", CancellationToken cancellationToken = default) instead.");
    }

    public override int SaveChanges()
    {
        Logger.Warn(
            "SaveChanges() llamado sin username. Use SaveChangesAsync(bool, string, CancellationToken) en su lugar.");
        throw new Exception(
            "Use SaveChangesAsync(bool acceptAllChangesOnSuccess, string username = \"\", CancellationToken cancellationToken = default) instead.");
    }
}