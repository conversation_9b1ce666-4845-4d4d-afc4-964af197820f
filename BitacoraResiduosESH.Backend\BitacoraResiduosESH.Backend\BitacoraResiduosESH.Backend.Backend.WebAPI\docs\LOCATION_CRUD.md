# CRUD de Ubicaciones (Location)

## Descripción General

El sistema de gestión de ubicaciones permite crea<PERSON>, leer, actualizar y eliminar ubicaciones que están relacionadas con tipos de ubicación. Cada ubicación debe estar asociada a un tipo de ubicación válido y activo.

## Modelo de Datos

### Entidad Location
- **Id** (int): Identificador único
- **Name** (string): Nombre de la ubicación (requerido, máx. 200 caracteres)
- **Description** (string): Descripción opcional (máx. 1000 caracteres)
- **LocationTypeId** (int): ID del tipo de ubicación (requerido)
- **LocationType** (navegación): Referencia al tipo de ubicación
- **Active** (bool): Estado activo/inactivo
- **Campos de auditoría**: CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsDeleted, etc.

### Relaciones
- **LocationType**: Relación muchos a uno (muchas ubicaciones pertenecen a un tipo)
- Restricción: No se puede eliminar un LocationType si tiene Location asociadas

## Endpoints Disponibles

### Endpoints Básicos (Heredados de SimpleEntityController)

#### 1. Obtener Todas las Ubicaciones
```http
GET /api/location?pageNumber=1&pageSize=10&includeDeleted=false
```
- **Descripción**: Obtiene una lista paginada de ubicaciones
- **Parámetros**:
  - `pageNumber`: Número de página (opcional, default: 1)
  - `pageSize`: Tamaño de página (opcional, default: 10)
  - `includeDeleted`: Incluir eliminadas (opcional, default: false)

#### 2. Obtener Ubicaciones Activas
```http
GET /api/location/active?pageNumber=1&pageSize=10
```
- **Descripción**: Obtiene solo las ubicaciones activas

#### 3. Obtener Ubicación por ID
```http
GET /api/location/{id}?includeDeleted=false
```
- **Descripción**: Obtiene una ubicación específica por su ID

#### 4. Crear Nueva Ubicación
```http
POST /api/location
Content-Type: application/json

{
  "name": "Almacén Principal",
  "description": "Almacén principal para herramientas",
  "locationTypeId": 1
}
```
- **Validaciones**:
  - `name`: Requerido, máximo 200 caracteres
  - `locationTypeId`: Requerido, debe existir y estar activo

#### 5. Actualizar Ubicación
```http
PUT /api/location/{id}
Content-Type: application/json

{
  "name": "Almacén Principal Actualizado",
  "description": "Descripción actualizada",
  "locationTypeId": 1
}
```

#### 6. Eliminar Ubicación (Soft Delete)
```http
DELETE /api/location/{id}
```

#### 7. Activar/Desactivar Ubicación
```http
PATCH /api/location/{id}/activate
PATCH /api/location/{id}/deactivate
```

#### 8. Verificar Existencia
```http
GET /api/location/{id}/exists?includeDeleted=false
```

#### 9. Búsquedas
```http
GET /api/location/search?name={searchTerm}
GET /api/location/search?description={searchTerm}
```

### Endpoints Específicos de Location

#### 10. Obtener Ubicaciones por Tipo
```http
GET /api/location/by-location-type/{locationTypeId}?includeDeleted=false
```
- **Descripción**: Obtiene todas las ubicaciones de un tipo específico
- **Uso**: Útil para filtrar ubicaciones por tipo

## Reglas de Negocio

### Validaciones
1. **Nombre requerido**: No puede estar vacío
2. **LocationType válido**: Debe existir y estar activo
3. **Unicidad**: Nombre único por tipo de ubicación
4. **Eliminación**: Solo soft delete para mantener historial

### Comportamientos Especiales
1. **Creación**: Se valida que el LocationType exista y esté activo
2. **Actualización**: Se permite cambiar el tipo si el nuevo tipo es válido
3. **Eliminación**: Soft delete, se marca como eliminada pero se mantiene en BD
4. **Búsquedas**: Incluyen la información del tipo de ubicación

## Ejemplos de Uso

### Caso 1: Crear Ubicación de Almacén
```json
POST /api/location
{
  "name": "Almacén A-001",
  "description": "Almacén principal del área A",
  "locationTypeId": 1
}
```

### Caso 2: Crear Ubicación de Producción
```json
POST /api/location
{
  "name": "Línea Producción 1",
  "description": "Primera línea de ensamble",
  "locationTypeId": 2
}
```

### Caso 3: Búsqueda por Tipo
```http
GET /api/location/by-location-type/1
```
Retorna todas las ubicaciones del tipo "Almacén Principal"

## Respuestas de API

### Respuesta Exitosa (GET)
```json
{
  "data": [
    {
      "id": 1,
      "name": "Almacén A-001",
      "description": "Almacén principal del área A",
      "locationTypeId": 1,
      "locationTypeName": "Almacén Principal",
      "active": true,
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": null,
      "createdBy": "admin",
      "updatedBy": null
    }
  ],
  "pageNumber": 1,
  "pageSize": 10,
  "totalRecords": 1,
  "totalPages": 1
}
```

### Respuesta de Error
```json
{
  "error": "El tipo de ubicación con ID 999 no existe."
}
```

## Casos de Error Comunes

1. **LocationType inexistente**:
   ```
   Error 400: "El tipo de ubicación con ID {id} no existe."
   ```

2. **LocationType inactivo**:
   ```
   Error 400: "El tipo de ubicación con ID {id} no está activo."
   ```

3. **Nombre duplicado**:
   ```
   Error 409: "Ya existe una ubicación con ese nombre para el tipo especificado."
   ```

4. **Ubicación no encontrada**:
   ```
   Error 404: "La ubicación con ID {id} no fue encontrada."
   ```

## Notas de Implementación

- Todas las consultas incluyen automáticamente la información del LocationType
- Se utilizan índices para optimizar búsquedas por nombre y tipo
- Las relaciones están configuradas con `DeleteBehavior.Restrict`
- El sistema mantiene auditoría completa de cambios
- Se utiliza paginación en todas las consultas de listado 