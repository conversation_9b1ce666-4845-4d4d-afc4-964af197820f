// Tipos para autenticación
export interface LoginRequest {
    email?: string;
    employeeNumber?: string;
    password: string;
}

export interface UserAuthInfo {
    id: number;
    name: string;
    email: string;
    employeeNumber: string;
    role: string;
}

export interface AuthResponse {
    token: string;
    tokenType: string;
    expiresIn: number;
    user: UserAuthInfo;
    message: string;
}

export interface RefreshTokenRequest {
    refreshToken: string;
}

export interface ValidateTokenRequest {
    token: string;
}

// Tipos para el store de autenticación
export interface AuthState {
    // Estado de autenticación
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;

    // Datos del usuario
    user: UserAuthInfo | null;
    token: string | null;
    tokenExpiry: Date | null;

    // Acciones
    login: (credentials: LoginRequest) => Promise<boolean>;
    logout: () => void;
    refreshToken: () => Promise<boolean>;
    validateToken: () => Promise<boolean>;
    clearError: () => void;

    // Utilidades
    hasRole: (role: string | string[]) => boolean;
    isTokenExpired: () => boolean;
    getTokenExpiryTime: () => number;
}

// Tipos para roles y permisos
export interface Role {
    name: string;
    permissions: string[];
}

export interface Permission {
    action: string;
    resource: string;
}

// Constantes para roles
export const ROLES = {
    ADMIN: 'Admin',
    USER: 'User',
    MANAGER: 'Manager',
    OPERATOR: 'Operator'
} as const;

export type UserRole = typeof ROLES[keyof typeof ROLES];

// Constantes para permisos
export const PERMISSIONS = {
    // Usuarios
    USER_CREATE: 'user:create',
    USER_READ: 'user:read',
    USER_UPDATE: 'user:update',
    USER_DELETE: 'user:delete',

    // Roles
    ROLE_CREATE: 'role:create',
    ROLE_READ: 'role:read',
    ROLE_UPDATE: 'role:update',
    ROLE_DELETE: 'role:delete',

    // Entidades
    ENTITY_CREATE: 'entity:create',
    ENTITY_READ: 'entity:read',
    ENTITY_UPDATE: 'entity:update',
    ENTITY_DELETE: 'entity:delete',

    // Sistema
    SYSTEM_ADMIN: 'system:admin',
    SYSTEM_CONFIG: 'system:config'
} as const;

export type PermissionType = typeof PERMISSIONS[keyof typeof PERMISSIONS]; 