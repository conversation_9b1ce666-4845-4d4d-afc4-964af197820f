using BitacoraResiduosESH.Backend.Backend.Application.Common;
using BitacoraResiduosESH.Backend.Backend.Application.DTOs.User;

namespace BitacoraResiduosESH.Backend.Backend.Application.Interfaces.Services;

public interface IUserService : IGenericService<User, UserDto, UserFilterDto, CreateUserDto, UpdateUserDto>
{
    Task<PagedResponse<UserDto>> GetByNameAsync(string name, PaginationFilter filter, bool includeDeleted = false);
    Task<PagedResponse<UserDto>> GetByEmailAsync(string email, PaginationFilter filter, bool includeDeleted = false);
    Task<UserDto?> GetByEmailExactAsync(string email, bool includeDeleted = false);
    Task<UserDto?> GetByEmployeeNumberExactAsync(string employeeNumber, bool includeDeleted = false);
    Task<bool> ExistsByEmailAsync(string email, int? excludeId = null, bool includeDeleted = false);
    Task<bool> ExistsByEmployeeNumberAsync(string employeeNumber, int? excludeId = null, bool includeDeleted = false);
    new Task<UserDto> CreateAsync(CreateUserDto dto, string createdBy);
    new Task<UserDto> UpdateAsync(int id, UpdateUserDto dto, string updatedBy);

    // Métodos para cambio de contraseña
    Task<bool> ChangePasswordAsync(ChangePasswordDto dto);
    Task<bool> AdminChangePasswordAsync(AdminChangePasswordDto dto, string updatedBy);

    // Métodos para autenticación (retornan entidad completa)
    Task<User?> GetUserEntityByEmailAsync(string email, bool includeDeleted = false);
    Task<User?> GetUserEntityByIdAsync(int id, bool includeDeleted = false);
    Task<User?> GetUserEntityByEmployeeNumberAsync(string employeeNumber, bool includeDeleted = false);
}