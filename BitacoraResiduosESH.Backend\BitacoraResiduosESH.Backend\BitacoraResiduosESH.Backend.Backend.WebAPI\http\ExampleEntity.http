@baseUrl = https://localhost:7001
@contentType = application/json

### Variables de entorno
@exampleId = 1
@categoryId = 1

### ========================================
### OPERACIONES DE LECTURA
### ========================================

### Obtener elemento por ID
GET {{baseUrl}}/api/example/{{exampleId}}
Content-Type: {{contentType}}

### Obtener elemento por ID (incluir eliminados)
GET {{baseUrl}}/api/example/{{exampleId}}?includeDeleted=true
Content-Type: {{contentType}}

### Obtener todos los elementos
GET {{baseUrl}}/api/example
Content-Type: {{contentType}}

### Obtener todos los elementos (incluir eliminados)
GET {{baseUrl}}/api/example?includeDeleted=true
Content-Type: {{contentType}}

### Obtener elementos paginados
GET {{baseUrl}}/api/example/paged?pageNumber=1&pageSize=10
Content-Type: {{contentType}}

### Obtener elementos paginados con filtros
GET {{baseUrl}}/api/example/paged?pageNumber=1&pageSize=10&name=ejemplo&active=true
Content-Type: {{contentType}}

### Obtener solo elementos activos
GET {{baseUrl}}/api/example/active
Content-Type: {{contentType}}

### Buscar elementos por nombre
GET {{baseUrl}}/api/example/search/name?name=ejemplo
Content-Type: {{contentType}}

### Buscar elementos por descripción
GET {{baseUrl}}/api/example/search/description?description=descripción
Content-Type: {{contentType}}

### Obtener elementos por categoría
GET {{baseUrl}}/api/example/category/{{categoryId}}
Content-Type: {{contentType}}

### Verificar si existe un elemento
GET {{baseUrl}}/api/example/{{exampleId}}/exists
Content-Type: {{contentType}}

### ========================================
### OPERACIONES DE ESCRITURA
### ========================================

### Crear nuevo elemento
POST {{baseUrl}}/api/example
Content-Type: {{contentType}}

{
  "name": "Nuevo Ejemplo",
  "description": "Descripción del nuevo ejemplo",
  "categoryId": {{categoryId}}
}

### Crear elemento sin categoría
POST {{baseUrl}}/api/example
Content-Type: {{contentType}}

{
  "name": "Ejemplo Sin Categoría",
  "description": "Este ejemplo no tiene categoría asignada"
}

### Actualizar elemento existente
PUT {{baseUrl}}/api/example/{{exampleId}}
Content-Type: {{contentType}}

{
  "name": "Ejemplo Actualizado",
  "description": "Descripción actualizada del ejemplo",
  "categoryId": {{categoryId}}
}

### ========================================
### OPERACIONES DE ELIMINACIÓN
### ========================================

### Eliminar elemento (soft delete)
DELETE {{baseUrl}}/api/example/{{exampleId}}
Content-Type: {{contentType}}

### Eliminar elemento permanentemente (hard delete)
DELETE {{baseUrl}}/api/example/{{exampleId}}/permanent
Content-Type: {{contentType}}

### ========================================
### OPERACIONES DE ACTIVACIÓN/DESACTIVACIÓN
### ========================================

### Activar elemento
PATCH {{baseUrl}}/api/example/{{exampleId}}/activate
Content-Type: {{contentType}}

### Desactivar elemento
PATCH {{baseUrl}}/api/example/{{exampleId}}/deactivate
Content-Type: {{contentType}}

### ========================================
### EJEMPLOS DE VALIDACIÓN
### ========================================

### Intentar crear elemento con nombre duplicado (debería fallar)
POST {{baseUrl}}/api/example
Content-Type: {{contentType}}

{
  "name": "Nuevo Ejemplo",
  "description": "Este debería fallar si ya existe un elemento con el mismo nombre"
}

### Intentar actualizar elemento inexistente (debería fallar)
PUT {{baseUrl}}/api/example/999
Content-Type: {{contentType}}

{
  "name": "Elemento Inexistente",
  "description": "Este debería fallar porque el ID no existe"
}

### Intentar eliminar elemento inexistente (debería fallar)
DELETE {{baseUrl}}/api/example/999
Content-Type: {{contentType}}

### ========================================
### EJEMPLOS DE BÚSQUEDA AVANZADA
### ========================================

### Buscar elementos con múltiples filtros
GET {{baseUrl}}/api/example/paged?pageNumber=1&pageSize=20&name=ejemplo&active=true&includeDeleted=false
Content-Type: {{contentType}}

### Buscar elementos por categoría específica
GET {{baseUrl}}/api/example/category/{{categoryId}}?includeDeleted=false
Content-Type: {{contentType}}

### Obtener elementos eliminados
GET {{baseUrl}}/api/example?includeDeleted=true
Content-Type: {{contentType}}

### ========================================
### EJEMPLOS DE WORKFLOW COMPLETO
### ========================================

### 1. Crear un nuevo elemento
# @name createExample
POST {{baseUrl}}/api/example
Content-Type: {{contentType}}

{
  "name": "Elemento de Prueba",
  "description": "Este es un elemento de prueba para el workflow",
  "categoryId": {{categoryId}}
}

### 2. Obtener el elemento creado (usar el ID de la respuesta anterior)
# @name getCreatedExample
GET {{baseUrl}}/api/example/{{createExample.response.body.id}}
Content-Type: {{contentType}}

### 3. Actualizar el elemento
PUT {{baseUrl}}/api/example/{{createExample.response.body.id}}
Content-Type: {{contentType}}

{
  "name": "Elemento de Prueba Actualizado",
  "description": "Descripción actualizada del elemento de prueba",
  "categoryId": {{categoryId}}
}

### 4. Desactivar el elemento
PATCH {{baseUrl}}/api/example/{{createExample.response.body.id}}/deactivate
Content-Type: {{contentType}}

### 5. Verificar que está desactivado
GET {{baseUrl}}/api/example/{{createExample.response.body.id}}
Content-Type: {{contentType}}

### 6. Reactivar el elemento
PATCH {{baseUrl}}/api/example/{{createExample.response.body.id}}/activate
Content-Type: {{contentType}}

### 7. Eliminar el elemento
DELETE {{baseUrl}}/api/example/{{createExample.response.body.id}}
Content-Type: {{contentType}}

### 8. Verificar que está eliminado (no debería aparecer)
GET {{baseUrl}}/api/example/{{createExample.response.body.id}}
Content-Type: {{contentType}}

### 9. Verificar que está eliminado (incluyendo eliminados)
GET {{baseUrl}}/api/example/{{createExample.response.body.id}}?includeDeleted=true
Content-Type: {{contentType}} 