using BitacoraResiduosESH.Backend.Backend.Domain.Entities.OpenIdConnect;

namespace BitacoraResiduosESH.Backend.Backend.Domain.Interfaces.Services;

/// <summary>
///     Interfaz para el servicio de OpenID Connect
/// </summary>
public interface IOpenIdConnectService
{
    /// <summary>
    ///     Obtiene la URL de autorización para iniciar el flujo de autenticación
    /// </summary>
    /// <param name="state">Estado para prevenir ataques CSRF</param>
    /// <param name="scope">Alcance de permisos solicitados</param>
    /// <returns>URL de autorización</returns>
    string GetAuthorizationUrl(string state, string scope = "openid profile email");

    /// <summary>
    ///     Intercambia el código de autorización por un token de acceso
    ///     Siguiendo la especificación OAuth 2.0 RFC6749 Section 4.1.3
    /// </summary>
    /// <param name="authorizationCode">Código de autorización recibido</param>
    /// <returns>Respuesta con el token de acceso</returns>
    Task<TokenResponse?> ExchangeCodeForTokenAsync(string authorizationCode);

    /// <summary>
    ///     Obtiene información del usuario usando el token de acceso
    /// </summary>
    /// <param name="accessToken">Token de acceso válido</param>
    /// <returns>Información del usuario</returns>
    Task<UserInfo?> GetUserInfoAsync(string accessToken);

    /// <summary>
    ///     Refresca el token de acceso usando el refresh token
    /// </summary>
    /// <param name="refreshToken">Token de refresco</param>
    /// <returns>Nueva respuesta con tokens</returns>
    Task<TokenResponse?> RefreshTokenAsync(string refreshToken);

    /// <summary>
    ///     Revoca el token de acceso
    /// </summary>
    /// <param name="token">Token a revocar</param>
    /// <param name="tokenTypeHint">Tipo de token (access_token o refresh_token)</param>
    /// <returns>True si se revocó exitosamente</returns>
    Task<bool> RevokeTokenAsync(string token, string tokenTypeHint = "access_token");
}