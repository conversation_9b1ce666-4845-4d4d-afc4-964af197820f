import React, {useCallback, useState} from 'react';
import {Search, X} from 'lucide-react';

export interface FuzzySearchProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    className?: string;
    debounceMs?: number;
}

const FuzzySearch: React.FC<FuzzySearchProps> = ({
                                                     value,
                                                     onChange,
                                                     placeholder = 'Buscar...',
                                                     className = '',
                                                     debounceMs = 300,
                                                 }) => {
    const [inputValue, setInputValue] = useState(value);
    const [debounceTimeout, setDebounceTimeout] = useState<number | null>(null);

    const handleInputChange = useCallback((newValue: string) => {
        setInputValue(newValue);

        // Limpiar timeout anterior
        if (debounceTimeout) {
            clearTimeout(debounceTimeout);
        }

        // Crear nuevo timeout
        const timeout = setTimeout(() => {
            onChange(newValue);
        }, debounceMs);

        setDebounceTimeout(timeout);
    }, [onChange, debounceMs, debounceTimeout]);

    const handleClear = useCallback(() => {
        setInputValue('');
        onChange('');
    }, [onChange]);

    return (
        <div className={`relative ${className}`}>
            <div className="relative">
                <Search
                    className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
                    style={{color: 'var(--text-secondary)'}}
                />
                <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => handleInputChange(e.target.value)}
                    placeholder={placeholder}
                    className="w-full pl-10 pr-10 py-2 border rounded-lg text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    style={{
                        backgroundColor: 'var(--card-bg)',
                        borderColor: 'var(--border-color)',
                        color: 'var(--text-primary)',
                    }}
                />
                {inputValue && (
                    <button
                        onClick={handleClear}
                        className="absolute right-3 top-1/2 -translate-y-1/2 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        style={{color: 'var(--text-secondary)'}}
                    >
                        <X className="h-4 w-4"/>
                    </button>
                )}
            </div>
        </div>
    );
};

export default FuzzySearch; 