namespace BitacoraResiduosESH.Backend.Backend.Application.DTOs.BaseEntity;

public class BaseEntityDto
{
    public int Id { get; set; }
    public DateTime Created { get; set; }
    public string CreatedString { get; set; } = string.Empty;
    public string CreatedBy { get; set; } = string.Empty;
    public bool IsDeleted { get; set; }
    public DateTime? Deleted { get; set; }
    public string? DeletedString { get; set; }
    public string? DeletedBy { get; set; }
    public DateTime? Updated { get; set; }
    public string? UpdatedString { get; set; }
    public string? UpdatedBy { get; set; }
    public bool Active { get; set; }
}