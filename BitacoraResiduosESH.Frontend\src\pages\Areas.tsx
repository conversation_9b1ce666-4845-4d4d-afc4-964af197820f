import React, { useState } from 'react';
import { SimpleEntityTable } from '../components/table/SimpleEntityTable';
import { useAreaCrud } from '../hooks/useAreaCrud';
import type { Area, CreateAreaDto, UpdateAreaDto } from '../types/area';

const Areas: React.FC = () => {
    // Estados para paginación y búsqueda
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    // Hook para operaciones CRUD
    const {
        useGetAll,
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,
    } = useAreaCrud();

    // Queries
    const { data: areasData, isLoading } = useGetAll(pageNumber, pageSize, false);

    // Mutations
    const createAreaMutation = useCreate();
    const updateAreaMutation = useUpdate();
    const deleteAreaMutation = useDelete();
    const activateAreaMutation = useActivate();
    const deactivateAreaMutation = useDeactivate();

    // Handlers
    const handleCreate = async (data: CreateAreaDto) => {
        await createAreaMutation.mutateAsync(data);
    };

    const handleUpdate = async (id: number, data: UpdateAreaDto) => {
        await updateAreaMutation.mutateAsync({ id, data });
    };

    const handleDelete = async (id: number) => {
        await deleteAreaMutation.mutateAsync(id);
    };

    const handleActivate = async (id: number) => {
        await activateAreaMutation.mutateAsync(id);
    };

    const handleDeactivate = async (id: number) => {
        await deactivateAreaMutation.mutateAsync(id);
    };

    const handleSearch = () => {
        setPageNumber(1); // Reset to first page when searching
    };

    const handlePageChange = (page: number) => {
        setPageNumber(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setPageNumber(1); // Reset to first page when changing page size
    };

    // Renderizar campos del formulario
    const renderFormFields = (data?: Area) => (
        <div className="space-y-4">
            <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                    Nombre *
                </label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    defaultValue={data?.name || ''}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ingrese el nombre del área"
                />
            </div>

            <div>
                <label htmlFor="description" className="block text-sm font-medium mb-1">
                    Descripción
                </label>
                <textarea
                    id="description"
                    name="description"
                    defaultValue={data?.description || ''}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ingrese una descripción del área"
                />
            </div>
        </div>
    );

    // Renderizar detalles del área
    const renderDetails = (area: Area) => (
        <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">ID</label>
                    <p className="text-sm">{area.id}</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Estado</label>
                    <p className="text-sm">
                        <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${area.active
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                                }`}
                        >
                            {area.active ? 'Activo' : 'Inactivo'}
                        </span>
                    </p>
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Nombre</label>
                <p className="text-sm">{area.name}</p>
            </div>

            <div>
                <label className="block text-sm font-medium text-gray-500">Descripción</label>
                <p className="text-sm">{area.description || 'Sin descripción'}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label className="block text-sm font-medium text-gray-500">Creado por</label>
                    <p className="text-sm">{area.createdBy}</p>
                </div>
                <div>
                    <label className="block text-sm font-medium text-gray-500">Fecha de creación</label>
                    <p className="text-sm">{area.createdLocalString}</p>
                </div>
            </div>

            {area.updatedBy && (
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Actualizado por</label>
                        <p className="text-sm">{area.updatedBy}</p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Fecha de actualización</label>
                        <p className="text-sm">{area.updatedLocalString}</p>
                    </div>
                </div>
            )}

            {area.isDeleted && (
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Eliminado por</label>
                        <p className="text-sm">{area.deletedBy}</p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-500">Fecha de eliminación</label>
                        <p className="text-sm">{area.deletedLocalString}</p>
                    </div>
                </div>
            )}
        </div>
    );

    return (
        <div className="p-6">
            <SimpleEntityTable
                data={areasData?.data || []}
                isLoading={isLoading}
                totalRecords={areasData?.totalRecords || 0}
                pageNumber={pageNumber}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                onSearch={handleSearch}
                onCreate={handleCreate}
                onUpdate={handleUpdate}
                onDelete={handleDelete}
                onActivate={handleActivate}
                onDeactivate={handleDeactivate}
                entityName="Área"
                entityNamePlural="Áreas"
                searchPlaceholder="Buscar áreas..."
                showActiveToggle={true}
                renderFormFields={renderFormFields}
                renderDetails={renderDetails}
            />
        </div>
    );
};

export default Areas;