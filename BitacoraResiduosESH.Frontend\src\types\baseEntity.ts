// Interfaces genéricas para entidades BaseEntity
export interface BaseEntity {
    id: number;
    created: string;
    createdString: string;
    createdBy: string;
    updated?: string;
    updatedString?: string;
    updatedBy?: string;
    isDeleted: boolean;
    deleted?: string;
    deletedString?: string;
    deletedBy?: string;
    active: boolean;

    // Propiedades para fechas locales
    createdLocal: string;
    createdLocalString: string;
    updatedLocal?: string;
    updatedLocalString?: string;
    deletedLocal?: string;
    deletedLocalString?: string;
}

export interface CreateBaseEntityDto {
    active?: boolean;
}

export interface UpdateBaseEntityDto {
    id: number;
    active?: boolean;
}

export interface BaseEntityFilterDto {
    pageNumber: number;
    pageSize: number;
    includeDeleted: boolean;
}
