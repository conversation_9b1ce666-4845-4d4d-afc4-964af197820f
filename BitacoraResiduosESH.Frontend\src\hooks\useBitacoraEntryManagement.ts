import { useState } from 'react';
import { type SearchableSelectOption } from '@/components/ui';
import type { BitacoraEntry, CreateBitacoraEntryDto, UpdateBitacoraEntryDto } from '@/types/bitacoraEntry';
import { useBitacoraEntryCrud } from './useBitacoraEntryCrud';
import { useAreaCrud } from './useAreaCrud';
import { useWasteTypeCrud } from './useWasteTypeCrud';
import { useContainerTypeCrud } from './useContainerTypeCrud';

export const useBitacoraEntryManagement = () => {
    // Estados para paginación y búsqueda
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    // Estados para los selects de búsqueda
    const [areaSearchTerm, setAreaSearchTerm] = useState('');
    const [wasteTypeSearchTerm, setWasteTypeSearchTerm] = useState('');
    const [containerTypeSearchTerm, setContainerTypeSearchTerm] = useState('');

    // Hook para operaciones CRUD de BitacoraEntry
    const {
        useGetAll,
        useCreate,
        useUpdate,
        useDelete,
        useActivate,
        useDeactivate,
    } = useBitacoraEntryCrud();

    // Hooks para obtener datos de entidades relacionadas
    const { useSearchByName: useSearchAreas } = useAreaCrud();
    const { useSearchByName: useSearchWasteTypes } = useWasteTypeCrud();
    const { useSearchByName: useSearchContainerTypes } = useContainerTypeCrud();

    // Queries principales
    const { data: entriesData, isLoading } = useGetAll(pageNumber, pageSize, false);

    // Queries para búsquedas de entidades relacionadas
    const { data: areasData } = useSearchAreas(areaSearchTerm || ' ', 1, 50, false);
    const { data: wasteTypesData } = useSearchWasteTypes(wasteTypeSearchTerm || ' ', 1, 50, false);
    const { data: containerTypesData } = useSearchContainerTypes(containerTypeSearchTerm || ' ', 1, 50, false);

    // Mutations
    const createMutation = useCreate();
    const updateMutation = useUpdate();
    const deleteMutation = useDelete();
    const activateMutation = useActivate();
    const deactivateMutation = useDeactivate();

    // Convertir datos a opciones para los selects
    const areaOptions: SearchableSelectOption[] = areasData?.data?.map(area => ({
        id: area.id,
        name: area.name,
        description: area.description
    })) || [];

    const wasteTypeOptions: SearchableSelectOption[] = wasteTypesData?.data?.map(wasteType => ({
        id: wasteType.id,
        name: wasteType.name,
        description: wasteType.description
    })) || [];

    const containerTypeOptions: SearchableSelectOption[] = containerTypesData?.data?.map(containerType => ({
        id: containerType.id,
        name: containerType.name,
        description: containerType.description
    })) || [];

    // Handlers para búsquedas
    const handleAreaSearch = (searchTerm: string) => {
        setAreaSearchTerm(searchTerm);
    };

    const handleWasteTypeSearch = (searchTerm: string) => {
        setWasteTypeSearchTerm(searchTerm);
    };

    const handleContainerTypeSearch = (searchTerm: string) => {
        setContainerTypeSearchTerm(searchTerm);
    };

    // Handlers para CRUD operations
    const handleCreate = async (data: CreateBitacoraEntryDto) => {
        await createMutation.mutateAsync(data);
    };

    const handleUpdate = async (id: number, data: UpdateBitacoraEntryDto) => {
        await updateMutation.mutateAsync({ id, data });
    };

    const handleDelete = async (id: number) => {
        await deleteMutation.mutateAsync(id);
    };

    const handleActivate = async (id: number) => {
        await activateMutation.mutateAsync(id);
    };

    const handleDeactivate = async (id: number) => {
        await deactivateMutation.mutateAsync(id);
    };

    const handlePageChange = (page: number) => {
        setPageNumber(page);
    };

    const handlePageSizeChange = (size: number) => {
        setPageSize(size);
        setPageNumber(1); // Reset to first page when changing page size
    };

    return {
        // Data
        entriesData,
        isLoading,
        areaOptions,
        wasteTypeOptions,
        containerTypeOptions,
        pageNumber,
        pageSize,

        // Handlers
        handleAreaSearch,
        handleWasteTypeSearch,
        handleContainerTypeSearch,
        handleCreate,
        handleUpdate,
        handleDelete,
        handleActivate,
        handleDeactivate,
        handlePageChange,
        handlePageSizeChange,
    };
};
